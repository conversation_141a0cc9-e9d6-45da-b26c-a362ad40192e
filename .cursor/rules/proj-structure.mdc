---
description: project structure
globs: 
alwaysApply: false
---
# Project Structure - Padel Community App

## Overview
This document provides a comprehensive overview of the project structure for our Ignite-based React Native application. Understanding this structure is crucial for maintaining consistency and finding the right place for new code.

## 📂 **Root Directory Structure**

```
PadelCommunityApp/
├── .cursor/                    # Cursor IDE configuration & rules
│   ├── coding-rules.mdc       # MANDATORY architectural patterns
│   ├── best-practices.mdc     # Code quality & optimization guidelines
│   ├── mobile-guide.mdc       # Mobile-specific implementation details
│   └── proj-structure.mdc     # This file - project structure overview
├── .expo/                     # Expo configuration & cache
├── .maestro/                  # End-to-end test flows
├── app/                       # 🏗️ MAIN APPLICATION CODE
├── assets/                    # Static assets (images, fonts, icons)
├── docs/                      # 📚 PROJECT DOCUMENTATION
├── ignite/                    # Ignite CLI templates
├── plugins/                   # Expo config plugins
├── test/                      # Test configuration & setup
├── types/                     # Global TypeScript type definitions
├── app.config.ts              # Expo app configuration
├── app.json                   # Expo app manifest
├── babel.config.js            # Babel transpiler configuration
├── eas.json                   # EAS Build configuration
├── jest.config.js             # Jest testing configuration
├── metro.config.js            # Metro bundler configuration
├── package.json               # Dependencies & scripts
├── tsconfig.json              # TypeScript configuration
└── README.md                  # Project overview & setup instructions
```

## 🏗️ **App Directory Structure** (Core Application)

```
app/
├── components/                # ✅ Reusable UI components
│   ├── AutoImage.tsx         # Optimized image component
│   ├── Button.tsx            # Themed button with presets
│   ├── Card.tsx              # Card container component
│   ├── CommunityCard.tsx     # Community card component
│   ├── CommunityCardLight.tsx # Lightweight community card
│   ├── EmptyState.tsx        # Empty state placeholder
│   ├── EventCard.tsx         # Event card component
│   ├── EventsList.tsx        # Events list component
│   ├── Header.tsx            # Navigation header
│   ├── Icon.tsx              # Vector icon system
│   ├── IconExample.tsx       # Icon usage example
│   ├── ListItem.tsx          # Generic list item component
│   ├── ListView.tsx          # List view container
│   ├── MapsSelectionModal.tsx # Maps selection modal
│   ├── NotificationItem.tsx  # Notification item component
│   ├── Popup.tsx             # Generic popup component
│   ├── Screen.tsx            # Screen container with safe areas
│   ├── SectionCard.tsx       # Section card component
│   ├── Text.tsx              # Themed text with presets
│   ├── TextField.tsx         # Form input component
│   ├── Toggle/               # Toggle components (checkbox, radio, switch)
│   │   ├── Checkbox.tsx      # Checkbox component
│   │   ├── Radio.tsx         # Radio button component
│   │   ├── Switch.tsx        # Switch component
│   │   ├── Toggle.tsx        # Base toggle component
│   │   └── index.ts          # Toggle exports
│   ├── TrophyCard.tsx        # Trophy card component
│   ├── VectorIcon.tsx        # Vector icon component
│   ├── VectorIconDemo.tsx    # Vector icon demo
│   ├── templates/            # Component templates for generation
│   │   └── IconComponentTemplate.tsx # Icon component template
│   └── index.ts              # Component exports
├── config/                   # 🔧 App configuration
│   ├── config.base.ts        # Base configuration
│   ├── config.dev.ts         # Development environment
│   ├── config.prod.ts        # Production environment
│   └── index.ts              # Configuration export
├── data/                     # 📊 Static data & mock data
│   ├── clubs.json            # Club sample data
│   ├── communities.json      # Community sample data
│   ├── events.json           # Event sample data
│   ├── notifications.json    # Notification sample data
│   └── user_data.json        # User sample data
├── devtools/                 # 🛠️ Development tools
│   ├── ReactotronClient.ts   # Reactotron debugging setup
│   └── ReactotronConfig.ts   # Reactotron configuration
├── hooks/                    # 🎣 Custom React hooks
│   └── useEvents.ts          # Event management hook
├── i18n/                     # 🌐 Internationalization
│   ├── ar.ts                 # Arabic translations
│   ├── en.ts                 # English translations (default)
│   ├── es.ts                 # Spanish translations
│   ├── fr.ts                 # French translations
│   ├── hi.ts                 # Hindi translations
│   ├── ja.ts                 # Japanese translations
│   ├── ko.ts                 # Korean translations
│   ├── i18n.ts               # i18n configuration
│   ├── translate.ts          # Translation utilities
│   └── index.ts              # i18n exports
├── models/                   # 🏪 MobX-State-Tree stores
│   ├── AuthenticationStore.ts # User authentication state
│   ├── RootStore.ts          # Root store combining all stores
│   ├── helpers/              # Store helper utilities
│   │   ├── getRootStore.ts   # Store access helper
│   │   ├── setupRootStore.ts # Store initialization
│   │   └── useStores.ts      # React hook for store access
│   └── index.ts              # Store exports
├── navigators/               # 🧭 Navigation configuration
│   ├── AppNavigator.tsx      # Main app navigation stack
│   ├── HomeNavigator.tsx     # Home section navigation
│   ├── navigationUtilities.ts # Navigation helper functions
│   └── index.ts              # Navigation exports
├── screens/                  # 📱 Screen components
│   ├── Communities/          # Community-related screens
│   │   ├── CommunitiesScreen.tsx # Main communities screen
│   │   ├── CommunitiesHeader.tsx # Communities header component
│   │   ├── CommunitiesList.tsx # Communities list component
│   │   ├── types.ts          # Communities screen types
│   │   └── index.ts          # Communities exports
│   ├── CommunityDetail/      # Community detail screens
│   │   ├── CommunityDetailScreen.tsx # Main community detail screen
│   │   ├── components/       # Screen-specific components
│   │   │   ├── ChatItem.tsx  # Chat message item
│   │   │   ├── ChatTab.tsx   # Chat tab component
│   │   │   ├── CommunityAbout.tsx # Community about section
│   │   │   ├── CommunityAdmins.tsx # Community admins section
│   │   │   ├── CommunityHeader.tsx # Community header
│   │   │   ├── CommunityRules.tsx # Community rules section
│   │   │   ├── CommunityStats.tsx # Community statistics
│   │   │   ├── CommunityTabs.tsx # Community navigation tabs
│   │   │   ├── EventsTab.tsx # Events tab component
│   │   │   ├── HomeTab.tsx   # Home tab component
│   │   │   ├── MembersTab.tsx # Members tab component
│   │   │   └── index.ts      # Components exports
│   │   ├── hooks/            # Community detail hooks
│   │   │   ├── useCommunityActions.ts # Community action hooks
│   │   │   ├── useCommunityData.ts # Community data hooks
│   │   │   └── index.ts      # Hooks exports
│   │   ├── types.ts          # Community detail types
│   │   ├── utils.ts          # Community detail utilities
│   │   └── index.ts          # Community detail exports
│   ├── ErrorScreen/          # Error handling screens
│   │   ├── ErrorBoundary.tsx # Error boundary component
│   │   └── ErrorDetails.tsx  # Error details component
│   ├── EventDetail/          # Event detail screens
│   │   ├── EventDetailScreen.tsx # Main event detail screen
│   │   ├── components/       # Event detail components
│   │   │   ├── EventDescription.tsx # Event description
│   │   │   ├── EventHeader.tsx # Event header
│   │   │   ├── EventInfo.tsx # Event information
│   │   │   ├── EventLocation.tsx # Event location
│   │   │   ├── EventRules.tsx # Event rules
│   │   │   ├── JoinButton.tsx # Join event button
│   │   │   ├── ParticipantsList.tsx # Event participants
│   │   │   └── index.ts      # Components exports
│   │   ├── hooks/            # Event detail hooks
│   │   │   ├── useEventActions.ts # Event action hooks
│   │   │   ├── useEventDetail.ts # Event detail hooks
│   │   │   └── index.ts      # Hooks exports
│   │   ├── types.ts          # Event detail types
│   │   ├── utils.ts          # Event detail utilities
│   │   └── index.ts          # Event detail exports
│   ├── Home/                 # Home screen
│   │   ├── HomeScreen.tsx    # Main home screen
│   │   ├── components/       # Home screen components
│   │   │   ├── HomeContent.tsx # Home content component
│   │   │   ├── HomeHeader.tsx # Home header component
│   │   │   └── index.ts      # Components exports
│   │   ├── hooks/            # Home screen hooks
│   │   │   ├── useHomeActions.ts # Home action hooks
│   │   │   ├── useHomeScrollToTop.ts # Home scroll hooks
│   │   │   └── index.ts      # Hooks exports
│   │   ├── types.ts          # Home screen types
│   │   ├── utils.ts          # Home screen utilities
│   │   └── index.ts          # Home exports
│   ├── Profile/              # User profile screens
│   │   ├── ProfileScreen.tsx # Main profile screen
│   │   ├── components/       # Profile screen components
│   │   │   ├── Communities.tsx # Profile communities
│   │   │   ├── Matches.tsx   # Profile matches
│   │   │   ├── ProfileHeader.tsx # Profile header
│   │   │   ├── ProfileList.tsx # Profile list
│   │   │   ├── SettingsPopup.tsx # Settings popup
│   │   │   ├── Trophies.tsx  # Profile trophies
│   │   │   └── index.ts      # Components exports
│   │   ├── hooks/            # Profile screen hooks
│   │   │   ├── useProfileActions.ts # Profile action hooks
│   │   │   ├── useProfileData.ts # Profile data hooks
│   │   │   ├── useProfileScrollToTop.ts # Profile scroll hooks
│   │   │   └── index.ts      # Hooks exports
│   │   ├── types.ts          # Profile screen types
│   │   ├── utils.ts          # Profile screen utilities
│   │   └── index.ts          # Profile exports
│   ├── DemoDebugScreen.tsx   # Development debugging screen
│   ├── LoginScreen.tsx       # Authentication screen
│   ├── NotificationScreen.tsx # Notifications screen
│   ├── WelcomeScreen.tsx     # Onboarding screen
│   └── index.ts              # Screen exports
├── services/                 # 🌐 External services & APIs
│   └── api/                  # API layer
│       ├── api.ts            # API client configuration
│       ├── api.types.ts      # API type definitions
│       └── apiProblem.ts     # API error handling
├── theme/                    # 🎨 Design system
│   ├── colors.ts             # Light theme colors
│   ├── colorsDark.ts         # Dark theme colors
│   ├── spacing.ts            # Spacing tokens
│   ├── spacingDark.ts        # Dark theme spacing
│   ├── styles.ts             # Common style utilities
│   ├── timing.ts             # Animation timing values
│   ├── typography.ts         # Typography system
│   └── index.ts              # Theme exports
├── types/                    # 📋 TypeScript type definitions
│   ├── communities.ts        # Community-related types
│   ├── events.ts             # Event-related types
│   ├── notifications.ts      # Notification types
│   └── index.ts              # Type exports
├── utils/                    # 🔧 Utility functions & hooks
│   ├── storage/              # Local storage utilities
│   │   ├── storage.ts        # Storage abstraction
│   │   └── storage.test.ts   # Storage tests
│   ├── crashReporting.ts     # Crash reporting setup
│   ├── delay.ts              # Async delay utility
│   ├── formatDate.ts         # Date formatting utilities
│   ├── gestureHandler.ts     # Gesture handling utilities
│   ├── iconPreloader.ts      # Icon preloading utility
│   ├── openLinkInBrowser.ts  # External link handling
│   ├── safeAreaHelpers.ts    # Safe area utilities
│   ├── safeAsync.ts          # Safe async operations
│   ├── useAppTheme.ts        # Theme hook (ESSENTIAL)
│   ├── useHeader.tsx         # Navigation header hook
│   ├── useIsMounted.ts       # Component mount status
│   └── useSafeAreaInsetsStyle.ts # Safe area styling hook
└── app.tsx                   # 🚀 Main app entry point
```

## 📦 **Assets Directory Structure**

```
assets/
├── icons/                    # Vector icons (@1x, @2x, @3x)
│   ├── back.png             # Navigation back icon
│   ├── bell.png             # Notification icon
│   ├── caretLeft.png        # Left caret icon
│   ├── caretRight.png       # Right caret icon
│   └── demo/                # Demo icons for examples
├── images/                   # App images & graphics
│   ├── app-icon-all.png     # App icon source
│   ├── app-icon-android-*.png # Android app icons
│   └── demo/                # Demo images for examples
└── fonts/                   # Custom fonts (if any)
```

## 📚 **Documentation Structure** (`/docs`)

```
docs/
├── DONE/                     # ✅ Completed documentation
│   └── VECTOR_ICONS_MIGRATION.md # Completed migration docs
├── GUIDE/                    # 📖 Implementation guides
│   ├── Vector Icons.md       # Icon usage guide
│   └── Safe Area.md          # Safe area implementation guide
├── ACTION/                   # 🎯 Action plans & templates
│   └── ACTION_PLAN_TEMPLATE.md # Template for action plans
└── TODO/                     # 📋 Planning & analysis documents
    ├── BACKEND_ARCHITECTURE.md # Backend planning
    ├── SUPABASE_COVERAGE.md  # Database integration planning
    ├── TODO_LIST.md          # Project todo items
    └── SWOT_ANALYSIS.md      # Project analysis
```

## 🔧 **Configuration Files**

### **TypeScript Configuration**
- `tsconfig.json` - Main TypeScript config with strict mode
- `tsconfig.app.json` - App-specific TypeScript config
- `tsconfig.node.json` - Node.js TypeScript config

### **Build & Deploy Configuration**
- `eas.json` - EAS Build profiles (dev/preview/production)
- `metro.config.js` - Metro bundler configuration
- `babel.config.js` - Babel transpiler setup

### **Testing Configuration**
- `jest.config.js` - Jest testing framework setup
- `.maestro/` - End-to-end test flows

## 📁 **Directory Usage Guidelines**

### **Core Application Directories**

#### `/app/components/` - Reusable UI Components
- **Purpose**: Shared components used across multiple screens
- **Pattern**: One component per file, with presets for variants
- **Naming**: PascalCase (`Button.tsx`, `TextField.tsx`)
- **Structure**: Component + styles in same file

#### `/app/screens/` - Screen Components
- **Purpose**: Full-screen components representing app pages
- **Pattern**: One screen per file, extend `AppStackScreenProps`
- **Organization**: Group related screens in subdirectories
- **Structure**: Screen component + local components in subfolder

#### `/app/models/` - State Management
- **Purpose**: MobX-State-Tree stores for global state
- **Pattern**: One store per domain (auth, user, etc.)
- **Access**: Always use `useStores()` hook
- **Structure**: Store definition + actions + computed values

#### `/app/navigators/` - Navigation Setup
- **Purpose**: React Navigation configuration
- **Pattern**: Type-safe navigation with parameter definitions
- **Structure**: Navigator + types + utilities

#### `/app/utils/` - Utility Functions
- **Purpose**: Reusable helper functions and custom hooks
- **Pattern**: Single responsibility, well-tested functions
- **Naming**: Hooks start with `use`, utilities are camelCase

#### `/app/theme/` - Design System
- **Purpose**: Design tokens and theming system
- **Pattern**: Token-based system with light/dark mode support
- **Usage**: Always use through `useAppTheme()` hook

### **Asset Directories**

#### `/assets/icons/` - Vector Icons
- **Purpose**: App icons with multiple resolutions
- **Pattern**: @1x, @2x, @3x variants for each icon
- **Usage**: Through `<Icon>` component only

#### `/assets/images/` - Images & Graphics
- **Purpose**: Static images, app icons, graphics
- **Pattern**: Optimized for mobile, multiple resolutions
- **Usage**: Through `<AutoImage>` component recommended

## 🚨 **Important Patterns**

### **File Naming Conventions**
- **Components**: PascalCase (`UserProfile.tsx`)
- **Screens**: PascalCase (`HomeScreen.tsx`)
- **Hooks**: camelCase starting with 'use' (`useUserData.ts`)
- **Utilities**: camelCase (`formatDate.ts`)
- **Types**: camelCase (`userTypes.ts`)
- **Stores**: PascalCase (`AuthenticationStore.ts`)

### **Import Path Rules**
- **App imports**: Always use `@/` alias (`@/components`, `@/utils`)
- **Relative imports**: Only for files in same directory
- **External imports**: Standard node_modules imports

### **Component Organization**
- **Simple components**: Single file with component + styles
- **Complex components**: Directory with index.tsx + subcomponents
- **Screen components**: Directory with screen + local components

## 🎯 **Quick Reference**

### **Most Important Directories**
1. `/app/components/` - UI building blocks
2. `/app/screens/` - App pages
3. `/app/utils/useAppTheme.ts` - Theming system
4. `/app/navigators/` - Navigation setup
5. `/app/models/` - Global state

### **Key Entry Points**
- `app/app.tsx` - Main app component
- `app/navigators/AppNavigator.tsx` - Navigation root
- `app/models/RootStore.ts` - State root
- `app/theme/index.ts` - Design system root

### **Essential Utilities**
- `useAppTheme()` - Theming hook (use for ALL styling)
- `useStores()` - State access hook
- `useSafeAreaInsetsStyle()` - Safe area handling
- `translate()` - Internationalization

---

*This structure follows Ignite CLI best practices and mobile-first architecture principles. Always check existing patterns before adding new files or directories.*
