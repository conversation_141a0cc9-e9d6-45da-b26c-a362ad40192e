---
description: 
globs: 
alwaysApply: true
---
# Padel Community App - AI Coding Rules

## Project Overview
This is an Ignite-based React Native application using Expo for mobile development. The project follows specific architectural patterns and conventions that must be strictly followed.

## 1. Project Architecture & Structure

### Core Stack
- **Framework**: React Native with Expo (v52+)
- **Language**: TypeScript (strict mode)
- **State Management**: MobX-State-Tree (MST)
- **Navigation**: React Navigation v7 (Native Stack)
- **Storage**: React Native MMKV
- **Styling**: Themed styles with TypeScript
- **Internationalization**: react-i18next
- **Testing**: Jest + React Native Testing Library + Maestro (E2E)

### Current Directory

Always check if you are in correct directory:

### Folder Structure (STRICT)
```
app/
├── components/        # Pure UI components (NO business logic)
├── config/            # App configuration (dev/prod)
├── devtools/          # Reactotron configuration
├── i18n/              # Internationalization files
├── models/            # MobX-State-Tree models
├── navigators/        # Navigation configuration
├── screens/           # Screen components (use services for logic)
├── services/          # Business logic layer (calculations, validations, formatting)
├── theme/             # Theme configuration (colors, spacing, typography)
├── utils/             # Utility functions and hooks
└── app.tsx            # Main app entry point
```

## 2. Component Architecture Rules (MANDATORY)

### Component Definition Pattern
```typescript
// ALWAYS use this pattern for components
import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ComponentNameProps {
  // Props definition
}

export const ComponentName: FC<ComponentNameProps> = observer(function ComponentName(props) {
  const { themed } = useAppTheme()
  
  // Component logic here
  
  return (
    <View style={themed($container)}>
      {/* Component JSX */}
    </View>
  )
})

// Styles at bottom using ThemedStyle
const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  // Themed styles here
})
```

### Component Rules (STRICT)
1. **ALWAYS** wrap components with `observer` from mobx-react-lite
2. **ALWAYS** use functional components with `FC` typing
3. **ALWAYS** use the function name in the observer wrapper
4. **ALWAYS** define interfaces for props with clear naming
5. **ALWAYS** use themed styles for styling
6. **ALWAYS** place styles at the bottom of the file
7. **ALWAYS** use `$` prefix for style objects

## 3. Mobile Screen Architecture (MANDATORY)

### Screen Component Pattern
```typescript
import { observer } from "mobx-react-lite"
import { FC } from "react"
import { Screen } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useHeader } from "@/utils/useHeader"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"

interface ScreenNameProps extends AppStackScreenProps<"ScreenName"> {}

export const ScreenName: FC<ScreenNameProps> = observer(function ScreenName(props) {
  const { navigation } = props
  
  // Configure native header
  useHeader({
    title: "Screen Title",
    leftIcon: "back",
    onLeftPress: () => navigation.goBack(),
  })
  
  // Handle safe areas for notches/home indicators
  const $bottomInsets = useSafeAreaInsetsStyle(["bottom"])
  
  return (
    <Screen preset="scroll" contentContainerStyle={$bottomInsets}>
      {/* Mobile-optimized content */}
    </Screen>
  )
})
```

### Screen Rules (STRICT)
1. **ALWAYS** extend `AppStackScreenProps<T>` for screen props
2. **ALWAYS** use `useHeader()` for native header configuration
3. **ALWAYS** handle safe areas with `useSafeAreaInsetsStyle()`
4. **ALWAYS** use `<Screen>` component for all screen layouts
5. **ALWAYS** specify screen preset: "scroll" | "fixed" | "auto"

## 4. Styling Rules (MANDATORY)

### ThemedStyle Pattern (MANDATORY)
```typescript
// For themed styles
const $styledElement: ThemedStyle<ViewStyle> = ({ colors, spacing, typography }) => ({
  backgroundColor: colors.background,
  padding: spacing.md,
  // Use theme values
})

// For non-themed styles (rare)
const $staticElement: ViewStyle = {
  position: "absolute",
  // Static values only
}
```

### Mobile Touch Targets (MANDATORY)
```typescript
const $touchableStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  minHeight: 44, // iOS HIG minimum
  minWidth: 44,  // Android minimum
  padding: spacing.sm,
  justifyContent: "center",
  alignItems: "center",
})
```

### Style Guidelines (STRICT)
1. **NEVER** use `StyleSheet.create()`
2. **ALWAYS** use `ThemedStyle<T>` for dynamic styling
3. **ALWAYS** prefix style objects with `$`
4. **ALWAYS** use theme values: `colors.*`, `spacing.*`, `typography.*`
5. **ALWAYS** place styles at file bottom
6. **PREFER** theme values over hardcoded values
7. **USE** camelCase for style object names
8. **ENSURE** minimum 44pt touch targets for interactive elements

## 5. Import Rules (STRICT)

### Import Order (MANDATORY)
```typescript
// 1. React imports
import { observer } from "mobx-react-lite"
import { FC, useEffect, useState } from "react"

// 2. React Native imports
import { View, Text, ViewStyle, TextStyle } from "react-native"

// 3. Third-party imports
import { NavigationProp } from "@react-navigation/native"

// 4. Local component imports
import { Button, Screen, Text as CustomText } from "@/components"

// 5. Local utility imports
import { useStores } from "@/models"
import { useAppTheme } from "@/utils/useAppTheme"
import { ThemedStyle } from "@/theme"

// 6. Type imports
import type { AppStackScreenProps } from "@/navigators"
```

### Import Rules (STRICT)
1. **ALWAYS** use `@/` path alias for app imports
2. **GROUP** imports by category (React, RN, 3rd party, local)
3. **SEPARATE** groups with blank lines
4. **USE** type imports when importing only types

## 6. Navigation Rules (MANDATORY)

### Navigation Pattern
```typescript
interface ScreenNameProps extends AppStackScreenProps<"ScreenName"> {}

export const ScreenName: FC<ScreenNameProps> = observer(function ScreenName(props) {
  const { navigation, route } = props
  
  // Type-safe navigation
  navigation.navigate("Profile", { userId: 123 })
  
  // Access typed route params
  const { paramName } = route.params
})
```

### Navigation Rules (STRICT)
1. **ALWAYS** extend `AppStackScreenProps<T>` for screen props
2. **ALWAYS** type navigation parameters in `AppStackParamList`
3. **USE** `navigation.navigate()` for navigation
4. **DEFINE** exit routes in config for Android back button
5. **HANDLE** deep linking with proper parameter validation

## 7. State Management Rules (MANDATORY)

### MobX-State-Tree Usage
```typescript
// In components
const { authenticationStore, userStore } = useStores()

// Always use store methods, never direct manipulation
authenticationStore.login(credentials)
```

### State Rules (STRICT)
1. **ALWAYS** use `useStores()` hook to access stores
2. **NEVER** manipulate store state directly
3. **USE** store actions for state changes
4. **PREFER** computed values over derived state
5. **OPTIMIZE** for mobile performance with efficient reactivity

## 8. Internationalization Rules (MANDATORY)

### Text Component Usage
```typescript
// Using translation keys
<Text tx="common:welcome" />

// Using direct text
<Text text="Direct text" />

// With interpolation
<Text tx="user:greeting" txOptions={{ name: userName }} />
```

### i18n Rules (STRICT)
1. **ALWAYS** use `tx` prop for translatable text
2. **USE** `text` prop only for non-translatable content
3. **ORGANIZE** translations by feature/screen
4. **USE** colon notation for nested keys
5. **SUPPORT** RTL languages (Arabic, Hebrew) with proper layout

## 9. Mobile Component Integration Rules (MANDATORY)

### Required Component Usage
```typescript
// Screen component (MANDATORY for all screens)
<Screen preset="scroll" safeAreaEdges={["top", "bottom"]}>

// Button component (MANDATORY for all buttons)
<Button preset="filled" text="Action" onPress={handlePress} />

// Icon component (MANDATORY for all icons)
<Icon icon="back" size={24} color={colors.text} />

// Text component (MANDATORY for all text)
<Text preset="heading" text="Title" />
```

### Mobile Integration Rules (STRICT)
1. **ALWAYS** use existing component presets before customization
2. **ALWAYS** handle safe areas with `useSafeAreaInsetsStyle()`
3. **ALWAYS** use `useAppTheme()` for theming
4. **NEVER** hardcode colors, spacing, or typography values
5. **ENSURE** components work in both light and dark modes

## 10. Hook Architecture Rules (MANDATORY)

### Hook Responsibility Separation (STRICT)
```typescript
// ✅ Data Hook - ONLY data fetching and state
const useFeatureData = () => ({
  data: events,
  loading: isLoading,
  error: error
})

// ✅ Actions Hook - ONLY business logic and user interactions  
const useFeatureActions = () => ({
  handleJoin: (eventId) => { /* business logic */ },
  handleShare: (content) => { /* share logic */ }
})

// ✅ Navigation Hook - ONLY navigation functions
const useFeatureNavigation = ({ navigation }) => ({
  handleBack: () => navigation.goBack(),
  handleNavigateToDetail: (id) => navigation.navigate("Detail", { id })
})

// ✅ UI Hook - ONLY UI state and behaviors
const useFeatureUIActions = () => ({
  activeTab: currentTab,
  handleTabChange: setCurrentTab,
  scrollToTop: handleScrollToTop
})
```

### Hook Split Rules (MANDATORY)
1. **NEVER** mix data fetching with navigation logic in same hook
2. **ALWAYS** separate business actions from UI state management
3. **SPLIT** hooks when they handle more than one responsibility type
4. **USE** consistent naming: `useXXXData`, `useXXXActions`, `useXXXNavigation`, `useXXXUIActions`
5. **PREFER** multiple focused hooks over one monolithic hook

## 11. Pure UI Components & Service Layer (MANDATORY)

### Component Pattern
```typescript
// ✅ PURE UI COMPONENT - Only display and callbacks
export const EventCard: FC<EventCardProps> = observer(function EventCard({
  event,
  onJoinPress,
}) {
  const { themed } = useAppTheme()
  
  // ✅ Use service for all business logic
  const joinConfig = EventService.getJoinButtonConfig(event)
  const priceText = EventService.formatPriceText(event.price)
  
  return (
    <Card style={themed($container)}>
      <Text text={event.title} preset="subheading" />
      <Text text={priceText} />
      <Button
        preset={joinConfig.preset}
        text={joinConfig.text}
        onPress={() => onJoinPress(event.id)}
        disabled={joinConfig.disabled}
      />
    </Card>
  )
})
```

### Service Pattern
```typescript
// ✅ SERVICE - All business logic separated
export class EventService {
  static getJoinButtonConfig(event: Event, userId?: string): {
    preset: ButtonPresetNames
    text: string
    disabled: boolean
  } {
    // Business logic here
  }

  static formatPriceText(price: number): string {
    return price === 0 ? "Free" : `$${price.toFixed(2)}`
  }
}
```

### Business Logic Separation (STRICT)

#### ✅ BELONGS IN SERVICES
- Data validation, calculations, formatting
- Business rules, status determinations
- Button states, color mappings

#### ✅ BELONGS IN COMPONENTS  
- Display logic, user interactions
- UI state, theme application
- Accessibility

#### ❌ NEVER IN COMPONENTS
- Math operations, data formatting
- Status determination, validation logic
- API logic, data transformation

### Service Rules (STRICT)
1. **ALWAYS** use static methods for stateless logic
2. **ALWAYS** return typed objects with clear interfaces
3. **NEVER** import React or React Native in services
4. **NEVER** access component state directly from services
5. **USE** descriptive method names

## CRITICAL: NEVER VIOLATE THESE PATTERNS
- Component wrapping with observer
- ThemedStyle usage for styling
- Proper import organization
- MobX store usage patterns
- Navigation typing
- Screen component usage for all screens
- Safe area handling
- Mobile touch target minimums
- **Pure UI Components**: No business logic in components
- **Service Layer**: All business logic in services
- **Business Logic Separation**: Components only handle display and callbacks

## Reference: Additional Guides
- **Best Practices**: See **[best-practices.mdc](mdc:best-practices.mdc)** for performance, testing, and code quality
- **Mobile Implementation**: See **[mobile-guide.mdc](mdc:mobile-guide.mdc)** for device features and platform-specific patterns

---

*These rules are MANDATORY and must be followed in all code. They ensure mobile-native performance and consistent architecture.*
