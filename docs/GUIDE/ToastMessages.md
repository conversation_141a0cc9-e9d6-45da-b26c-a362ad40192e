# Toast Messages Guide

> **Library**: [`toastify-react-native`](https://www.npmjs.com/package/toastify-react-native)
>
> **Purpose**: Provide a lightweight, customizable, cross-platform toast notification system for success, error, info and warning messages.
>
> **Global Helpers**: `showSuccessToast`, `showErrorToast`, `showInfoToast`, `showWarningToast`

---

## 1. Installation

```
# Using pnpm (preferred)
pnpm add toastify-react-native react-native-vector-icons

# or yarn
yarn add toastify-react-native react-native-vector-icons

# or npm
npm install toastify-react-native react-native-vector-icons
```

`toastify-react-native` relies on **react-native-vector-icons**.  Follow the [official installation guide](https://github.com/oblador/react-native-vector-icons#installation) if your project is **bare**.  Expo projects already include vector-icons, so no extra steps are required.

---

## 2. App Setup (Already done ✅)

`ToastManager` is registered once in `app/app.tsx` so the component tree only contains a single provider.  The default configuration for our app is:

```tsx
<ToastManager
  position="top"            // Always show at top (avoids keyboard overlap)
  showCloseIcon              // Show dismiss (×) icon
  showProgressBar            // Show time-out progress bar
/>
```

You **must not** mount another `ToastManager`.  Multiple instances cause unpredictable behaviour.

---

## 3. Showing Toasts

Use the **wrapper helpers** located in `@/utils/toast` to display messages anywhere in the codebase.

```ts
import { showSuccessToast, showErrorToast } from "@/utils/toast"

showSuccessToast("Profile updated successfully")
showErrorToast("Something went wrong. Please try again.")
```

### Helper reference

| Helper              | Underlying call                | Typical use-case           |
| ------------------- | ------------------------------ | -------------------------- |
| `showSuccessToast`  | `Toast.success(message, "top")`| CRUD success, confirmations|
| `showErrorToast`    | `Toast.error(message, "top")`  | API failures, validation   |
| `showInfoToast`     | `Toast.info(message, "top")`   | Neutral information        |
| `showWarningToast`  | `Toast.warn(message, "top")`   | Destructive warnings       |

Need more control? Import the original API:

```ts
import { Toast } from "toastify-react-native"
Toast.show({
  type: "success",
  text1: "Custom Title",
  text2: "Longer explanation…",
  position: "top",
  icon: "check-circle",
  iconFamily: "Feather",
  visibilityTime: 4000,
  autoHide: true,
})
```

---

## 4. Replacing Old Notifications

1. **Search** for `Alert.alert(` and `console.log('TODO: Show error toast')` comments.
2. Replace/upgrade those calls with the helpers above.
   ```tsx
   // Before
   Alert.alert("Error", "Failed to fetch data")

   // After
   showErrorToast("Failed to fetch data")
   ```
3. Avoid using `Alert` for non-critical errors; reserve system alerts for confirmations that require an explicit **Yes/No** choice from the user.

---

## 5. Styling & Theming

`toastify-react-native` automatically respects light/dark mode.  If you need further customisation you can pass a `config` object to `ToastManager` and override individual toast types.  Refer to the package README for advanced usage.

---

## 6. FAQ

**Q:** *Can I show a toast from a service file?*

**A:** Yes.  The helpers have no React dependency, but remember to keep **business logic** in services and **display logic** in UI layers.  Expose a callback instead of calling the toast directly from deep service functions when possible.

**Q:** *How do I disable the progress bar for a specific toast?*

**A:** Use the full `Toast.show` API:
```ts
Toast.show({
  type: 'info',
  text1: 'Syncing...',
  showProgressBar: false,
})
```

---

Happy toasting! 🎉 