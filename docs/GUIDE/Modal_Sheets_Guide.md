# Modal Sheet Presentation Guide

This guide documents how to implement a consistent *page-sheet* style modal across iOS and Android for any screen in the Padel Community App.

---

## 1  Navigator-level Setup (iOS)

For **each modal sheet screen** (EditProfile, Notifications, CreateCommunity, etc.) add or update the route in `app/navigators/AppNavigator.tsx` with the following **platform-gated** options:

```tsx
<Stack.Screen
  name="EditProfile"
  component={EditProfileScreen}
  options={{
    headerShown: false,
    presentation: Platform.OS === "ios" ? "modal" : undefined,   // iOS native sheet
    animation:   Platform.OS === "ios" ? "slide_from_bottom" : "none", // smooth slide
    contentStyle: { backgroundColor: "transparent" },            // remove grey backdrop
  }}
/>
```

### Why?
* `presentation: "modal"` → enables the native iOS **pageSheet**.
* `animation: "slide_from_bottom"` → restores the sheet-like slide (we disabled card push).
* `contentStyle.backgroundColor = "transparent"` → removes the default grey layer React-Navigation inserts between the root screen and the sheet.

Android ignores `presentation: "modal"`, so we guard the prop with `Platform.OS`.

Important notes:
* **Never set `presentation: "modal"` on Android** – it falls back to a normal card animation that slides from the right.
* Leaving `animation: "none"` (or omitting it) on Android ensures the underlying stack does **not** animate when the sheet is dismissed.
* The same option block should be duplicated for every sheet-style route (e.g., `Notifications`, `CreateCommunity`).

---

## 2  Screen-level Implementation

In the screen component (`EditProfileScreen.tsx` as reference):

```tsx
import { Modal, Platform, useState } from "react-native"

const isIOS = Platform.OS === "ios"
const [visible, setVisible] = useState(!isIOS) // Modal only shown on Android

if (isIOS) {
  // iOS uses the navigator's native sheet – just render the content
  return <EditProfileContent … />
}

// Android path: mimic a sheet with RN <Modal>
return (
  <Modal
    visible={visible}
    animationType="slide"
    presentationStyle="pageSheet"
    onRequestClose={handleClose}
  >
    <EditProfileContent … />
  </Modal>
)
```

### Key points
* **iOS**: render content directly (native-stack already created the sheet).
* **Android**: wrap the content in a React-Native `Modal` with `presentationStyle="pageSheet"` to approximate the same behaviour.
* Close logic: `handleClose` hides the Modal (Android) **and** calls `navigation.goBack()` to pop the stack.

---

## 3  Header UX

Update the in-screen header to use an "X" icon instead of a back arrow – this clearly communicates that the user is dismissing a modal, not navigating back in the stack.

```diff
- <Icon icon="back" size={24} />
+ <Icon icon="x"   size={24} />
```

Apply the change to every header inside modal sheets (e.g. `EditProfileContent`, `NotificationsScreen`).

---

## 4  Step-by-Step Checklist for New Screens

1. **Navigator**
   * Add `presentation`, `animation`, `contentStyle` with `Platform.OS` guards as shown above.
2. **Screen file**
   * Implement iOS/Android split: direct render vs RN `<Modal>`.
3. **Header**
   * Replace back arrow with `x` icon wired to `navigation.goBack()`.
4. **Test**
   * iOS: sheet slides up, swipe-down dismisses, no grey backdrop.
   * Android: sheet slides up from bottom via RN Modal, back button or X dismisses.

---

## 5  Troubleshooting

| Issue | Fix |
|-------|-----|
| Double modal on iOS | Ensure the RN `<Modal>` is **not** rendered on iOS.<br/>`const isIOS = Platform.OS === "ios"` |
| Grey background visible | Set `contentStyle.backgroundColor = "transparent"` in navigator options. |
| No animation on iOS | Use `animation: "slide_from_bottom"` in navigator options. |
| Sheet pushes from right (card) | Remove `presentation: "card"` or any default; ensure `presentation: "modal"`. |

---

### Reference Implementations
* `EditProfileScreen.tsx`
* `NotificationsScreen.tsx`
* Navigator excerpt in `AppNavigator.tsx`

Use these as templates for future modal-sheet screens. 