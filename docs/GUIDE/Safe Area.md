# Safe Area Handling Guide

## Overview

This guide covers best practices for handling safe areas across different devices in React Native, specifically for the Padel Community App.

## Current Implementation

We use `react-native-safe-area-context` (version 4.12.0) which is the industry standard for safe area handling.

## Best Practices

### 1. Screen-Level Safe Area Handling

**✅ DO:**
```tsx
// Let the Screen component handle safe areas
<Screen preset="fixed" safeAreaEdges={["top", "bottom"]}>
  <YourContent />
</Screen>
```

**❌ DON'T:**
```tsx
// Don't disable safe areas unless absolutely necessary
<Screen preset="fixed" safeAreaEdges={[]}>
  <YourContent />
</Screen>
```

### 2. Safe Area Configurations

Use our predefined configurations from `utils/safeAreaHelpers.ts`:

```tsx
import { getSafeAreaConfig } from '@/utils/safeAreaHelpers'

// For screens with tab navigation
<Screen safeAreaEdges={getSafeAreaConfig('WITH_TABS')}>

// For full-screen content
<Screen safeAreaEdges={getSafeAreaConfig('FULL_SCREEN')}>

// For modal screens
<Screen safeAreaEdges={getSafeAreaConfig('MODAL')}>
```

### 3. Header Components

**✅ DO:**
```tsx
// Let the parent Screen handle safe areas
export const MyHeader = () => {
  return (
    <View style={$header}>
      {/* Header content */}
    </View>
  )
}
```

**❌ DON'T:**
```tsx
// Don't manually add safe area padding in components
export const MyHeader = () => {
  const { top } = useSafeAreaInsets()
  return (
    <View style={[$header, { paddingTop: top }]}>
      {/* Header content */}
    </View>
  )
}
```

### 4. Tab Navigator

**✅ DO:**
```tsx
const Tab = createBottomTabNavigator()

export function HomeNavigator() {
  const { bottom } = useSafeAreaInsets()
  
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarStyle: { height: bottom + 70 }, // Dynamic height
        // ... other options
      }}
    >
      {/* Tab screens */}
    </Tab.Navigator>
  )
}
```

### 5. Enhanced Safe Area Hook

Use our enhanced hook for more control:

```tsx
import { useEnhancedSafeArea } from '@/utils/safeAreaHelpers'

export const MyComponent = () => {
  const safeArea = useEnhancedSafeArea()
  
  // Check device characteristics
  if (safeArea.hasNotch) {
    // Handle devices with notch/dynamic island
  }
  
  if (safeArea.hasHomeIndicator) {
    // Handle devices with home indicator
  }
  
  return (
    <View style={{ paddingTop: safeArea.headerPaddingTop }}>
      {/* Content */}
    </View>
  )
}
```

## Device-Specific Considerations

### iPhone 14 Pro/15 Pro (Dynamic Island)
- Top inset: ~59px
- Bottom inset: ~34px
- Need extra consideration for camera cutout

### iPhone SE/8 (Home Button)
- Top inset: 20px
- Bottom inset: 0px
- Traditional status bar

### Android Devices
- Varies greatly by manufacturer
- Use `StatusBar.currentHeight` for Android status bar
- Handle notches and gesture navigation differently

### iPad
- Generally minimal safe areas
- Consider orientation changes
- Different safe areas in split view

## Common Issues and Solutions

### Issue: Content Hidden Behind Status Bar
**Solution:** Ensure `safeAreaEdges` includes "top"

### Issue: Content Hidden Behind Tab Bar
**Solution:** Use `WITH_TABS` configuration or exclude "bottom" from safe area edges

### Issue: Inconsistent Padding Across Devices
**Solution:** Use our helper functions instead of hardcoded values

### Issue: Modal Content Not Properly Spaced
**Solution:** Use `MODAL` configuration for full-screen modals

## Testing Strategy

1. **iOS Simulator:** Test on various iPhone models (SE, 14, 15 Pro)
2. **Android Emulator:** Test on different screen sizes and Android versions
3. **Physical Devices:** Always test on real devices when possible
4. **Orientation:** Test both portrait and landscape modes
5. **Split Screen:** Test iPad split screen scenarios

## Migration Checklist

- [ ] Replace `safeAreaEdges={[]}` with appropriate configurations
- [ ] Remove manual `useSafeAreaInsets()` from header components
- [ ] Update hardcoded padding values
- [ ] Use `getSafeAreaConfig()` helper functions
- [ ] Test on multiple device types
- [ ] Verify tab navigation spacing
- [ ] Check modal presentations

## Additional Libraries (Optional)

While `react-native-safe-area-context` is sufficient for most cases, consider these for specific needs:

### react-native-super-grid
- For grid layouts that need safe area awareness

### react-native-keyboard-aware-scroll-view
- Already included, works well with safe areas

### expo-system-ui
- For advanced system UI control (already included)

## Performance Considerations

- Safe area insets are calculated once and cached
- Avoid nested `useSafeAreaInsets()` calls
- Use our helper functions for consistent calculations
- Consider memoization for complex safe area calculations

## Resources

- [React Native Safe Area Context Documentation](https://github.com/th3rdwave/react-native-safe-area-context)
- [iOS Human Interface Guidelines - Safe Areas](https://developer.apple.com/design/human-interface-guidelines/layout)
- [Android Design Guidelines - System UI](https://developer.android.com/design/ui/mobile/guides/layout/system-ui) 