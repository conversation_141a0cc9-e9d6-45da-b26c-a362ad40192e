# Developer Guide: Local Data Management with Expo & Drizzle ORM

## 🎯 **Purpose of this Guide**
This document serves as a comprehensive guide for developers working with local data persistence in the Padel Community App. It explains the existing architecture using `expo-sqlite` and Drizzle ORM, and provides instructions on how to leverage, expand, and maintain this system for new features and data entities.

**This guide assumes you are familiar with:**
*   TypeScript
*   React Native & Expo
*   MobX-State-Tree (MST) fundamentals
*   Basic SQL concepts

## 🏗️ **Existing Architecture: The Repository Pattern**

Our application utilizes a **Repository Pattern** to manage data access. This pattern abstracts the data source (whether it's a local SQLite database or a remote API) behind a clean, consistent interface. This design choice provides immense flexibility: we can swap out the underlying data source without affecting the business logic or UI.

```mermaid
graph TD
    UI[UI Components] -->|Accesses Data via| MST[MobX-State-Tree Stores]
    MST -->|Requests Data from| RepositoryInterface[Repository Interfaces (e.g., CommunityRepository)]
    RepositoryInterface -->|Implemented by (Currently Active)| SQLiteRepo[CommunityRepositorySqlite]
    RepositoryInterface -->|Implemented by (Future)| SupabaseRepo[CommunityRepositorySupabase (Stub)]
    SQLiteRepo -->|Uses| DrizzleORM[Drizzle ORM]
    DrizzleORM -->|Connects to| ExpoSQLite[expo-sqlite]
    ExpoSQLite -->|Manages Local File| AppDB[app.db (SQLite Database)]

    subgraph Data Layer
        RepositoryInterface
        SQLiteRepo
        SupabaseRepo
        DrizzleORM
        ExpoSQLite
        AppDB
    end
```

### **Key Components and Their Roles:**

1.  **Repository Interfaces (`app/services/repositories/`)**:
    *   **Purpose**: Define the contract for data operations (CRUD - Create, Read, Update, Delete) for specific data entities (e.g., `Community`, `Event`).
    *   **Structure**: These are TypeScript interfaces (e.g., `CommunityRepository`, `EventRepository`) that declare methods like `getAll()`, `getById(id)`, `save(entity)`, `delete(id)`.
    *   **How to Use**: When a MobX-State-Tree store needs data, it calls methods defined in these interfaces. It *does not* care about the underlying implementation.
    *   **Expansion**: To manage a new data entity (e.g., `PlayerProfile`), you would first define a new interface (`PlayerProfileRepository`) in this directory.

2.  **SQLite Implementation (`app/services/sqlite/`)**:
    *   **Purpose**: Contains the concrete classes that implement the Repository Interfaces using `expo-sqlite` and Drizzle ORM. This is our current active data source.
    *   **Structure**:
        *   `sqlite-client.ts`: Establishes the connection to the `app.db` file.
        *   `schema.ts`: Defines the database tables and their columns using Drizzle's schema declaration API. This is where your TypeScript types are mapped to database structures.
        *   `migrations.ts`: Contains the logic to create or update database tables (schema migration) using raw SQL `CREATE TABLE` statements.
        *   `*-repository-sqlite.ts`: Files like `community-repository-sqlite.ts` implement the corresponding interfaces (e.g., `CommunityRepository`) using Drizzle ORM queries (e.g., `drizzleDb.select().from(communities).all()`).
    *   **How to Use**: These classes handle the actual database interactions. They also manage the conversion between complex JavaScript/TypeScript objects (e.g., arrays, nested objects) and their `TEXT` representation in SQLite (using `JSON.stringify` and `JSON.parse`). They also handle null values.
    *   **Expansion**: For a new `PlayerProfileRepository` interface, you would create `player-profile-repository-sqlite.ts` here, implement its methods, and update `schema.ts` with the new table definition.

3.  **Supabase Stub (`app/services/supabase/`)**:
    *   **Purpose**: This directory holds placeholder implementations (stubs) for future remote data sources, like Supabase.
    *   **Structure**: Files like `community-repository-supabase.ts` contain methods that currently just throw a "Not implemented" error.
    *   **Expansion**: When we integrate Supabase, these stubs will be replaced with actual implementations that interact with the Supabase API, providing a seamless transition with zero impact on higher layers of the application.

4.  **MobX-State-Tree (MST) Stores (`app/models/`)**:
    *   **Purpose**: Manage the application's global state and act as the primary interface for UI components to access and modify data.
    *   **Structure**:
        *   `RootStore.ts`: The central store that aggregates all other MST stores (e.g., `AuthenticationStore`, `OfflineStore`, `CommunityStore`).
        *   `OfflineStore.ts`: Tracks the hydration status of the local database, indicating when data is loaded and ready.
        *   `CommunityStore.ts` (and similar for other entities): These stores hold the actual data (e.g., an array of `Community` objects) and expose actions (e.g., `hydrate()`, `addCommunity()`, `removeCommunity()`) that delegate to the appropriate Repository implementation.
    *   **How to Use**: UI components use `useStores()` to access these stores. Stores then call methods on the Repository interfaces to perform data operations.
    *   **Expansion**: For a new `PlayerProfile` entity, you would create `PlayerProfileStore.ts`, define its model and actions, and add it to `RootStore.ts`.

## 🛠️ **How to Expand the Data Layer for a New Entity**

Let's walk through the process of adding a new data entity, for example, `PlayerProfile`, to the local database system.

### **Step 1: Define the Data Structure (`app/types/`)**

First, define your TypeScript interface for the new entity.

```typescript
// app/types/player-profile.ts
export interface PlayerProfile {
  id: string;
  name: string;
  email: string;
  age?: number;
  level: string; // e.g., "Beginner", "Intermediate"
  stats: { wins: number; losses: number }; // Complex type
}

// app/types/index.ts (ensure it's exported)
export * from "./player-profile";
```

### **Step 2: Define the Repository Interface (`app/services/repositories/`)**

Create an interface for CRUD operations on your new entity.

```typescript
// app/services/repositories/player-profile-repository.ts
import { PlayerProfile } from "@/types";

export interface PlayerProfileRepository {
  getAll(): Promise<PlayerProfile[]>;
  getById(id: string): Promise<PlayerProfile | undefined>;
  save(entity: PlayerProfile): Promise<void>;
  delete(id: string): Promise<void>;
}

// app/services/repositories/index.ts (add to barrel file)
export * from "./player-profile-repository";
```

### **Step 3: Update the Drizzle Schema (`app/services/sqlite/schema.ts`)**

Add a new table definition in `schema.ts` that corresponds to your `PlayerProfile` interface. Remember to use `text()` for complex JSON objects.

```typescript
// app/services/sqlite/schema.ts (partial update)
import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
// ... existing tables

export const playerProfiles = sqliteTable('player_profiles', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull(),
  age: integer('age'),
  level: text('level'),
  stats: text('stats'), // Stored as JSON string
});
```

### **Step 4: Create a Migration (`app/services/sqlite/migrations.ts`)**

Add a `CREATE TABLE` statement for your new entity. This will be executed when the app starts.

```typescript
// app/services/sqlite/migrations.ts (partial update)
import { db } from "./sqlite-client";

export const runMigrations = () => {
  console.log("Running migrations...");
  try {
    db.execSync(
      `CREATE TABLE IF NOT EXISTS communities (...);` // Existing table
    );
    db.execSync(
      `CREATE TABLE IF NOT EXISTS player_profiles (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        age INTEGER,
        level TEXT,
        stats TEXT
      );`
    );
    console.log("Player profiles table migration successful.");
  } catch (error) {
    console.error("Migration failed:", error);
  }
};
```

### **Step 5: Implement the SQLite Repository (`app/services/sqlite/`)**

Create a class that implements `PlayerProfileRepository` using Drizzle ORM. Pay close attention to serializing/deserializing JSON fields and handling nullable values.

```typescript
// app/services/sqlite/player-profile-repository-sqlite.ts
import { PlayerProfileRepository } from "../repositories/player-profile-repository";
import { PlayerProfile } from "@/types";
import { db } from "./sqlite-client";
import { drizzle } from 'drizzle-orm/expo-sqlite';
import { playerProfiles } from './schema'; // Import new table schema
import { eq } from "drizzle-orm";

const drizzleDb = drizzle(db);

export class PlayerProfileRepositorySqlite implements PlayerProfileRepository {
  private parseJSON(jsonString: string | null, defaultValue: any) {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString);
    } catch (e) {
      console.error("Failed to parse JSON:", jsonString, e);
      return defaultValue;
    }
  }

  async getAll(): Promise<PlayerProfile[]> {
    const result = await drizzleDb.select().from(playerProfiles).all();
    return result.map(p => ({
      ...p,
      age: p.age ?? undefined, // Handle nullable number
      stats: this.parseJSON(p.stats, { wins: 0, losses: 0 }), // Parse JSON
    }));
  }

  async getById(id: string): Promise<PlayerProfile | undefined> {
    const result = await drizzleDb.select().from(playerProfiles).where(eq(playerProfiles.id, id)).get();
    if (!result) return undefined;
    return {
      ...result,
      age: result.age ?? undefined,
      stats: this.parseJSON(result.stats, { wins: 0, losses: 0 }),
    };
  }

  async save(entity: PlayerProfile): Promise<void> {
    const entityToSave = {
      ...entity,
      stats: JSON.stringify(entity.stats), // Stringify JSON
    };
    await drizzleDb.insert(playerProfiles).values(entityToSave).onConflictDoUpdate({ target: playerProfiles.id, set: entityToSave });
  }

  async delete(id: string): Promise<void> {
    await drizzleDb.delete(playerProfiles).where(eq(playerProfiles.id, id));
  }
}
```

### **Step 6: Create the Supabase Stub (`app/services/supabase/`)**

Maintain the pattern for future remote integration.

```typescript
// app/services/supabase/player-profile-repository-supabase.ts
import { PlayerProfileRepository } from "../repositories/player-profile-repository";
import { PlayerProfile } from "@/types";

export class PlayerProfileRepositorySupabase implements PlayerProfileRepository {
  getAll(): Promise<PlayerProfile[]> { throw new Error("Not implemented"); }
  getById(id: string): Promise<PlayerProfile | undefined> { throw new Error("Not implemented"); }
  save(entity: PlayerProfile): Promise<void> { throw new Error("Not implemented"); }
  delete(id: string): Promise<void> { throw new Error("Not implemented"); }
}
```

### **Step 7: Create the MobX-State-Tree Store (`app/models/`)**

Define the MST model and store, exposing actions that use your new repository.

```typescript
// app/models/PlayerProfileStore.ts
import { Instance, types, flow } from "mobx-state-tree";
import { PlayerProfile } from "@/types";
import { PlayerProfileRepositorySqlite } from "@/services/sqlite/player-profile-repository-sqlite";
import { withSetPropAction } from "./helpers/withSetPropAction";

const PlayerProfileModel = types.model("PlayerProfile").props({
    id: types.identifier,
    name: types.string,
    email: types.string,
    age: types.maybe(types.number),
    level: types.string,
    stats: types.frozen(), // Use frozen for complex objects
});

export const PlayerProfileStoreModel = types
    .model("PlayerProfileStore")
    .props({
        playerProfiles: types.array(PlayerProfileModel),
    })
    .actions(withSetPropAction)
    .actions(self => {
        const playerProfileRepository = new PlayerProfileRepositorySqlite();

        const hydrate = flow(function* () {
            const profiles: PlayerProfile[] = yield playerProfileRepository.getAll();
            self.setProp("playerProfiles", profiles);
        });

        const addProfile = flow(function* (profile: PlayerProfile) {
            yield playerProfileRepository.save(profile);
            yield hydrate();
        });

        // Add other CRUD actions as needed

        return {
            hydrate,
            addProfile,
        };
    });

export interface PlayerProfileStore extends Instance<typeof PlayerProfileStoreModel> { }
```

### **Step 8: Integrate into `RootStore` and Hydration (`app/models/`)**

Add your new store to `RootStore.ts` and ensure it's hydrated on app startup in `setupRootStore.ts`.

```typescript
// app/models/RootStore.ts (partial update)
import { PlayerProfileStoreModel } from "./PlayerProfileStore"; // New import

export const RootStoreModel = types.model("RootStore").props({
  // ... existing stores
  playerProfileStore: types.optional(PlayerProfileStoreModel, {}), // Add new store
});

// app/models/helpers/setupRootStore.ts (partial update)
export async function setupRootStore(rootStore: RootStore) {
  // ... existing code

  // Hydrate new store
  await rootStore.playerProfileStore.hydrate();
  // ... existing hydration calls
}
```

### **Step 9: Update Type Exports (`app/types/index.ts`)**

Ensure your new types are exported from the main `types` barrel file.

```typescript
// app/types/index.ts (partial update)
export * from "./communities";
export * from "./events";
export * from "./notifications";
export * from "./messages";
export * from "./player-profile"; // New export
```

## 🚀 **Future Expansion and Maintenance**

### **Adding New Features to Existing Entities**
If you need to add new fields to an existing entity (e.g., `Community`), follow these steps:
1.  **Update Interface**: Modify `CommunityData` in `app/types/communities.ts`.
2.  **Update Drizzle Schema**: Add the new column(s) to `communities` table in `app/services/sqlite/schema.ts`.
3.  **Create Migration**: Add an `ALTER TABLE` statement in `app/services/sqlite/migrations.ts` to add the new column.
4.  **Update Repository**: Adjust `CommunityRepositorySqlite` methods (`getAll`, `getById`, `save`) to handle the new field, including `JSON.parse`/`JSON.stringify` if it's a complex type, and null handling.
5.  **Update MST Store**: Modify `CommunityModel` and `CommunityStore` actions as needed.

### **Swapping to a Remote Backend (e.g., Supabase)**
The Repository pattern makes this straightforward:
1.  **Implement Remote Repositories**: Replace the stub implementations in `app/services/supabase/` (e.g., `community-repository-supabase.ts`) with actual code that interacts with your remote API (e.g., `@supabase/supabase-js`).
2.  **Update Repository Instantiation**: In your MST stores (e.g., `CommunityStore.ts`), change the instantiation from `new CommunityRepositorySqlite()` to `new CommunityRepositorySupabase()` (or use a dependency injection system based on an environment flag).
3.  **Data Synchronization**: Implement a `SyncManager` (as outlined in `LOCAL_DB_SETUP_ACTION_PLAN.md`) to handle synchronization between the local database and the remote backend (e.g., stale-while-revalidate, offline queueing).

### **Maintaining Schema & Migrations**
*   **Drizzle Kit CLI**: Use `drizzle-kit` commands (e.g., `npm run studio`) for schema visualization and debugging.
*   **Schema Evolution**: For significant schema changes, consider using Drizzle Kit's migration generation features (though currently we use raw SQL for simplicity in `migrations.ts`). For complex scenarios, Drizzle Kit's `generate` and `migrate` commands can help.

## 🚀 **Drizzle ORM Development Workflow**

This section details the typical workflow and commands used for managing your local SQLite database schema and data with Drizzle ORM and `drizzle-kit`.

### **1. Schema Definition (`app/services/sqlite/schema.ts`)**
*   **Guidance**: Define your database tables and their columns in `app/services/sqlite/schema.ts` using Drizzle's schema declaration API. This file serves as the single source of truth for your database structure.

### **2. Generating Migrations**
*   **Purpose**: After modifying `schema.ts`, you need to generate SQL migration files that reflect these changes. These files are used to update your database schema.
*   **Command**: Ensure your `package.json` has a script like:
    ```json
    "scripts": {
      "drizzle:generate": "drizzle-kit generate --config ./drizzle.config.ts"
    }
    ```
    Then run: `npm run drizzle:generate`
*   **Explanation**: This command reads your `schema.ts` and compares it to the last known state (stored in `drizzle/meta`). If differences are found, it creates a new `.sql` file in the `drizzle/` directory with the necessary `ALTER TABLE` or `CREATE TABLE` statements.

### **3. Applying Migrations**
*   **Purpose**: To apply the generated SQL migration files to your local `app.db` database.
*   **Command**: Ensure your `package.json` has a script (if not already present):
    ```json
    "scripts": {
      "drizzle:migrate": "drizzle-kit migrate --config ./drizzle.config.ts"
    }
    ```
    Then run: `npm run drizzle:migrate`
*   **Explanation**: This command reads all `.sql` files in the `drizzle/` directory and executes them against the `app.db` specified in `drizzle.config.ts`. It also manages a `drizzle_migrations` table to keep track of applied migrations.

### **4. Syncing Schema (`drizzle-kit push`)**
*   **Purpose**: This command is useful for rapidly developing and testing schema changes without generating explicit migration files. It directly synchronizes your `schema.ts` with the connected database.
*   **Command**: `npx drizzle-kit push --config ./drizzle.config.ts`
*   **Explanation**: `drizzle-kit push` will compare your `schema.ts` directly with the `app.db` database and apply the necessary `CREATE TABLE`, `ALTER TABLE`, or `DROP TABLE` statements to make the database schema match your code. **Use with caution in production or shared development environments as it can lead to data loss if not used carefully.**

### **5. Database File (`app.db`) and Sandbox Interaction**
*   **Local Development `app.db`**: During development, `drizzle-kit` tools (like `generate`, `push`, `migrate`, `studio`) interact with a local `app.db` file located in your project's root directory. This file is primarily for schema management and initial data seeding during development.
*   **Expo Sandbox Database**: When your Expo application runs on a physical device or simulator, `expo-sqlite` creates and manages its own `app.db` file within the application's private sandbox. This is an isolated environment, and the `app.db` in your project root is *not* directly synced to the device's sandbox.
*   **Synchronization**: The schema defined in `app/services/sqlite/schema.ts` is what dictates the structure of the database created by `expo-sqlite` on the device. Data seeding (like generating dummy users) needs to be handled within the application's lifecycle or through development-time scripts that interact with the local `app.db` directly (as demonstrated by `insert_users.ts` which populates the root `app.db`). To get data into the device's `app.db`, you'd typically have an in-app seeding mechanism or fetch from a remote source.

### **6. `better-sqlite3` Usage**
*   **Context**: `better-sqlite3` is a fast, synchronous Node.js SQLite library used by `drizzle-kit` internally to interact with the local `app.db` file during development (e.g., when running `drizzle-kit push` or `drizzle-kit migrate`).
*   **Direct Interaction (for seeding/debugging)**: As demonstrated when inserting dummy users, `better-sqlite3` can also be used directly in Node.js scripts (like `insert_users.ts`) to perform operations on the local `app.db` file. This is particularly useful for one-off data seeding, bulk imports, or advanced debugging scenarios outside the typical Drizzle ORM flow.
*   **Installation**: If you encounter errors about missing SQLite drivers, you might need to install `better-sqlite3` or `@libsql/client` as a dependency (`npm install better-sqlite3`).

### **7. Drizzle Studio (`npm run studio`)**
*   **Purpose**: A web-based GUI for visualizing your database schema and data, and for interacting with your database.
*   **Command**: `npm run studio` (assuming a script is configured in `package.json`).
*   **Access**: Once started, it usually runs on `https://local.drizzle.studio` and connects to the `app.db` defined in your `drizzle.config.ts`. It provides a convenient way to verify data and schema directly.

## ⚠️ **Important Nuances**

*   **iOS Sandboxing**: Remember that `app.db` is stored within the app's private sandbox (e.g., `Library/Application Support` or `Documents/SQLite`) and not directly in the app bundle. This is standard iOS behavior and cannot be changed.
*   **JSON Handling**: Always `JSON.stringify` complex objects (arrays, nested objects) before saving them to `TEXT` columns in SQLite, and `JSON.parse` them after retrieval. Implement robust error handling for `JSON.parse`.
*   **Nullable Fields**: Database columns can be nullable, but TypeScript interfaces often define fields as non-nullable. Always use nullish coalescing (`??`) or optional chaining (`?.`) when accessing or mapping data from the database to your TypeScript types.
*   **Native Module Linking**: If you encounter "Cannot find native module" errors, ensure you run `npx expo prebuild --clean` and rebuild/reinstall the native app.

By following these guidelines, developers can effectively extend and maintain the local data management system, ensuring a robust and scalable application.
