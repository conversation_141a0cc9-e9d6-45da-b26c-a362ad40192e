# StatusBadge Component - Usage Guide

## Overview

The `StatusBadge` component provides a centralized solution for all badge/tag implementations across the Padel Community App. It eliminates code duplication and ensures consistent styling and behavior.

## Import

```typescript
import { StatusBadge } from "@/components"
```

## Basic Usage

```typescript
<StatusBadge status="Active" />
<StatusBadge status="Full" variant="status" size="sm" />
```

## Variants & Use Cases

### 1. Event Type Badges (`variant="type"`)

**Use for**: Event type indicators (Americano, Tournament, etc.)

```typescript
// ✅ BEFORE (duplicated across EventCard, EventCardLight, etc.)
const getTypeColor = (type: string) => {
  switch (type) {
    case EventType.AMERICANO: return colors.palette.secondary500
    case EventType.MEXICANO: return colors.palette.accent500
    // ... more duplicated logic
  }
}
<View style={[themed($typeTag), { backgroundColor: getTypeColor(event.type) }]}>
  <Text text={event.type} style={themed($typeText)} />
</View>

// ✅ AFTER (centralized)
<StatusBadge status={event.type} variant="type" size="xs" />
```

**Supported Event Types:**
- `AMERICANO` → Secondary blue
- `MEXICANO` → Accent yellow
- `TOURNAMENT` → Primary orange  
- `LEAGUE` → Red
- `SOCIAL` → Light accent
- Others → Neutral gray

### 2. Status Badges (`variant="status"`)

**Use for**: General statuses, event states, community states

```typescript
// Event statuses
<StatusBadge status="joinable" variant="status" />
<StatusBadge status="full" variant="status" />
<StatusBadge status="completed" variant="status" />
<StatusBadge status="cancelled" variant="status" />

// Action statuses
<StatusBadge status="joined" variant="status" />
<StatusBadge status="pending" variant="status" />
<StatusBadge status="request" variant="status" />
```

**Color Mapping:**
- `joinable`, `join`, `joined`, `active` → Green (success)
- `full`, `inactive` → Gray (neutral)
- `completed`, `finished` → Dark gray
- `cancelled`, `canceled`, `closed` → Red (error)
- `request`, `pending` → Yellow (accent)

### 3. Role Badges (`variant="role"`)

**Use for**: User roles, permissions, community positions

```typescript
// ✅ BEFORE (in CommunityCardLight.tsx)
<View style={themed($roleBadge)}>
  <Text text={community.role} style={themed($roleBadgeText)} size="xs" />
</View>

// ✅ AFTER
<StatusBadge status={community.role} variant="role" size="sm" />
```

**Examples:**
```typescript
<StatusBadge status="Admin" variant="role" />
<StatusBadge status="Moderator" variant="role" />
<StatusBadge status="Member" variant="role" />
```

**Color Mapping:**
- `admin`, `administrator` → Primary orange
- `moderator`, `mod` → Secondary blue
- `member`, `user` → Neutral gray

### 4. Status Indicators (`variant="indicator"`)

**Use for**: Small circular status indicators (online/offline)

```typescript
// ✅ BEFORE (in CommunityAdmins.tsx)
<View
  style={[
    themed($statusIndicator),
    {
      backgroundColor: admin.status === "Online" 
        ? colors.palette.accent500 
        : colors.palette.neutral400,
    },
  ]}
/>

// ✅ AFTER
<StatusBadge status={admin.status} variant="indicator" />
```

**Examples:**
```typescript
<StatusBadge status="online" variant="indicator" />
<StatusBadge status="offline" variant="indicator" />
<StatusBadge status="away" variant="indicator" />
```

## Size Options

```typescript
<StatusBadge status="Small" size="xs" />    // 16px height, 10px font
<StatusBadge status="Medium" size="sm" />   // 20px height, 11px font  
<StatusBadge status="Large" size="md" />    // 24px height, 12px font
```

## Custom Styling

```typescript
<StatusBadge 
  status="Custom" 
  style={{ marginLeft: 8 }}
  textStyle={{ fontWeight: 'bold' }}
/>
```

## Replacement Examples

### Replace EventCard Type Tags

```typescript
// ❌ BEFORE (47 lines in EventCard.tsx)
const getTypeColor = (type: string) => {
  switch (type) {
    case EventType.AMERICANO:
      return colors.palette.secondary500
    case EventType.MEXICANO:
      return colors.palette.accent500
    case EventType.TOURNAMENT:
      return colors.palette.primary500
    case EventType.LEAGUE:
      return colors.palette.angry500
    case EventType.SOCIAL:
      return colors.palette.accent400
    default:
      return colors.palette.neutral500
  }
}

<View style={[themed($typeTag), { backgroundColor: getTypeColor(event.type) }]}>
  <Text text={event.type} style={themed($typeText)} size="xxs" weight="medium" />
</View>

const $typeTag: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 12,
})

const $typeText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

// ✅ AFTER (1 line)
<StatusBadge status={event.type} variant="type" size="xs" />
```

### Replace UpcomingMatchCard Status

```typescript
// ❌ BEFORE (in UpcomingMatchCard.tsx)
<View style={themed($statusBadge)}>
  <Text text={match.status} style={themed($statusText)} />
</View>

const $statusBadge: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "rgba(255, 255, 255, 0.2)",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: spacing.xs,
})

// ✅ AFTER
<StatusBadge status={match.status} variant="status" size="sm" />
```

### Replace CommunityCardLight Role Badge

```typescript
// ❌ BEFORE (in CommunityCardLight.tsx)
<View style={themed($roleBadge)}>
  <Text text={community.role} style={themed($roleBadgeText)} size="xs" />
</View>

const $roleBadge: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  backgroundColor: colors.palette.neutral200,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: spacing.xs,
})

const $roleBadgeText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

// ✅ AFTER
<StatusBadge status={community.role} variant="role" size="sm" />
```

## Benefits

### ✅ **Elimination of Code Duplication**
- **Before**: 4+ duplicate `generateInitials` functions, 8+ similar badge styles
- **After**: Single centralized component with consistent behavior

### ✅ **Consistent Design System**
- Unified color mapping across all badge types
- Consistent sizing and spacing
- Proper theme integration

### ✅ **Maintenance Efficiency**
- Single source of truth for badge logic
- Easy to add new status types or variants
- Centralized styling updates

### ✅ **Type Safety**
- Proper TypeScript interfaces
- IntelliSense support for all badge variants
- Compile-time error checking

## Migration Checklist

1. ✅ **Replace EventCard type tags** → `<StatusBadge variant="type" />`
2. ✅ **Replace EventCardLight type tags** → `<StatusBadge variant="type" />`
3. 🔲 **Replace UpcomingMatchCard status badges** → `<StatusBadge variant="status" />`
4. 🔲 **Replace CommunityCardLight role badges** → `<StatusBadge variant="role" />`
5. 🔲 **Replace CommunityAdmins status indicators** → `<StatusBadge variant="indicator" />`
6. 🔲 **Remove all duplicate badge styles and functions**

## Next Steps

1. **Week 1**: Replace all remaining badge implementations with StatusBadge
2. **Week 2**: Remove all duplicate style definitions and color functions
3. **Week 3**: Add additional status types as needed
4. **Month 2**: Extend with new variants (priority, urgency, etc.) as requirements grow

---

*This component addresses the CRITICAL duplication issue identified in the SWOT analysis and provides a foundation for consistent badge styling across the entire application.* 