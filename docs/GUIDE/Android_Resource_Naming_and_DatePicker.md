# Android Resource Naming & Date-Picker Integration Guide

> Last updated: 2025-07-04

This guide captures the steps we followed when we hit two common Android build errors while working in the Padel Community App.

---

## 1  `RNCDatePicker` Cannot Be Found

**Symptoms**
```
Invariant Violation: TurboModuleRegistry.getEnforcing(...): 'RNCDatePicker' could not be found.
```
The JS bundle loads, but the native `RNCDatePicker` class is missing from the debug APK.

### Fix
1. **Install the Expo-compatible version** of the library (Expo SDK 52 → 8.4.1):
   ```bash
   npx expo install @react-native-community/datetimepicker
   ```
2. **Regenerate native projects** & autolink the module:
   ```bash
   npx expo prebuild --clean    # wipes android/ & ios/ then recreates them
   ```
3. **Clean & rebuild** the Android project:
   ```bash
   cd android && ./gradlew clean && cd ..
   npx expo run:android         # or: npx expo run:ios
   ```
4. **Start Metro with a fresh cache** (optional but recommended):
   ```bash
   expo start -c
   ```

You should now be able to import and use the picker without crashes.

---

## 2  `mergeDebugResources` → *invalid file-based resource name*

Gradle rejects any drawable whose filename contains **uppercase letters, hyphens (-)** or **retina suffixes (@2x / @3x)**.

Example error:
```
ERROR … 'L' is not a valid file-based resource name character
```

### Root Cause
During pre-build Expo copies everything under `assets/**` into
`android/app/src/main/res/drawable`.  If our filenames violate Android
rules the build fails.

### Allowed pattern
```
[a-z0-9_]+
```
(no hyphens, capitals, dots (other than the extension) or @ multipliers)

### Fix Workflow
```bash
# 1.  Rename offending files (examples shown)
git mv assets/icons/caretLeft.png         assets/icons/caret_left.png
# …repeat for @2x / @3x and any other files with ‑ or capital letters

# 2.  Update paths in app.json (icon, adaptiveIcon, splash, web.favicon …)

# 3.  Delete previously copied bad assets
find android/app/src/main/res/drawable \
     \( -name '*@*' -o -name '*-*' -o -name '*[A-Z]*' \) -type f -delete

# 4.  Clean & rebuild
npx expo prebuild --clean
cd android && ./gradlew clean && cd ..
npx expo run:android
```

💡 **Tip:** add this lint rule to catch bad filenames early:
```bash
# .husky/pre-commit or similar
if git diff --cached --name-only | grep -E 'assets/.*[A-Z@-]'; then
  echo "❌  Android-incompatible asset filename detected" && exit 1
fi
```

---

## 3  Naming Convention Cheatsheet
| Asset type              | Good example                       | Bad example                      |
|-------------------------|------------------------------------|----------------------------------|
| Icon / drawable         | `icon_profile.png`                 | `iconProfile.png`                |
| Retina multiplier       | *(avoid copying to Android)*       | `<EMAIL>`                    |
| Adaptive-icon foreground| `app_icon_android_adaptive_fg.png` | `app-icon-android-adaptive.png`  |
| Splash screen           | `splash_bg.png`                    | `splash-background.png`          |

Stick to **lower-snake-case** and you’ll never see the
`mergeDebugResources` error again.

---

**See also**
* <https://developer.android.com/guide/topics/resources/overview>
* Expo docs on [`@react-native-community/datetimepicker`](https://docs.expo.dev/versions/latest/sdk/date-time-picker/)
