# Supabase Service Integration

A high-level checklist for wiring **mobile UI fields** to Supabase tables through a thin service layer. Use it whenever you add a feature that needs to read/write Postgres data.

---

## 1. Database Preparation (Supabase Dashboard)

1. **Table / Columns** – Create or verify the table in **public** schema with the exact columns you plan to bind to UI fields (e.g. `display_name`, `mobile_no`).
2. **Primary Key** – Make the PK match `auth.users.id` (`uuid`) or store your own (`user_id`). Keep naming consistent.
3. **RLS Policies** – Enable RLS and add at least:
   - `SELECT ... USING (user_id = auth.uid())`
   - `UPDATE ... USING (user_id = auth.uid())`
4. **Optional Triggers** – e.g. `last_updated` timestamp trigger.

> Tip: Changes in schema are visible to clients instantly – no rebuild required.

---

## 2. Create a Supabase *Service* (app/services/supabase)

1. **Name** it after the entity `profile-service.ts`, `event-service.ts` …
2. Expose *pure* async methods:
   - `fetchX()` → `select()` with explicit column list.
   - `updateX(dto)` → `update({...}).eq("user_id", id)`.
3. Do **not** import React or MST inside a service.
4. Add dev `console.log` calls during early testing; remove later.

---

## 3. Cache Layer (MobX-State-Tree)

1. Add a store in `app/models/` for the entity.
2. Include it in `RootStore` so it auto-persists to MMKV via `setupRootStore`.
3. Provide `setEntity()` and `clear()` actions only – business logic stays in service.

---

## 4. Wiring Points in the App

| Moment | What to Call | Side Effect |
|--------|--------------|-----------|
| **Login** success | `service.fetchProfile()` → `store.setProfile()` | Initial hydration |
| **Screen Mount** (if data can change elsewhere) | fetch again or rely on store | Fresh data |
| **Save Button** | `service.updateProfile(dto)` → on success `store.setProfile(dto)` | Keep cache & UI in sync |
| **Logout** | `authenticationStore.logout()` + `store.clear()` | Clean slate |

---

## 5. UI Binding

1. **Read-only fields** – Pull directly from MST store via `useStores()`.
2. **Editable forms** –
   - Initialise form state with store values.
   - On save, call service → update store.
3. **Header / Avatar** – Observe store (`observer`) so name/avatar update instantly.

---

## 6. Error Handling & UX

1. Wrap service calls with `safeAsync()` utility – consistent logging & toasts.
2. Show optimistic UI where possible; rollback on error if needed.

---

## 7. Testing Checklist

- [ ] Invalid credentials block login? RLS confirmed.
- [ ] Data persists after app restart (MMKV snapshot).
- [ ] Profile edit reflects in home header without restart.
- [ ] Logout clears local cache.

---

Use this guide as your **reference card** whenever you connect new text fields or forms to Supabase. Keep the service thin, the store focused, and the UI reactive. 