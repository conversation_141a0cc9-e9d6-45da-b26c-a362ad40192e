# Bottom Sheet Component Pattern

This guide explains how to build **feature-specific bottom sheets** using the shared `BaseBottomSheet` wrapper.

---

## 1  BaseBottomSheet

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `visible` | `boolean` | ✅ | – | Controls sheet presentation. |
| `onClose` | `() => void` | ✅ | – | Called after the sheet is dismissed (including swipe-down). |
| `title` | `string` | – | `undefined` | If provided, a header row with title & **X** button is rendered. |
| `snapPoints` | `Array<string\|number>` | – | `["50%"]` | React-Gorhom snap points. Accepts percentage (`"50%"`) or pixel numbers (`450`). |
| `enableDynamicSizing` | `boolean` | – | `false` | Set to `true` if the sheet's height should grow / shrink while scrolling. |
| `children` | `ReactNode` | ✅ | – | Sheet body. You decide whether to use `BottomSheetScrollView`, `FlashList`, etc. |

### Example
```tsx
<BaseBottomSheet
  visible={showRules}
  onClose={() => setShowRules(false)}
  title="Event Rules"
  snapPoints={["75%"]}
>
  <BottomSheetScrollView>
    {...content}
  </BottomSheetScrollView>
</BaseBottomSheet>
```

---

## 2  Feature-level wrappers

### 2.1  `SelectionSheet`
Location: `app/screens/EditProfile/components/SelectionSheet.tsx`

Purpose: single-choice picker used for *Country*, *Gender*, etc.

Additional props:
* `items: { value: string; label: string; flag?: string }[]`
* `onSelect(value: string)`
* `selectedValue?: string`

### 2.2  `InfoSheet`
Location: `app/screens/CreateEvent/components/InfoSheet.tsx`

Purpose: read-only list of formatted rules for an event format.

Additional props:
* `items: string[]` (lines)

Both wrappers **must** expose at least `visible` and `onClose` so they remain interchangeable.

```tsx
<SelectionSheet
  visible={showCountry}
  onClose={() => setShowCountry(false)}
  onSelect={(v) => setCountry(v)}
  items={COUNTRY_CODES}
  title={translate("selectCountry")}
  selectedValue={country}
/>
```

---

## 3  Import conventions

1. **Never** import `@gorhom/bottom-sheet` directly in screen code—wrap inside a sheet component.
2. Re-export feature sheets from their barrel (`components/index.ts`) for clean imports.
3. The shared alias files `app/components/SelectionModal.tsx` and `app/components/InfoModal.tsx` map to the new sheets to avoid breaking legacy imports.

---

## 4  Why `enableDynamicSizing` = false by default?
Dynamic sizing can cause noticeable flickers when the sheet content height changes while the user scrolls. Setting it to `false` results in a more stable experience for most lists. Enable it only when necessary (e.g., auto-expand image gallery).

---

## 5  Checklist when adding a new sheet

- [ ] Does it re-use `BaseBottomSheet`?
- [ ] Are `visible` & `onClose` props present?
- [ ] Have you chosen reasonable `snapPoints`?
- [ ] Is `enableDynamicSizing` justified if set to `true`?
- [ ] Added export in local `components/index.ts` barrel?
- [ ] Added docs/code comments if the sheet has unique behaviour? 