# React Native Screen Refactoring Checklist & Guidelines

*Comprehensive guide for systematic screen refactoring in React Native applications*

## 🎯 **Overview**

This checklist provides a systematic approach to refactoring React Native screens from complex, mixed-responsibility components into modular, maintainable feature-based architectures. Based on successful refactoring of EventDetail (-80% complexity), Home (-60% complexity), and Profile (-34% complexity + data architecture fixes).

---

## 📋 **Pre-Refactoring Assessment**

### **1. Complexity Analysis**
- [ ] **Line Count**: Document current component size
- [ ] **Responsibility Count**: List all responsibilities (data, UI, navigation, etc.)
- [ ] **Dependency Analysis**: Map external dependencies and imports
- [ ] **State Management**: Identify local state, global state, and side effects
- [ ] **Props Drilling**: Document prop passing depth and complexity

### **2. Architecture Analysis**
- [ ] **Component Structure**: Current organization and file structure
- [ ] **Data Flow**: How data flows through the component
- [ ] **Side Effects**: useEffect hooks, API calls, timers, subscriptions
- [ ] **Event Handlers**: Count and complexity of user interaction handlers
- [ ] **Navigation**: How component handles routing and deep linking

### **3. Risk Assessment**
- [ ] **Breaking Changes**: Identify potential breaking changes
- [ ] **Test Coverage**: Document existing tests that may be affected
- [ ] **Dependencies**: Components or screens that depend on this one
- [ ] **Business Critical**: Identify mission-critical functionality
- [ ] **User Impact**: Features that directly affect user experience

---

## 🏗️ **Refactoring Strategy**

### **Phase 1: Architecture Design**

#### **1.1 Directory Structure Planning**
- [ ] **Feature-Based Organization**: Plan `/ScreenName/` directory structure
- [ ] **Component Organization**: Identify reusable vs. screen-specific components
- [ ] **Hook Planning**: Map responsibilities to custom hooks
- [ ] **Utility Planning**: Identify shared logic for utils.ts
- [ ] **Type Planning**: Plan interface organization and sharing

**Standard Structure:**
```
/ScreenName/
├── ScreenNameScreen.tsx        # Main orchestrator
├── types.ts                    # Shared interfaces
├── utils.ts                    # Utilities & business logic
├── hooks/
│   ├── useScreenNameData.ts    # Data management
│   ├── useScreenNameActions.ts # User interactions
│   ├── useScreenNameScroll.ts  # Scroll (if applicable)
│   └── index.ts               # Hook exports
├── components/
│   ├── ScreenComponent1.tsx    # Feature-specific components
│   ├── ScreenComponent2.tsx
│   └── index.ts               # Component exports
└── index.ts                   # Main exports
```

#### **1.2 Responsibility Mapping**
- [ ] **Data Management**: What data is fetched, stored, and managed?
- [ ] **User Interactions**: What actions can users perform?
- [ ] **Navigation**: What screens can be navigated to?
- [ ] **UI State**: What local state affects the UI?
- [ ] **Side Effects**: What external effects need management?

### **Phase 2: Implementation Strategy**

#### **2.1 Hook Extraction Priority**
**High Priority (Do First):**
- [ ] **Data Hook**: `useScreenNameData` - Data fetching and core state
- [ ] **Actions Hook**: `useScreenNameActions` - User interactions and navigation
- [ ] **Scroll Hook**: `useScreenNameScroll` - Scroll management (if needed)

**Medium Priority:**
- [ ] **Form Hook**: `useScreenNameForm` - Form state and validation (if applicable)
- [ ] **Animation Hook**: `useScreenNameAnimation` - Animations (if applicable)

#### **2.2 Utility Extraction Priority**
**High Priority:**
- [ ] **Data Utilities**: API calls, data transformation, validation
- [ ] **Business Logic**: Core business rules and calculations
- [ ] **Helper Functions**: Reusable utility functions

**Medium Priority:**
- [ ] **Formatting**: Date, currency, text formatting
- [ ] **Validation**: Input validation and sanitization

#### **2.3 Component Extraction Strategy**
- [ ] **Identify Single Responsibility**: Each component should have one clear purpose
- [ ] **Extract Reusable Components**: Components that could be used elsewhere
- [ ] **Create Feature Components**: Screen-specific components with clear interfaces
- [ ] **Maintain Component Hierarchy**: Logical parent-child relationships

---

## ⚙️ **Implementation Checklist**

### **Phase 1: Setup & Foundation**

#### **1.1 Directory Structure**
- [ ] Create main screen directory (`/screens/ScreenName/`)
- [ ] Create subdirectories (`/hooks/`, `/components/`, `/utils.ts`, `/types.ts`)
- [ ] Set up index.ts files for clean exports
- [ ] Plan backward compatibility for existing imports

#### **1.2 Type System**
- [ ] Extract existing interfaces to `types.ts`
- [ ] Define hook parameter and return types
- [ ] Define component prop interfaces
- [ ] Ensure TypeScript strict mode compatibility
- [ ] Plan shared vs. local type definitions

#### **1.3 Utility Foundation**
- [ ] Create `utils.ts` with core business logic
- [ ] Extract data fetching functions
- [ ] Extract helper and formatting functions
- [ ] Document utility function purposes and usage
- [ ] Add proper error handling to utilities

### **Phase 2: Hook Creation**

#### **2.1 Data Management Hook**
```typescript
// Template: useScreenNameData.ts
interface UseScreenNameDataProps {
  // Input parameters
}

export const useScreenNameData = (props: UseScreenNameDataProps) => {
  // State management
  // Data fetching
  // Computed values
  // Local UI state
  
  return {
    // Data
    // Loading states
    // Error states
    // UI state
    // State setters
  }
}
```

**Checklist:**
- [ ] Define clear input/output interfaces
- [ ] Handle loading and error states
- [ ] Implement proper data fetching lifecycle
- [ ] Add data transformation logic
- [ ] Include computed/derived state

#### **2.2 Actions Hook**
```typescript
// Template: useScreenNameActions.ts
interface UseScreenNameActionsProps {
  navigation: any
  data: DataType
  // Other dependencies
}

export const useScreenNameActions = (props: UseScreenNameActionsProps) => {
  // Navigation handlers
  // User interaction handlers
  // Form submission handlers
  // Business action handlers
  
  return {
    // Navigation actions
    // User actions
    // Business actions
  }
}
```

**Checklist:**
- [ ] Extract all user interaction handlers
- [ ] Implement navigation logic
- [ ] Add business action handlers
- [ ] Include error handling for actions
- [ ] Add loading states for async actions

#### **2.3 Scroll Management Hook (If Applicable)**
```typescript
// Template: useScreenNameScroll.ts
export const useScreenNameScroll = () => {
  // ScrollView ref management
  // Scroll position tracking
  // Scroll-to-top functionality
  // Focus effect integration
  
  return {
    scrollViewRef,
    scrollToTop,
    // Other scroll utilities
  }
}
```

**Checklist:**
- [ ] Implement ref management
- [ ] Add scroll-to-top functionality
- [ ] Integrate with navigation focus effects
- [ ] Handle scroll state persistence (if needed)

### **Phase 3: Component Organization**

#### **3.1 Component Extraction**
- [ ] **Identify Component Boundaries**: Clear single responsibilities
- [ ] **Extract Header Components**: Screen-specific headers and navigation
- [ ] **Extract Content Components**: Main content areas and sections
- [ ] **Extract Action Components**: Buttons, forms, interactive elements
- [ ] **Extract List Components**: Data display and list management

#### **3.2 Component Interface Design**
```typescript
// Template: Component interface
interface ComponentProps {
  // Required data
  // Optional configuration
  // Event handlers
  // Style overrides (if needed)
}

export const Component: FC<ComponentProps> = (props) => {
  // Component logic
  // Render JSX
}
```

**Checklist:**
- [ ] Define clear prop interfaces
- [ ] Implement proper prop validation
- [ ] Add default props where appropriate
- [ ] Document component purpose and usage
- [ ] Ensure component is pure/predictable

#### **3.3 Component Organization**
- [ ] Move components to `/components/` directory
- [ ] Update import paths throughout application
- [ ] Create component index.ts for exports
- [ ] Verify component isolation and testability
- [ ] Document component relationships

### **Phase 4: Main Screen Refactoring**

#### **4.1 Screen Simplification**
```typescript
// Template: Refactored screen structure
export const ScreenNameScreen: FC<ScreenProps> = ({ navigation, route }) => {
  // Extract parameters
  // Use custom hooks
  // Simple JSX orchestration
  
  const { data, loading, error } = useScreenNameData({ params })
  const { handleAction1, handleAction2 } = useScreenNameActions({ navigation, data })
  const { scrollViewRef } = useScreenNameScroll()
  
  return (
    <Screen>
      <Component1 data={data} onAction={handleAction1} />
      <Component2 ref={scrollViewRef} onAction={handleAction2} />
    </Screen>
  )
}
```

**Checklist:**
- [ ] Remove all inline handlers
- [ ] Replace local state with hook state
- [ ] Use hooks for all side effects
- [ ] Simplify JSX to pure composition
- [ ] Maintain all original functionality

#### **4.2 Integration Testing**
- [ ] Verify navigation works correctly
- [ ] Test all user interactions
- [ ] Validate data loading and error states
- [ ] Confirm scroll functionality (if applicable)
- [ ] Check performance impact

---

## 🔍 **Quality Assurance Checklist**

### **Code Quality**
- [ ] **TypeScript Compilation**: Zero TypeScript errors
- [ ] **Linting**: All ESLint rules pass
- [ ] **Code Formatting**: Consistent with project standards
- [ ] **Import Organization**: Clean, organized imports
- [ ] **Dead Code**: No unused imports or variables

### **Functionality**
- [ ] **Feature Parity**: All original features work
- [ ] **Navigation**: All navigation paths function
- [ ] **User Interactions**: All buttons and forms work
- [ ] **Data Loading**: Loading and error states display correctly
- [ ] **Performance**: No noticeable performance degradation

### **Architecture**
- [ ] **Single Responsibility**: Each component/hook has one purpose
- [ ] **Separation of Concerns**: Data, UI, and business logic separated
- [ ] **Reusability**: Components and hooks are reusable
- [ ] **Testability**: Each part can be tested in isolation
- [ ] **Maintainability**: Code is easy to understand and modify

### **Documentation**
- [ ] **Component Documentation**: Clear prop interfaces and usage
- [ ] **Hook Documentation**: Clear parameter and return documentation
- [ ] **Code Comments**: Complex logic is commented
- [ ] **Type Documentation**: TypeScript interfaces are descriptive
- [ ] **Change Documentation**: Refactoring changes are documented

---

## 🚀 **Advanced Considerations**

### **Data Architecture**
- [ ] **ID Consistency**: Ensure proper unique identifiers across data files
- [ ] **Type Consistency**: Consistent types across related components
- [ ] **Data Validation**: Proper data validation and sanitization
- [ ] **API Integration**: Prepare hooks for real API integration
- [ ] **Error Handling**: Comprehensive error handling strategy

### **Performance Optimization**
- [ ] **Memoization**: Use React.memo, useMemo, useCallback where appropriate
- [ ] **Lazy Loading**: Implement lazy loading for large components
- [ ] **Bundle Optimization**: Avoid unnecessary re-renders
- [ ] **Memory Management**: Proper cleanup of subscriptions and timers
- [ ] **Navigation Optimization**: Efficient navigation state management

### **Testing Strategy**
- [ ] **Unit Tests**: Test hooks and utilities in isolation
- [ ] **Component Tests**: Test component rendering and interactions
- [ ] **Integration Tests**: Test screen-level functionality
- [ ] **E2E Tests**: Test complete user workflows
- [ ] **Mock Strategy**: Proper mocking for external dependencies

### **Future-Proofing**
- [ ] **Scalability**: Architecture supports feature additions
- [ ] **Modularity**: Components can be easily moved or reused
- [ ] **Configuration**: Configurable components and hooks
- [ ] **Internationalization**: Ready for i18n implementation
- [ ] **Theme Support**: Consistent theming throughout

---

## 📊 **Success Metrics**

### **Quantitative Metrics**
- [ ] **Line Count Reduction**: Target 30-80% reduction in main component
- [ ] **Complexity Reduction**: Fewer responsibilities per file
- [ ] **Import Simplification**: Cleaner import statements
- [ ] **File Organization**: Logical file structure achieved
- [ ] **TypeScript Coverage**: 100% TypeScript coverage maintained

### **Qualitative Metrics**
- [ ] **Developer Experience**: Easier to understand and modify
- [ ] **Maintainability**: Changes are isolated to relevant files
- [ ] **Testability**: Components and hooks can be tested independently
- [ ] **Reusability**: Components can be reused across screens
- [ ] **Performance**: No regression in app performance

### **Pattern Consistency**
- [ ] **Architecture Consistency**: Matches established patterns
- [ ] **Naming Conventions**: Consistent naming across project
- [ ] **File Structure**: Follows project standards
- [ ] **Export Patterns**: Clean and consistent exports
- [ ] **Documentation**: Consistent documentation style

---

## 🔄 **Common Pitfalls & Solutions**

### **Pitfall 1: Over-Extraction**
**Problem**: Creating too many small hooks or components
**Solution**: Maintain balance - extract when there's clear single responsibility

### **Pitfall 2: Prop Drilling**
**Problem**: Passing too many props through component hierarchy
**Solution**: Use hooks at the appropriate level, consider context for shared state

### **Pitfall 3: State Management Confusion**
**Problem**: Unclear where state should live
**Solution**: Follow the rule: state lives closest to where it's used by multiple components

### **Pitfall 4: Import Path Chaos**
**Problem**: Confusing or broken import paths after refactoring
**Solution**: Update imports incrementally, use IDE refactoring tools, test frequently

### **Pitfall 5: Type Inconsistency**
**Problem**: Similar interfaces defined in multiple places
**Solution**: Create shared types, establish clear type ownership patterns

### **Pitfall 6: Lost Functionality**
**Problem**: Missing features after refactoring
**Solution**: Create comprehensive functionality checklist before starting

---

## 📚 **Templates & Examples**

### **Hook Template**
```typescript
import { useState, useEffect } from 'react'

interface UseFeatureProps {
  param1: string
  param2?: number
}

interface UseFeatureReturn {
  data: DataType[]
  loading: boolean
  error: string | null
  actions: {
    handleAction: () => void
  }
}

export const useFeature = ({ param1, param2 }: UseFeatureProps): UseFeatureReturn => {
  const [data, setData] = useState<DataType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Implementation

  return {
    data,
    loading,
    error,
    actions: {
      handleAction
    }
  }
}
```

### **Component Template**
```typescript
import { FC } from 'react'
import { View, ViewStyle } from 'react-native'
import { useAppTheme } from '@/utils/useAppTheme'
import type { ThemedStyle } from '@/theme'

interface ComponentProps {
  data: DataType
  onAction: (item: DataType) => void
  loading?: boolean
}

export const Component: FC<ComponentProps> = ({ 
  data, 
  onAction, 
  loading = false 
}) => {
  const { themed } = useAppTheme()

  // Component logic

  return (
    <View style={themed($container)}>
      {/* JSX */}
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  // Styles
})
```

---

## 🎯 **Conclusion**

This checklist provides a systematic approach to refactoring React Native screens. The key is to:

1. **Plan thoroughly** before starting
2. **Extract incrementally** to avoid breaking changes
3. **Test frequently** during the process
4. **Document everything** for future reference
5. **Maintain consistency** with established patterns

Remember: The goal is not just to reduce lines of code, but to create **maintainable, testable, and scalable** architecture that will support future development.

---

**Document Version:** 1.0  
**Created:** January 2025  
**Based on:** EventDetail, Home, and Profile refactoring experiences  
**Next Review:** After next major refactoring project  
**Status:** Ready for use 