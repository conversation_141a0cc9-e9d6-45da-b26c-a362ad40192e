# 🚀 Quick Start: Vector Icons

> **See [VECTOR_ICONS_MIGRATION_GUIDE.md](./VECTOR_ICONS_MIGRATION_GUIDE.md) for the complete detailed guide.**

## ⚡ TL;DR - For New Components

### 1. Basic Usage
```tsx
import { MaterialIcon, FeatherIcon } from "@/components/VectorIcon"

// In your component:
<MaterialIcon name="home" size={24} />
<FeatherIcon name="heart" size={20} color="#FF6B6B" />
```

### 2. Quick Replacements

| Old PNG | New Vector Icon |
|---------|----------------|
| `<Icon icon="settings" />` | `<MaterialIcon name="settings" />` |
| `<Icon icon="bell" />` | `<MaterialIcon name="notifications" />` |
| `<Icon icon="caretLeft" />` | `<MaterialIcon name="arrow-back" />` |
| `<Icon icon="caretRight" />` | `<MaterialIcon name="arrow-forward" />` |
| `<Icon icon="more" />` | `<MaterialIcon name="more-vert" />` |

### 3. Component Template

Use this template for new components:

```tsx
import { MaterialIcon } from "@/components/VectorIcon"

export function MyNewComponent() {
  return (
    <TouchableOpacity>
      <MaterialIcon name="add" size={24} color="#007AFF" />
      <Text>Add Item</Text>
    </TouchableOpacity>
  )
}
```

## 🎯 Icon Libraries

- **MaterialIcon**: Google Material Design icons (most comprehensive)
- **FeatherIcon**: Clean, minimal stroke icons  
- **IonIcon**: iOS-style icons
- **FontAwesomeIcon**: Popular icon collection

## 📱 Essential Icons for Padel App

```tsx
// Navigation
<MaterialIcon name="home" size={24} />
<MaterialIcon name="arrow-back" size={24} />
<MaterialIcon name="menu" size={24} />

// Social  
<MaterialIcon name="notifications" size={24} />
<MaterialIcon name="message" size={24} />
<MaterialIcon name="group" size={24} />

// Actions
<MaterialIcon name="add" size={24} />
<MaterialIcon name="edit" size={24} />
<MaterialIcon name="share" size={24} />

// Sports
<MaterialIcon name="event" size={24} />              // Calendar
<MaterialIcon name="location-on" size={24} />        // Location
<MaterialIcon name="sports-tennis" size={24} />      // Racket
<MaterialIcon name="emoji-events" size={24} />       // Trophy
```

## 🔧 Enable Vector Icons (When Ready)

1. **Uncomment imports** in `app/components/VectorIcon.tsx`:
   ```tsx
   import MaterialIcons from "react-native-vector-icons/MaterialIcons"
   import Feather from "react-native-vector-icons/Feather"
   // etc...
   ```

2. **Replace placeholder function** with actual icon rendering

3. **Test on both platforms** (iOS & Android)

## ⚠️ Current Status

- ✅ **Setup Complete**: Package installed, platforms configured
- 🚧 **Icons Temporarily Disabled**: To prevent app crashes during setup
- 📱 **App Running**: Placeholder components showing 📱 emoji
- 🎯 **Ready to Enable**: Uncomment imports when ready to test

## 🔗 Resources

- [Material Icons](https://fonts.google.com/icons)
- [Feather Icons](https://feathericons.com/)
- [Complete Migration Guide](./VECTOR_ICONS_MIGRATION_GUIDE.md)

---

**Ready to start?** Use vector icons in your next new component! 🎨 