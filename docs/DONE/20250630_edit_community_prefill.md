# Edit Community Prefill Flow (Community → Create Community)

## 📅 Date
2025-06-30

## 🚀 Overview
Implemented a seamless *"Edit Community"* experience by re-using the existing **Create Community** modal. Tapping the settings icon on any Community Detail screen now opens the Create Community flow with the community's current data pre-filled — including members **and** moderators.

## 🔑 Key Features
1. **Settings Icon Navigation**
   • Added `handleSettingsPress` inside `useCommunityActions` to build an `initialData` payload and navigate to `CreateCommunity`.

2. **Navigation Typings**
   • Updated `AppStackParamList` → `CreateCommunity: { initialData?: Partial<CreateCommunityData> }`.

3. **Prefill Mapping**
   • `CommunityData` → `CreateCommunityData` mapping covers:
     – Details (name, description)
     – Settings (privacy, moderation toggle)
     – Members *(community.members ± admins/mods, unique)*
     – Moderators *(adminsAndModerators)*

4. **Moderation Auto-Enable**
   • `moderationEnabled` defaults to **true** when the source community already has moderators.

5. **CreateCommunity Enhancements**
   • `CreateCommunityData` now owns a `moderators` array & setters (`setModerators`).
   • `Settings` step shows a **UserSelector** for moderators when moderation is enabled, with selections pre-populated.

6. **UI Wiring**
   • `CreateCommunityScreen` passes `moderators` + change handler to `Settings`.
   • Dependency array added to `useHeader` to ensure header buttons register once handlers/data are ready.

## 🗂️ Files Touched
• `app/navigators/AppNavigator.tsx`
• `app/screens/CommunityDetail/hooks/useCommunityActions.ts`
• `app/screens/CommunityDetail/CommunityDetailScreen.tsx`
• `app/screens/CreateCommunity/hooks/useCreateCommunityData.ts`
• `app/screens/CreateCommunity/components/Settings/Settings.tsx`
• `app/screens/CreateCommunity/components/Settings/BasicSettings.tsx` *(toggle read only)*
• `app/screens/CreateCommunity/CreateCommunityScreen.tsx`

## ✨ Result
Editing a community is now a fully intuitive flow:
1. Open any community (e.g., **Elite Padel Club**).
2. Tap the **settings** icon in the header.
3. The Create Community modal slides up with all current details, member list, and moderators already filled in.
4. Users can adjust info, switch tabs, or discard safely.

All existing best-practice rules (observer, ThemedStyle, hook separation, etc.) remain intact.

## ♻️ 2025-06-30 Refactor – Service Extraction & Edit Mode

Additional improvements based on post-review:

| Area | Change |
|------|--------|
| **Service Layer** | Introduced `CommunityEditService` to own all *CommunityData ⇄ CreateCommunityData* transformations. |
| **Navigation Params** | `CreateCommunity` stack param extended with `mode?: "create" | "edit"` to distinguish flows. |
| **CommunityDetail** | `useCommunityActions` now delegates to `CommunityEditService.toFormData()` and passes `mode: "edit"`, removing mapping logic from the hook. |
| **CreateCommunityScreen** | Detects `isEditMode` via `route.params.mode` and:<br/>• Overrides final button label to **Save**<br/>• Uses *Edit Community* title on step 1<br/>• Re-uses existing steps & validation. |
| **Data Hook** | No change – still receives `initialData`, now provided by the service. |
| **Documentation** | Current file updated with refactor notes. |
| **i18n** | Added `createCommunityScreen.editCommunityTitle` and reused `common.save`. |

The feature now follows project rules for **single responsibility** and **feature isolation**: UI triggers → navigation hook → service mapping → shared form component.

---
*Committed via AI pair-programming session.* 