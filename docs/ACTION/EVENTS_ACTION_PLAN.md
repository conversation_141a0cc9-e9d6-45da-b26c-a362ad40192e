# Events Screen Action Plan

## 🎯 **Objective**
Refactor the `EventsScreen` to align with the project's latest architectural standards, specifically by implementing internationalization and replacing the manual `Header` component with the `useHeader` hook.

**Context:** The `EventsScreen` currently uses a hardcoded title and a manually implemented `Header`. This action plan will bring it into full compliance with the project's coding standards for consistency and maintainability.

---

## 📊 **Current Assessment: 8/10**

### ✅ **Strong Areas**
- **Hook-based Architecture**: Excellent separation of concerns with `useEventsData`, `useEventsActions`, `useEventsNavigation`, and `useEventsScrollToTop`. ✅
- **Component Composition**: Good use of the `EventsContent` sub-component. ✅
- **Observer Pattern**: Correctly wrapped with `observer` from mobx-react-lite. ✅
- **Theming**: Correctly uses `useAppTheme` for styling. ✅

### ⚠️ **Areas for Improvement**
- **Internationalization**: The header title is hardcoded.
- **Header Implementation**: The `Header` component is used directly, while the project standard is to use the `useHeader` hook for native header configuration.
- **Styling**: The `$headerWrapper`, `$createButtonStyle`, and `$createButtonTextStyle` styles are defined but not used in the component.

### 🔍 **Technical Debt**
- The `triggerEventsScreenScrollToTop` export is a workaround that could be improved with a more robust event system.

---

## 🚀 **Action Items**

### **Phase 1: Core Refactoring**
**Priority: HIGH**

#### 1.1 Implement Internationalization (i18n)
**Description:** Replace the hardcoded header title with a translation key.

```typescript
// Current implementation
<Header
  title="Events"
  ...
/>

// Target implementation
// In useHeader hook
useHeader({
  titleTx: "eventsScreen.title",
  ...
})
```
**TASKS:**
- [ ] Add an `eventsScreen.title` key to `app/i18n/en.ts` with the value "Events".
- [ ] Update the `useHeader` hook call to use the `titleTx` prop.

**Acceptance Criteria:**
- [ ] The header title is sourced from the i18n file.
- [ ] The screen displays the correct title.

#### 1.2 Refactor Header to use `useHeader` Hook
**Description:** Replace the manual `Header` component with the `useHeader` hook to align with the project's standard for native header configuration.

```typescript
// Current implementation
<Header
  title="Events"
  showBackButton={true}
  onBackPress={handleBackPress}
  rightButtons={[...]}
/>

// Target implementation
useHeader({
  titleTx: "eventsScreen.title",
  leftIcon: "back",
  onLeftPress: handleBackPress,
  rightIcon: "menu",
  onRightPress: handleCreatePress,
})
```
**TASKS:**
- [ ] Remove the existing `<Header>` component from the render method.
- [ ] Import the `useHeader` hook from `@/utils/useHeader`.
- [ ] Call `useHeader` with the appropriate configuration for the title, back button, and right-side action button.
- [ ] Remove the unused style definitions (`$headerWrapper`, `$createButtonStyle`, `$createButtonTextStyle`).

**Acceptance Criteria:**
- [ ] The native header is configured by the `useHeader` hook.
- [ ] The header's appearance and functionality remain identical to the previous implementation.
- [ ] Unused styles are removed from the file.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- All new text must use the `tx` prop or `translate` function.
- The `useHeader` hook should be used for all screen headers.

### **File Structure**
```
app/
├── screens/
│   └── Events/
│       ├── EventsScreen.tsx # REFACTORED
│       └── ...
└── i18n/
    └── en.ts                 # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] The screen uses the `useHeader` hook for header configuration.
- [ ] Unused styles have been removed.

### **Quality Assurance**
- [ ] The screen has been tested on both iOS and Android.
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
