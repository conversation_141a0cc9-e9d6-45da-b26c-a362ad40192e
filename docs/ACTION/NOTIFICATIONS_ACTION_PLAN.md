# Notifications Screen Action Plan

## 🎯 **Objective**
Refactor the `NotificationsScreen` to align with the project's architectural standards by unifying the navigation, implementing the `useHeader` hook, internationalizing strings, and simplifying the component's structure.

**Context:** The `NotificationsScreen` currently has platform-specific modal logic, a manually implemented header, and hardcoded strings. This action plan will address these issues to bring the screen into full compliance with the project's coding standards.

---

## 📊 **Current Assessment: 6/10**

### ✅ **Strong Areas**
- **Hook-based Architecture**: Good separation of concerns with `useNotificationsData`, `useNotificationsActions`, and `useNotificationsNavigation`. ✅
- **Component Composition**: The screen is well-structured with sub-components like `NotificationsList` and `NotificationEmptyState`. ✅

### ⚠️ **Areas for Improvement**
- **Platform-Specific Logic**: The use of a `Modal` for Android but not for iOS adds unnecessary complexity.
- **Header Implementation**: The header is manually implemented with `View` and `TouchableOpacity`, which is inconsistent with the project's `Header` component and `useHeader` hook.
- **Internationalization**: Hardcoded strings are used for the header title and "Clear All" button.
- **Component Structure**: The component is not wrapped in an `observer` and has a complex, multi-return structure for its content.
- **State Management**: The `visible` state for the Android modal should be handled by the navigator.

### 🔍 **Technical Debt**
- The loading and error states are empty and do not provide any user feedback.
- The `handleBackPress` and `_navigateBack` functions are redundant.

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural & Logic Refactoring**
**Priority: HIGH**

#### 1.1 Unify Navigation and Remove Platform-Specific Modal
**Description:** Refactor the screen to remove the conditional `Modal` wrapper for Android. The presentation style should be configured in `AppNavigator.tsx`.

**TASKS:**
- [ ] Remove the `Modal` component and all related state (`visible`, `setVisible`) from `NotificationsScreen.tsx`.
- [ ] Update `AppNavigator.tsx` to set the presentation style for the `Notifications` screen to `modal`.
- [ ] Remove the `isIOS` platform check and the conditional rendering logic.
- [ ] Simplify the `handleBackPress` function.

**Acceptance Criteria:**
- [ ] The screen is presented as a modal on both iOS and Android, managed by the navigator.
- [ ] The platform-specific logic is removed from the component.

#### 1.2 Refactor Header to use `useHeader` Hook
**Description:** Replace the manual header implementation with the `useHeader` hook.

**TASKS:**
- [ ] Remove the manual header `View` and its child components.
- [ ] Import and call the `useHeader` hook with the appropriate configuration for the title, close button, and "Clear All" button.

**Acceptance Criteria:**
- [ ] The native header is configured by the `useHeader` hook.
- [ ] The header's appearance and functionality remain identical to the previous implementation.

#### 1.3 Simplify Component Structure
**Description:** Refactor the component to have a single return statement and a `renderContent` function to handle the different states (loading, error, content).

**TASKS:**
- [ ] Create a `renderContent` function to conditionally render the `NotificationsList` or `NotificationEmptyState`.
- [ ] Refactor the main return statement to be a single `Screen` component that calls `renderContent`.
- [ ] Implement proper loading and error states using the `EmptyState` component.

**Acceptance Criteria:**
- [ ] The component has a single, non-redundant render path.
- [ ] The loading and error states provide clear feedback to the user.

### **Phase 2: Polish and Best Practices**
**Priority: MEDIUM**

#### 2.1 Implement Internationalization (i18n)
**Description:** Replace all hardcoded user-facing strings with i18n keys.

**TASKS:**
- [ ] Add new keys to `app/i18n/en.ts` for "Notifications" and "Clear All".
- [ ] Update the `useHeader` hook call to use `titleTx` and the button config to use `tx`.

**Acceptance Criteria:**
- [ ] All user-facing strings are sourced from i18n files.

#### 2.2 Comply with Mandatory Architectural Patterns
**Description:** The `NotificationsScreen` component will be updated to follow the strict architectural rules of the project.

**TASKS:**
- [ ] Wrap the component export with `observer` from `mobx-react-lite`.
- [ ] Use a named function inside the `observer` wrapper: `observer(function NotificationsScreen(props) { ... })`.

**Acceptance Criteria:**
- [ ] The component is wrapped in a named `observer` function.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Navigation presentation styles must be managed in the navigator.
- The `useHeader` hook should be used for all screen headers.
- Components must be wrapped in a named `observer`.

### **File Structure**
```
app/
├── navigators/
│   └── AppNavigator.tsx      # UPDATED
├── screens/
│   └── Notifications/
│       ├── NotificationsScreen.tsx # REFACTORED
│       └── ...
└── i18n/
    └── en.ts                 # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] The platform-specific modal logic is removed.
- [ ] The screen uses the `useHeader` hook.
- [ ] The component structure is simplified.
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] The component is wrapped in a named `observer`.

### **Quality Assurance**
- [ ] The screen's presentation is consistent on both iOS and Android.
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
