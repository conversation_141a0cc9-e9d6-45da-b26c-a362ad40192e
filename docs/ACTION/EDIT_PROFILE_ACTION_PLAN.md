# Edit Profile Screen Action Plan

## 🎯 **Objective**
Refactor the `EditProfileScreen` to unify its presentation logic, remove platform-specific modal handling, and align it with the project's standard navigation patterns.

**Context:** The `EditProfileScreen` currently uses a conditional `Modal` for Android, while relying on the native stack's presentation style for iOS. This creates an inconsistent implementation and adds unnecessary complexity. This action plan will streamline the component to use a single, navigator-driven presentation method.

---

## 📊 **Current Assessment: 6/10**

### ✅ **Strong Areas**
- **Component Composition**: The core UI is cleanly encapsulated in the `EditProfileContent` component. ✅
- **Observer Pattern**: The component correctly uses the `observer` wrapper. ✅
- **Data Handling**: The screen properly receives and processes the `initialProfile` route parameter. ✅

### ⚠️ **Areas for Improvement**
- **Platform-Specific Logic**: The primary issue is the divergent implementation for iOS and Android, which complicates the code and maintenance.
- **State Management**: The `visible` state for the Android modal should be handled by the navigator, not within the component.
- **API Integration**: The profile-saving logic is a `TODO` and needs to be implemented.

### 🔍 **Technical Debt**
- The `onSave` function contains a `console.log` for the saved data and a `console.error` for failures, which should be replaced with a proper API call and user-facing feedback (e.g., a toast message).

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural Refactoring**
**Priority: HIGH**

#### 1.1 Unify Navigation and Remove Platform-Specific Modal
**Description:** Refactor the screen to remove the conditional `Modal` wrapper for Android. The presentation style should be configured in `AppNavigator.tsx` to ensure consistent behavior across both platforms.

```typescript
// Current implementation
if (isIOS) {
  return <EditProfileContent ... />
}
return (
  <Modal visible={visible} ...>
    <EditProfileContent ... />
  </Modal>
)

// Target implementation (in EditProfileScreen.tsx)
return <EditProfileContent ... /> // The screen should only return its content

// In AppNavigator.tsx
// ...
<Stack.Screen
  name="EditProfile"
  component={Screens.EditProfileScreen}
  options={{ presentation: "modal" }}
/>
// ...
```
**TASKS:**
- [ ] Remove the `Modal` component and all related state (`visible`, `setVisible`) from `EditProfileScreen.tsx`.
- [ ] Update `AppNavigator.tsx` to set the presentation style for the `EditProfile` screen to `modal`.
- [ ] Remove the `isIOS` platform check and the conditional rendering logic.
- [ ] Simplify the `handleClose` function, as it no longer needs to manage the modal's visibility.

**Acceptance Criteria:**
- [ ] The screen is presented as a modal on both iOS and Android, managed by the navigator.
- [ ] The platform-specific logic is completely removed from the component.
- [ ] The close button functions correctly on both platforms.

### **Phase 2: API Integration and Polish**
**Priority: MEDIUM**

#### 2.1 Implement Profile Update API Call
**Description:** Replace the placeholder `console.log` in the `handleSave` function with an actual API call to update the user's profile.

**TASKS:**
- [ ] Create a new service or use an existing one (e.g., `UserService`) to handle the API request.
- [ ] Implement the `updateProfile` method in the service, which will send the `updatedProfile` data to the backend.
- [ ] Replace the `console.log` in `handleSave` with a call to the new service method.
- [ ] Implement proper loading and error handling for the API call (e.g., disable the save button while saving, show a toast message on success or failure).

**Acceptance Criteria:**
- [ ] The `handleSave` function makes an API call to update the profile.
- [ ] The UI provides feedback to the user during and after the save operation.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Navigation presentation styles must be managed in the navigator, not in individual screens.
- API calls should be handled within the service layer, not directly in screen components.

### **File Structure**
```
app/
├── navigators/
│   └── AppNavigator.tsx      # UPDATED
├── screens/
│   └── EditProfile/
│       ├── EditProfileScreen.tsx # REFACTORED
│       └── ...
└── services/
    └── UserService.ts        # NEW or UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] The platform-specific modal logic is removed.
- [ ] The profile update functionality is fully implemented with a real API call.

### **Quality Assurance**
- [ ] The screen's presentation is consistent on both iOS and Android.
- [ ] Saving the profile works as expected, with appropriate user feedback.
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
