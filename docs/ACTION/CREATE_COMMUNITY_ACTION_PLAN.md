# Create Community Screen Action Plan

## 🎯 **Objective**
Refactor the `CreateCommunityScreen` to improve its structure, align with architectural best practices, implement internationalization, and enhance maintainability.

**Context:** The `CreateCommunityScreen` is a multi-step modal for creating communities. While functional, its implementation can be simplified and brought into closer alignment with the project's coding standards, particularly regarding platform-specific logic and i18n.

---

## 📊 **Current Assessment: 7/10**

### ✅ **Strong Areas**
- **Multi-step Flow**: The screen successfully implements a three-step process with a progress bar. ✅
- **Hook-based Architecture**: Good use of hooks for data and actions. ✅
- **Component Composition**: The screen is well-composed from smaller components for each step. ✅

### ⚠️ **Areas for Improvement**
- **Platform-specific Logic**: The use of a `Modal` for Android but not for iOS adds complexity and can lead to inconsistencies. The navigation should handle the presentation style.
- **Internationalization**: Hardcoded strings are used for titles, button labels, and success messages.
- **State Management**: The `visible` state for the Android modal is managed within the component, which should be handled by the navigator.
- **Hardcoded Values**: The `isValid` prop in `useCreateCommunityActions` is hardcoded to `true`.

### 🔍 **Technical Debt**
- The `Success` modal's "View" button has an empty `onPress` handler.
- The `CreateCommunityFormData` mapping in the `useCreateCommunityActions` hook is complex and could be simplified.

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural & Logic Refactoring**
**Priority: HIGH**

#### 1.1 Unify Navigation and Remove Platform-Specific Modal
**Description:** Refactor the screen to remove the conditional `Modal` wrapper for Android. The presentation style should be configured in the `AppNavigator` to ensure consistent behavior across both platforms.

```typescript
// Current implementation
if (isIOS) return content
return (
  <Modal visible={visible} ...>
    {content}
  </Modal>
)

// Target implementation (in CreateCommunityScreen.tsx)
return content // The screen should only return its content

// In AppNavigator.tsx
// ...
<Stack.Screen
  name="CreateCommunity"
  component={Screens.CreateCommunityScreen}
  options={{ presentation: "modal" }} // Or "card" with custom animation
/>
// ...
```
**TASKS:**
- [ ] Remove the `Modal` component and all related state (`visible`, `setVisible`) from `CreateCommunityScreen.tsx`.
- [ ] Update the `AppNavigator.tsx` to set the presentation style for the `CreateCommunity` screen to `modal` or an equivalent.
- [ ] Remove the `isIOS` platform check related to the modal.

**Acceptance Criteria:**
- [ ] The screen is presented as a modal on both iOS and Android, managed by the navigator.
- [ ] The platform-specific logic is removed from the component.

#### 1.2 Implement Internationalization (i18n)
**Description:** Replace all hardcoded user-facing strings with i18n keys.

**TASKS:**
- [ ] Add new keys to `app/i18n/en.ts` for step titles, button labels, and success modal content.
- [ ] Replace hardcoded `text` and `title` props with `tx` and `titleTx` props or the `translate` function.
- [ ] Update the `Success` component to accept `tx` props for its buttons.

**Acceptance Criteria:**
- [ ] All user-facing strings are sourced from i18n files.
- [ ] The screen's text content is fully internationalized.

### **Phase 2: Polish and Best Practices**
**Priority: MEDIUM**

#### 2.1 Refactor Form Data Handling
**Description:** Simplify the form data mapping and validation logic.

**TASKS:**
- [ ] Implement actual form validation instead of hardcoding `isValid: true`.
- [ ] Simplify the mapping of form data within the `useCreateCommunityActions` hook. Consider creating a utility function for this.

**Acceptance Criteria:**
- [ ] The "Next" button is disabled when the form is invalid.
- [ ] The form data is correctly structured and validated before submission.

#### 2.2 Complete Success Modal Functionality
**Description:** Implement the `onPress` handler for the "View" button in the `Success` modal.

**TASKS:**
- [ ] The `useCreateCommunityActions` hook should return the newly created community's ID upon success.
- [ ] The `onPress` handler for the "View" button should navigate to the `CommunityDetail` screen with the new community's ID.

**Acceptance Criteria:**
- [ ] The "View" button in the success modal navigates the user to the detail screen of the community they just created.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Navigation presentation styles should be managed in the navigator, not in individual screens.
- All new text must use the `tx` prop or `translate` function.

### **File Structure**
```
app/
├── navigators/
│   └── AppNavigator.tsx      # UPDATED
├── screens/
│   └── CreateCommunity/
│       ├── CreateCommunityScreen.tsx # REFACTORED
│       └── ...
└── i18n/
    └── en.ts                 # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] The platform-specific modal logic is removed.
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] Form validation is implemented correctly.
- [ ] The success modal's "View" button is functional.

### **Quality Assurance**
- [ ] The screen's presentation is consistent on both iOS and Android.
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
