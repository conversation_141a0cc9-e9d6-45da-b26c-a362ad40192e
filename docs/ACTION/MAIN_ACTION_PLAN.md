# Main Screen Action Plan

## 🎯 **Objective**
Refactor the `MainScreen` to improve data handling, centralize business logic, and align with the project's architectural standards.

**Context:** The `MainScreen` currently fetches and processes some of its data directly within the component. This action plan will move this logic into the appropriate hooks for better separation of concerns and maintainability.

---

## 📊 **Current Assessment: 8/10**

### ✅ **Strong Areas**
- **Hook-based Architecture**: Good use of `useMainData`, `useMainNavigation`, and `useMainScrollToTop`. ✅
- **Component Composition**: The screen is well-structured with a clear hierarchy of sub-components (`MainHeader`, `UpcomingMatches`, etc.). ✅
- **Observer Pattern**: Correctly wrapped with `observer` from mobx-react-lite. ✅

### ⚠️ **Areas for Improvement**
- **Data Fetching**: The logic for fetching and processing upcoming matches is implemented directly in the `MainScreen` component. This should be moved to the `useMainData` hook.
- **Business Logic**: The `handleMatchPress` function is defined in the component, but it should be part of the `useMainNavigation` or a new `useMainActions` hook.

### 🔍 **Technical Debt**
- The `triggerMainScreenScrollToTop` export is a workaround that could be improved with a more robust event system.
- Requiring `events.json` directly in the component is not ideal and can affect performance. This should be handled within a service or data hook.

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural & Logic Refactoring**
**Priority: HIGH**

#### 1.1 Centralize Data Fetching in `useMainData` Hook
**Description:** Move the logic for fetching and processing the `upcomingMatches` data from the `MainScreen` component to the `useMainData` hook.

```typescript
// Current implementation (in MainScreen.tsx)
const upcomingMatches: UpcomingMatch[] = (() => {
  const eventsData = require("@/data/events.json")
  // ...
})()

// Target implementation (in hooks/useMainData.ts)
export const useMainData = () => {
  // ...
  const upcomingMatches = useMemo(() => {
    const eventsData = require("@/data/events.json")
    // ...
    return shuffled.slice(0, 2).map(...)
  }, [])

  return { ..., upcomingMatches }
}
```
**TASKS:**
- [ ] Open `app/screens/Main/hooks/useMainData.ts`.
- [ ] Move the `upcomingMatches` logic into the `useMainData` hook.
- [ ] Wrap the logic in a `useMemo` to prevent unnecessary re-calculations.
- [ ] Return `upcomingMatches` from the hook.
- [ ] Update `MainScreen.tsx` to get `upcomingMatches` from the `useMainData` hook.

**Acceptance Criteria:**
- [ ] The data fetching logic is removed from the `MainScreen` component.
- [ ] The `upcomingMatches` data is provided by the `useMainData` hook.

#### 1.2 Centralize Business Logic in Hooks
**Description:** Move the `handleMatchPress` function from the `MainScreen` component to the `useMainNavigation` hook.

```typescript
// Current implementation (in MainScreen.tsx)
const handleMatchPress = (match: UpcomingMatch) => {
  props.navigation.navigate("EventDetail", { eventId: match.id })
}

// Target implementation (in hooks/useMainNavigation.ts)
export const useMainNavigation = ({ navigation }) => {
  // ...
  const handleMatchPress = (match: UpcomingMatch) => {
    navigation.navigate("EventDetail", { eventId: match.id })
  }

  return { ..., handleMatchPress }
}
```
**TASKS:**
- [ ] Open `app/screens/Main/hooks/useMainNavigation.ts`.
- [ ] Move the `handleMatchPress` function into the `useMainNavigation` hook.
- [ ] Return `handleMatchPress` from the hook.
- [ ] Update `MainScreen.tsx` to get `handleMatchPress` from the `useMainNavigation` hook.

**Acceptance Criteria:**
- [ ] The `handleMatchPress` function is removed from the `MainScreen` component.
- [ ] The `handleMatchPress` function is provided by the `useMainNavigation` hook.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Data fetching and processing logic should reside in data hooks (e.g., `use...Data`).
- Navigation and action logic should reside in navigation or action hooks.

### **File Structure**
```
app/
└── screens/
    └── Main/
        ├── MainScreen.tsx # REFACTORED
        └── hooks/
            ├── useMainData.ts       # UPDATED
            └── useMainNavigation.ts # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] All data fetching logic is centralized in the `useMainData` hook.
- [ ] All business and navigation logic is centralized in the appropriate hooks.

### **Quality Assurance**
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
