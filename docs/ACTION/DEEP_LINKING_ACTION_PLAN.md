# Padel Community App Deep Linking Action Plan

## 🎯 **Objective**
Enable robust deep linking for the Padel Community App so that users can open:
1. A specific Community (`communityId`) → `CommunityDetailScreen`
2. A specific Event (`eventId`) → `EventDetailScreen`
3. A specific User Profile (`profileId`) → `ProfileScreen`

**Context:**
Currently the app uses React Navigation with manual stack definitions but *no* linking configuration. Marketing links, push notifications, and universal links cannot route users directly to in-app content. Implementing deep linking will improve engagement, shareability, and push-notification UX.

---

## 📊 **Current Assessment: 6/10**

### ✅ **Strong Areas**
- Well-structured navigation (`AppNavigator.tsx`) with typed params
- Screen components already accept the required IDs through `route.params`
- Consistent barrel export pattern simplifies screen imports ✅
- JSON fixtures (`communities.json`, `events.json`, `user_data.json`) provide predictable data ✅
- Themed UI and state management are decoupled from navigation ✅

### ⚠️ **Areas for Improvement**
- No `linking` prop on `<NavigationContainer>`
- No configured URI scheme / universal links in `app.json`
- Lacking helpers to generate links in-app (e.g., Share button)
- Push-notification payloads do not contain link targets

### 🔍 **Technical Debt**
- `navigationUtilities.ts` manually handles back button & exit routes; will need refactor for linking
- Some screens create modals via stack options—must ensure path mapping still works
- Unit tests do not cover navigation params parsing

---

## 🚀 **Action Items**

### **Phase 1: Linking Infrastructure**
**Priority: HIGH**

#### 1.1 Add URL Scheme & Universal Links
**Description:** Configure scheme `padel` and HTTPS domain `https://padelcommunity.app`.

```json
// app.json (excerpt)
{
  "expo": {
    "scheme": "padel",
    "ios": { "bundleIdentifier": "com.padelcommunity.app" },
    "android": { "package": "com.padelcommunity.app" },
    "platforms": ["ios", "android"],
    "extra": { "eas": { "projectId": "<uuid>" } }
  }
}
```
**TASKS:**
- [ ] Register `padel://` scheme for both platforms in **app.json**
- [ ] Configure Associated Domains (iOS) & Asset Links (Android) for `padelcommunity.app`

**Acceptance Criteria:**
- [ ] `xcrun simctl openurl` opens app via `padel://test`
- [ ] HTTPS link launches app when installed

#### 1.2 Define Linking Config in Navigation
**Description:** Map paths to stack routes.

```typescript
const linking = {
  prefixes: ["padel://", "https://padelcommunity.app"],
  config: {
    screens: {
      CommunityDetail: "community/:communityId",
      EventDetail: "event/:eventId",
      Profile: "profile/:userId",
      // fallbacks
      Main: "main",
    },
  },
}
```
**TASKS:**
- [ ] Create `navigation/linkingConfig.ts`
- [ ] Inject `linking` into `<NavigationContainer>`

**Acceptance Criteria:**
- [ ] App navigates to correct screen when opened with matching URL

#### 1.3 Analytics / Logging Hook
**Description:** Track deep link openings for analytics.

**Steps:**
1. Create `useLinkAnalytics.ts` hook
2. Log link source & params via existing `crashReporting.ts`
3. Integrate in `AppNavigator`

**Dependencies:** none

### **Phase 2: Screen-Level Enhancements**
**Priority: HIGH**

#### 2.1 Community Share Link
**Description:** Generate sharable URL in `useCommunityActions`.

**Steps:**
1. Add helper `createCommunityLink(communityId)`
2. Replace hardcoded share content
3. Unit test URL output

#### 2.2 Event Share Link
**Description:** Same for events via `useEventActions`.

#### 2.3 Profile Share Link
**Description:** Implement in `useProfileActions`.

**Dependencies:** 1.2 (linking config)

#### 2.4 Fallback Handling
**Description:** Show graceful UI when deep-linked ID not found.

**Risk Assessment:**
- **Medium Risk:** Invalid IDs → blank screens → Mitigation: validate & show `EmptyState`

### **Phase 3: Testing & QA**
**Priority: MEDIUM**

#### 3.1 Unit Tests for Linking Parser
**Description:** Use jest + `@react-navigation/native` helpers.

**Steps:**
1. Mock `Linking.openURL`
2. Assert navigation state after link

#### 3.2 E2E Tests with Maestro
**Description:** Simulate opening links on device.

```yaml
- openLink: "padel://community/A56DG"
- assertVisible: "Elite Padel Club"
```

#### 3.3 Regression Pass
**Description:** Full smoke test on iOS & Android physical devices

### **Phase 4: Documentation & Release**
**Priority: LOW**

#### 4.1 Update README & docs/GUIDE/DeepLinking.md
#### 4.2 Add changelog entry

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Keep `linkingConfig.ts` under `app/navigators/` for discoverability
- Use TypeScript enums for route paths

### **File Structure**
```
app/
  navigators/
    AppNavigator.tsx
    linkingConfig.ts  <-- new
  utils/
    shareLinks.ts     <-- createCommunityLink, createEventLink, createProfileLink
```

### **Testing Strategy**
- Jest for unit parsing
- Maestro for device deep-link testing

### **Performance Considerations**
- Lazy-load heavy screens to improve cold-start deep-link time

### **Security Considerations**
- Validate IDs (regex `[A-Z0-9]+`) to avoid injection

---

## 🔄 **Migration Strategy**

### **Incremental Approach**
1. Ship scheme + navigation config behind feature flag `enableDeepLinking`
2. Enable links in marketing emails as beta
3. Roll out universal links after QA

### **Rollback Plan**
- Disable flag to revert to non-linking build

---

### **Critical Path**
- 1.1 → 1.2 → 2.x → 3.x → 4.x

---

## 🛠 **Resources & Dependencies**
- Expo docs: https://docs.expo.dev/guides/deep-linking/
- React Navigation linking: https://reactnavigation.org/docs/deep-linking

---

## 🚨 **Risk Assessment**

### **High Risk Items**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Incorrect universal link config | Broken App Store links | Medium | Test on TestFlight & Play-Internal |

### **Medium Risk Items**
| Risk | Impact | Probability | Mitigation | Owner |
|------|--------|-------------|------------|-------|
| Invalid params | User sees error screen | Medium | Validate & show `EmptyState` | Dev |

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] Schemes & domains configured
- [ ] Linking config in navigator
- [ ] Share helpers implemented
- [ ] Screens display correct data from links

### **Quality Assurance**
- [ ] All linking unit & E2E tests pass
- [ ] Manual tests on real devices

### **Documentation**
- [ ] README & DeepLinking guide updated

### **Production Ready**
- [ ] Deployed to staging with feature flag on
- [ ] Stakeholder approval

---

## 📚 **References & Resources**
- Expo: Deep Linking & Universal Links
- React Navigation: Linking guide

---

**Template Version:** 1.0  
**Created:** 2025-07-07  
**Last Updated:** 2025-07-07  
**Template Author:** AI Assistant 