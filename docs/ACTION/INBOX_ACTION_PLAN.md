# Inbox Screen Action Plan

## 🎯 **Objective**
Refactor the `InboxScreen` to align with the project's latest architectural standards by implementing the `useHeader` hook and internationalizing all user-facing strings.

**Context:** The `InboxScreen` currently uses a manual `Header` component and a hardcoded title. This action plan will bring it into full compliance with the project's coding standards for consistency and maintainability.

---

## 📊 **Current Assessment: 7/10**

### ✅ **Strong Areas**
- **Component Composition**: Good use of sub-components for tabs (`InboxTabs`, `MessageTab`, `InviteTab`). ✅
- **Observer Pattern**: Correctly wrapped with `observer` from mobx-react-lite. ✅
- **Theming**: Correctly uses `useAppTheme` for styling. ✅

### ⚠️ **Areas for Improvement**
- **Internationalization**: The header title is hardcoded.
- **Header Implementation**: The `Header` component is used directly instead of the `useHeader` hook.
- **Hook-based Architecture**: The screen is not using the standard hook-based architecture for actions and navigation.

### 🔍 **Technical Debt**
- The `renderActiveTabContent` function uses a switch statement, which is acceptable for this small number of tabs but could be refactored into a more scalable pattern if more tabs are added in the future.

---

## 🚀 **Action Items**

### **Phase 1: Core Refactoring**
**Priority: HIGH**

#### 1.1 Implement Internationalization (i18n)
**Description:** Replace the hardcoded header title with a translation key.

```typescript
// Current implementation
<Header
  title="Inbox"
  ...
/>

// Target implementation
// In useHeader hook
useHeader({
  titleTx: "inboxScreen.title",
  ...
})
```
**TASKS:**
- [ ] Add an `inboxScreen.title` key to `app/i18n/en.ts` with the value "Inbox".
- [ ] Update the `useHeader` hook call to use the `titleTx` prop.

**Acceptance Criteria:**
- [ ] The header title is sourced from the i18n file.
- [ ] The screen displays the correct title.

#### 1.2 Refactor Header to use `useHeader` Hook
**Description:** Replace the manual `Header` component with the `useHeader` hook to align with the project's standard for native header configuration.

```typescript
// Current implementation
<Header
  title="Inbox"
  showBackButton={true}
  onBackPress={navigation.goBack}
/>

// Target implementation
useHeader({
  titleTx: "inboxScreen.title",
  leftIcon: "back",
  onLeftPress: navigation.goBack,
})
```
**TASKS:**
- [ ] Remove the existing `<Header>` component from the render method.
- [ ] Import the `useHeader` hook from `@/utils/useHeader`.
- [ ] Call `useHeader` with the appropriate configuration for the title and back button.

**Acceptance Criteria:**
- [ ] The native header is configured by the `useHeader` hook.
- [ ] The header's appearance and functionality remain identical to the previous implementation.

#### 1.3 Implement Standard Hooks
**Description:** Create and implement the standard `useInboxActions` and `useInboxNavigation` hooks to better separate concerns.

**TASKS:**
- [ ] Create `useInboxActions.ts` and `useInboxNavigation.ts` in the `app/screens/Inbox/hooks/` directory.
- [ ] Move any business logic (if any is added in the future) to `useInboxActions`.
- [ ] Move navigation logic (like `navigation.goBack`) into `useInboxNavigation`.

**Acceptance Criteria:**
- [ ] The screen uses the standard set of hooks for actions and navigation.
- [ ] The code is better organized and follows the project's architectural patterns.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- All new text must use the `tx` prop or `translate` function.
- The `useHeader` hook should be used for all screen headers.

### **File Structure**
```
app/
├── screens/
│   └── Inbox/
│       ├── InboxScreen.tsx # REFACTORED
│       └── hooks/
│           ├── useInboxActions.ts    # NEW
│           └── useInboxNavigation.ts # NEW
└── i18n/
    └── en.ts                 # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] The screen uses the `useHeader` hook for header configuration.
- [ ] The screen uses the standard set of hooks for actions and navigation.

### **Quality Assurance**
- [ ] The screen has been tested on both iOS and Android.
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
