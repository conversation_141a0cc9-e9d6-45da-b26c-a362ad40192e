# Profile Screen Action Plan

## 🎯 **Objective**
Refactor the `ProfileScreen` to align with the project's latest architectural standards by implementing the `useHeader` hook, internationalizing all user-facing strings, and improving the separation of concerns between hooks.

**Context:** The `ProfileScreen` is a key component with complex logic for displaying user profiles. The current implementation can be improved by adopting the `useHeader` hook, centralizing navigation logic, and ensuring all text is internationalized.

---

## 📊 **Current Assessment: 7/10**

### ✅ **Strong Areas**
- **Hook-based Architecture**: Excellent separation of concerns with a comprehensive set of hooks for data, actions, UI, and navigation. ✅
- **Component Composition**: The screen is well-structured with sub-components like `ProfileHeader`, `ProfileList`, and `SettingsPopup`. ✅
- **Observer Pattern**: Correctly wrapped with `observer` from mobx-react-lite. ✅

### ⚠️ **Areas for Improvement**
- **Header Implementation**: The `Header` component is used directly instead of the `useHeader` hook.
- **Internationalization**: The header title is hardcoded.
- **Business Logic in Component**: The `handleMessagePress` function contains navigation logic and should be moved to the `useProfileNavigation` hook.
- **Type Safety**: The `userId` is extracted from route params using `(route as any)`, which is not type-safe.

### 🔍 **Technical Debt**
- The `onEditProfileOpen` callback within `useProfileActions` contains navigation logic that should be in `useProfileNavigation`.
- The `triggerProfileScreenScrollToTop` export is a workaround that could be improved.

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural & Logic Refactoring**
**Priority: HIGH**

#### 1.1 Refactor Header to use `useHeader` Hook
**Description:** Replace the manual `Header` component with the `useHeader` hook for native header configuration.

```typescript
// Current implementation
<Header
  title="Profile"
  showBackButton={true}
  onBackPress={handleBackPress}
  rightButtons={[...]}
/>

// Target implementation
useHeader({
  titleTx: "profileScreen.title",
  leftIcon: "back",
  onLeftPress: handleBackPress,
  rightIcons: ["share", ...(isCurrentUser ? ["settings"] : [])],
  onRightPress: (icon) => {
    if (icon === "share") handleSharePress()
    if (icon === "settings") handleSettingsButton()
  },
})
```
**TASKS:**
- [ ] Remove the existing `<Header>` component from the render method.
- [ ] Import the `useHeader` hook from `@/utils/useHeader`.
- [ ] Call `useHeader` with the appropriate configuration for the title, back button, and right-side action buttons.

**Acceptance Criteria:**
- [ ] The native header is configured by the `useHeader` hook.
- [ ] The header's appearance and functionality remain identical to the previous implementation.

#### 1.2 Centralize Navigation Logic
**Description:** Move the `handleMessagePress` function and the `onEditProfileOpen` navigation logic to the `useProfileNavigation` hook.

**TASKS:**
- [ ] Move the `handleMessagePress` function from `ProfileScreen.tsx` to `useProfileNavigation.ts`.
- [ ] In `useProfileActions.ts`, replace the `onEditProfileOpen` callback with a call to a new function in `useProfileNavigation.ts` that handles the navigation to the `EditProfile` screen.

**Acceptance Criteria:**
- [ ] The `ProfileScreen` component no longer contains navigation logic.
- [ ] All navigation actions are handled by the `useProfileNavigation` hook.

### **Phase 2: Polish and Best Practices**
**Priority: MEDIUM**

#### 2.1 Implement Internationalization (i18n)
**Description:** Replace all hardcoded user-facing strings with i18n keys.

**TASKS:**
- [ ] Add a new key to `app/i18n/en.ts` for the "Profile" title.
- [ ] Update the `useHeader` hook call to use `titleTx`.

**Acceptance Criteria:**
- [ ] All hardcoded user-facing strings are replaced with i18n keys.

#### 2.2 Improve Type Safety
**Description:** Remove the `any` cast when accessing `route.params`.

**TASKS:**
- [ ] Ensure the `ProfileScreenProps` correctly type the `route` prop so that `userId` can be accessed without casting. This may involve updating the navigator's param list.

**Acceptance Criteria:**
- [ ] The `(route as any)` cast is removed, and `userId` is accessed in a type-safe manner.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- The `useHeader` hook should be used for all screen headers.
- Navigation logic must be centralized in navigation hooks.
- All new text must use the `tx` prop or `translate` function.

### **File Structure**
```
app/
├── screens/
│   └── Profile/
│       ├── ProfileScreen.tsx # REFACTORED
│       └── hooks/
│           ├── useProfileActions.ts    # UPDATED
│           └── useProfileNavigation.ts # UPDATED
└── i18n/
    └── en.ts                 # UPDATED
```

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] The screen uses the `useHeader` hook.
- [ ] All navigation logic is centralized in the `useProfileNavigation` hook.
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] Route params are accessed in a type-safe manner.

### **Quality Assurance**
- [ ] No regressions in functionality have been introduced.
- [ ] The code adheres to all project coding rules.
