# Private Chat Screen Action Plan

## 🎯 **Objective**
Refactor the `PrivateChatScreen` to align with the project's mandatory architectural standards, improve code structure, and enhance maintainability. This includes extracting keyboard logic, simplifying render paths, ensuring compliance with MobX and i18n patterns, and improving overall component architecture.

**Context:** The current `PrivateChatScreen` implementation contains several deviations from the established project architecture, such as direct keyboard event handling, redundant layout code, and missing internationalization. This action plan outlines the steps to bring it into full compliance.

---

## 📊 **Current Assessment: 6/10**

### ✅ **Strong Areas**
- **Hook-based Architecture**: Good separation of concerns with `usePrivateChatData`, `usePrivateChatActions`, and `usePrivateChatNavigation` hooks. ✅
- **Theming**: Correctly uses `useAppTheme` for consistent styling. ✅
- **Component Reusability**: Leverages existing components like `Screen`, `Header`, and `EmptyState`. ✅
- **State Handling**: Clear separation of loading and error states. ✅

### ⚠️ **Areas for Improvement**
- **Keyboard Handling**: Logic is implemented directly in the component instead of a reusable hook.
- **Redundant JSX**: The `Screen` and `Header` components are duplicated across the loading, error, and main render blocks.
- **Architectural Compliance**: The component is missing the mandatory `observer` wrapper and named function for debugging.
- **Internationalization**: Hardcoded strings are used instead of `tx` props.
- **Layout Management**: Keyboard avoidance is handled with basic padding, which can be fragile.

### 🔍 **Technical Debt**
- **`any` type for navigation**: The `UsePrivateChatActionsProps` interface uses `any` for the navigation prop.
- **Non-functional "Try Again" button**: The error state's recovery action is an empty function.
- **Inconsistent import style**: Type-only imports are not consistently grouped.

---

## 🚀 **Action Items**

### **Phase 1: Core Architectural & Logic Refactoring**
**Priority: HIGH**

#### 1.1 Extract Keyboard Handling to a Custom Hook
**Description:** The keyboard event listeners and state management logic will be extracted from `PrivateChatScreen.tsx` into a new reusable hook `app/hooks/useKeyboard.ts`. This centralizes the logic and makes it available to other components.

```typescript
// Current implementation (in PrivateChatScreen.tsx)
useEffect(() => {
  const keyboardDidShowListener = Keyboard.addListener(
    Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
    (e) => {
      setKeyboardHeight(e.endCoordinates.height)
      setIsKeyboardVisible(true)
    },
  )
  // ... hide listener
  return () => {
    keyboardDidShowListener?.remove()
    // ...
  }
}, [])

// Target implementation (in app/hooks/useKeyboard.ts)
import { useEffect, useState } from "react"
import { Keyboard, Platform } from "react-native"

export const useKeyboard = () => {
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)

  useEffect(() => {
    const showEvent = Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow"
    const hideEvent = Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide"

    const keyboardDidShowListener = Keyboard.addListener(showEvent, (e) => {
      setKeyboardHeight(e.endCoordinates.height)
      setIsKeyboardVisible(true)
    })

    const keyboardDidHideListener = Keyboard.addListener(hideEvent, () => {
      setKeyboardHeight(0)
      setIsKeyboardVisible(false)
    })

    return () => {
      keyboardDidShowListener?.remove()
      keyboardDidHideListener?.remove()
    }
  }, [])

  return { keyboardHeight, isKeyboardVisible }
}

// In PrivateChatScreen.tsx
// const { keyboardHeight, isKeyboardVisible } = useKeyboard()
```
**TASKS:**
- [ ] Create `app/hooks/useKeyboard.ts`.
- [ ] Move the keyboard-related `useEffect` and `useState` calls from `PrivateChatScreen.tsx` to `useKeyboard.ts`.
- [ ] Export `keyboardHeight` and `isKeyboardVisible` from the new hook.
- [ ] Replace the original implementation in `PrivateChatScreen.tsx` with a call to `useKeyboard()`.

**Acceptance Criteria:**
- [ ] The `useKeyboard` hook is created and functional.
- [ ] `PrivateChatScreen` behaves identically to before the refactor regarding keyboard handling.
- [ ] The new hook is reusable and contains no component-specific logic.

#### 1.2 Unify Screen Layout and Remove Redundancy
**Description:** The `PrivateChatScreen` will be refactored to have a single `Screen` and `Header` component, with the content area (`ChatMessagesList`, `Loading`, `Error`) being rendered conditionally.

```typescript
// Current implementation
if (loading) {
  return (
    <Screen ...>
      <Header ... />
      <LoadingState />
    </Screen>
  )
}
if (error) {
  return (
    <Screen ...>
      <Header ... />
      <ErrorState />
    </Screen>
  )
}
return (
  <Screen ...>
    <Header ... />
    <ChatContent />
  </Screen>
)

// Target implementation
const renderContent = () => {
  if (loading) return <LoadingState />
  if (error) return <ErrorState onRetry={...} />
  return <ChatMessagesList ... />
}

return (
  <Screen ...>
    <Header ... />
    <View style={$chatContainer}>
      {renderContent()}
    </View>
    <ChatInputContainer ... />
  </Screen>
)
```

**TASKS:**
- [ ] Create a new function `renderContent` inside `PrivateChatScreen`.
- [ ] Move the `loading`, `error`, and `success` view logic into the `renderContent` function.
- [ ] Refactor the main return statement to render the `Screen`, `Header`, and `ChatInputContainer` unconditionally, calling `renderContent` for the main view.

**Acceptance Criteria:**
- [ ] The `Screen` and `Header` components are rendered only once.
- [ ] The loading, error, and chat list states are rendered correctly based on the `usePrivateChatData` hook's status.

#### 1.3 Comply with Mandatory Architectural Patterns
**Description:** The `PrivateChatScreen` component will be updated to follow the strict architectural rules of the project.

**TASKS:**
- [ ] Wrap the component export with `observer` from `mobx-react-lite`.
- [ ] Use a named function inside the `observer` wrapper for better debugging: `observer(function PrivateChatScreen(props) { ... })`.
- [ ] Update the `buttonOnPress` prop for the `EmptyState` component to trigger a data refetch action (a new function to be added to `usePrivateChatData`).

**Acceptance Criteria:**
- [ ] The component is wrapped in a named `observer` function.
- [ ] The "Try Again" button in the error state successfully re-triggers the data fetching logic.

### **Phase 2: Polish and Best Practices**
**Priority: MEDIUM**

#### 2.1 Implement Internationalization (i18n)
**Description:** All hardcoded user-facing strings in `PrivateChatScreen.tsx` will be replaced with i18n keys using the `tx` prop on the `Text` component and `translate` for other props.

**TASKS:**
- [ ] Add new keys to `app/i18n/en.ts` for "Loading chat...", "Oops! Something went wrong", "Try Again", and the placeholder text `Message {name}...`.
- [ ] Replace hardcoded `text` props in `PrivateChatScreen` with the corresponding `tx` prop.
- [ ] Use `txOptions` for the dynamic placeholder text in `ChatInputContainer`.
- [ ] Update the `EmptyState` component to accept `tx` and `txOptions` props.

**Acceptance Criteria:**
- [ ] No hardcoded user-facing strings remain in the component.
- [ ] All text is sourced from the i18n translation files.

#### 2.2 Improve Type Safety
**Description:** Update the `UsePrivateChatActionsProps` type to remove the `any` type for navigation.

**TASKS:**
- [ ] Open `app/screens/PrivateChat/types.ts`.
- [ ] Import `AppStackScreenProps` from `@/navigators`.
- [ ] Change the `navigation` prop type from `any` to `AppStackScreenProps<"PrivateChat">["navigation"]`.

**Acceptance Criteria:**
- [ ] The `any` type is removed and replaced with a type-safe navigation prop.

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
```typescript
// Adhere to the mandatory observer pattern
export const PrivateChatScreen: FC<PrivateChatScreenProps> = observer(function PrivateChatScreen(
  props,
) {
  // ...
})

// Use i18n for all user-facing text
<Text tx="privateChat.loading" />
<EmptyState headingTx="privateChat.error.heading" content={error} buttonTx="common.tryAgain" />
```

### **File Structure**
```
app/
├── hooks/
│   └── useKeyboard.ts        # NEW
├── screens/
│   └── PrivateChat/
│       ├── PrivateChatScreen.tsx # REFACTORED
│       ├── types.ts              # UPDATED
│       └── ...
└── i18n/
    └── en.ts                 # UPDATED
```

### **Testing Strategy**
- **Manual Testing:** Verify that the chat screen's loading, error, and success states render correctly. Confirm that keyboard avoidance works as expected on both iOS and Android.
- **Unit Tests:** Add a test for the `useKeyboard` hook to ensure it correctly tracks keyboard visibility and height.

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] Keyboard handling logic is fully encapsulated in the `useKeyboard` hook.
- [ ] `PrivateChatScreen` has a single, non-redundant render path.
- [ ] The component is wrapped in a named `observer`.
- [ ] All hardcoded strings are replaced with i18n keys.
- [ ] The "Try Again" button is functional.
- [ ] Navigation prop is type-safe.

### **Quality Assurance**
- [ ] Code has been reviewed and adheres to all rules in `.cursor/rules/`.
- [ ] The screen has been tested on both iOS and Android physical devices or simulators.
- [ ] No regressions in functionality have been introduced.
