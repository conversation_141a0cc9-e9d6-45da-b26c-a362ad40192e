# Local DB & Repository Layer Action Plan

## 🎯 **Objective**
Set up a minimal, swappable data layer that uses **expo-sqlite** today and can be replaced by **Supabase/Postgres** later with zero changes to UI or MobX-State-Tree (MST) stores.

**Context:** We want to ship an offline-capable iOS app quickly.  Supabase will become the single source of truth later, but for now we only need a local SQLite cache.  A thin Repository interface + two concrete implementations (SQLite & stub Supabase) give us this flexibility while keeping the codebase clean.

---

## 📊 **Current Assessment: 2/10**

### ✅ **Strong Areas**
- Clear long-term architecture in `ARCHITECTURE.md` ✅
- MobX-State-Tree already used across the app ✅
- Existing utility conventions & theming system ✅

### ⚠️ **Areas for Improvement**
- No unified Repository interface
- No local DB schema or migrations
- Stores fetch directly from in-memory JSO<PERSON> mocks

### 🔍 **Technical Debt**
- Future migration effort if API shape diverges between SQLite & Supabase
- Manual SQL string handling could grow messy without Drizzle

---

## 🚀 **Action Items**

### **Phase 1: Local SQLite Foundations**
**Priority: HIGH**

#### 1.1 Create shared Repository interface
**Description:** Define `CommunityRepository`, `EventRepository`, etc. inside `app/services/repositories/`.  Each exposes type-safe CRUD methods.

```typescript
// app/services/repositories/community-repository.ts
export interface CommunityRepository {
  getAll(): Promise<Community[]>;
  getById(id: string): Promise<Community | undefined>;
  save(entity: Community): Promise<void>;
  delete(id: string): Promise<void>;
}
```
**TASKS:**
- [ ] Create `repositories/` folder & interfaces
- [ ] Export barrel file `index.ts`

**Acceptance Criteria:**
- [ ] Interfaces compile without implementation

#### 1.2 Implement SQLite driver (expo-sqlite)
**Description:** Concrete class `CommunityRepositorySqlite` using `expo-sqlite` + Drizzle schema.

```typescript
// app/services/sqlite-client.ts (low-level helper)
import * as SQLite from "expo-sqlite"
export const db = SQLite.openDatabase("app.db")
```

**TASKS:**
- [ ] Install `expo-sqlite` & `drizzle-orm-sqlite`
- [ ] Create `sqlite-client.ts`
- [ ] Write migration `create table communities (...)`
- [ ] Implement `CommunityRepositorySqlite`

**Acceptance Criteria:**
- [ ] App boots without errors on iOS simulator
- [ ] `repository.getAll()` returns empty array instead of crashing

#### 1.3 Stub Supabase driver
**Description:** Add a placeholder `CommunityRepositorySupabase` that throws `"NotImplemented"`.  Keeps DI pattern ready.

**TASKS:**
- [ ] Create stub implementation files

**Acceptance Criteria:**
- [ ] DI container can switch between drivers via env flag

#### 1.4 Set up **Drizzle Studio**
**Description:** Install and configure Drizzle Studio for visual schema inspection and ad-hoc queries during development.

**TASKS:**
- [ ] Install `drizzle-kit` CLI globally (`npm i -g drizzle-kit`)
- [ ] Add `"studio": "drizzle-kit studio --db ./app.db"` script to `package.json`
- [ ] Document usage in README (`npm run studio` opens GUI)

**Acceptance Criteria:**
- [ ] Running `npm run studio` launches Drizzle Studio connected to the local SQLite file

### **Phase 2: MST Store Integration**
**Priority: HIGH**

#### 2.1 Create `OfflineStore` (hydration status)
**Description:** Track hydration lifecycle & last sync timestamp.

**Steps:**
1. `app/models/OfflineStore.ts` with `isHydrated`, `lastSync`
2. Expose actions `hydrate()` and `setLastSync()`

**Dependencies:** Repository interfaces ready

#### 2.2 Wire stores to Repository
**Description:** At app start call `repository.getAll()` → `communityStore.setCommunities()`.  On create/update/delete delegate to repository then update store.

**Acceptance Criteria:**
- [ ] Communities screen renders data pulled from SQLite
- [ ] Any new community persists across app restarts

### **Phase 3: Supabase Driver & Sync (Future)**
**Priority: MEDIUM**

#### 3.1 Implement real Supabase drivers
**Description:** Replace stub with `@supabase/supabase-js` queries that mirror SQLite SQL.

#### 3.2 Add SyncManager
**Description:** After every successful remote fetch, call `CacheService.save*` to refresh local tables (stale-while-revalidate pattern).

---

## 🔧 **Implementation Guidelines**

### **Code Standards**
- Use Drizzle schema for both drivers
- Keep SQL in `migrations.ts`, not scattered across code

### **File Structure**
```
app/services/
  repositories/
    community-repository.ts
    event-repository.ts
    index.ts
  sqlite/
    sqlite-client.ts
    migrations.ts
    community-repository-sqlite.ts
  supabase/
    community-repository-supabase.ts (stub)
app/models/
  OfflineStore.ts
```

### **Testing Strategy**
- Unit test SQLite repository with Jest (in-memory DB)
- Smoke test hydration path on iOS simulator

### **Performance Considerations**
- Use `INSERT OR REPLACE` to batch-write rows
- Wrap reads in single transaction

### **Security Considerations**
- No sensitive data stored locally at this stage

---

## 🔄 **Migration Strategy**

### **Incremental Approach**
1. Complete Phase 1 and ship TestFlight build (local only)
2. Implement Phase 3 drivers while users test
3. Flip feature flag to Supabase in staging → monitor
4. Remove SQLite writes once remote confirmed stable

### **Rollback Plan**
- Env flag `DATA_DRIVER=sqlite` reverts instantly
- Keep SQLite migrations intact for at least one version

---

### **Critical Path**
- Define repository interfaces
- SQLite migrations compile on first launch
- Hydration populates MST stores

---

## 🛠 **Resources & Dependencies**
- expo-sqlite: local DB engine
- drizzle-orm: typed SQL builder (SQLite & Postgres)
- mobx-state-tree: state management
- @supabase/supabase-js: future remote driver

---

## 🚨 **Risk Assessment**

### **High Risk Items**
| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Schema divergence between SQLite & Postgres | High | Medium | Use Drizzle to share schema |
| Data loss during driver switch | Medium | Low | Keep local writes enabled until Supabase stable |

---

## ✅ **Definition of Done**

### **Feature Complete**
- [ ] Repository interfaces & SQLite implementation done
- [ ] Offline cache hydrates MST stores
- [ ] Supabase stub compiles

### **Quality Assurance**
- [ ] Unit tests passing
- [ ] Manual offline test successful

### **Documentation**
- [ ] README section on data layer updated

### **Production Ready**
- [ ] Build deployed to TestFlight with SQLite driver

---

## 📚 **References & Resources**
- `docs/TODO/ARCHITECTURE.md`
- Expo SQLite docs: https://docs.expo.dev/versions/latest/sdk/sqlite/
- Drizzle ORM docs: https://orm.drizzle.team
- Supabase JS docs: https://supabase.com/docs/reference/javascript
- **Drizzle Studio guide:** https://orm.drizzle.team/docs/get-started-sqlite

---

**Template Version:** 1.0
**Created:** 2025-07-08
**Last Updated:** 2025-07-08
**Template Author:** AI Assistant 