{"expo": {"name": "PadelCommunityApp", "slug": "PadelCommunityApp", "scheme": "padelcommunityapp", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "automatic", "icon": "./assets/images/app_icon_all.png", "updates": {"fallbackToCacheTimeout": 0}, "newArchEnabled": false, "jsEngine": "hermes", "assetBundlePatterns": ["**/*"], "android": {"icon": "./assets/images/app_icon_android_legacy.png", "package": "com.padelcommunityapp", "adaptiveIcon": {"foregroundImage": "./assets/images/app_icon_android_adaptive_foreground.png", "backgroundImage": "./assets/images/app_icon_android_adaptive_background.png"}, "allowBackup": false, "intentFilters": [{"action": "VIEW", "data": [{"scheme": "padelcommunityapp"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "ios": {"icon": "./assets/images/app_icon_ios.png", "supportsTablet": true, "scheme": "padelcommunityapp", "bundleIdentifier": "com.padelcommunityapp", "privacyManifests": {"NSPrivacyAccessedAPITypes": [{"NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryUserDefaults", "NSPrivacyAccessedAPITypeReasons": ["CA92.1"]}]}, "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "web": {"favicon": "./assets/images/app_icon_web_favicon.png", "bundler": "metro"}, "plugins": ["expo-router", "expo-localization", "expo-font", ["expo-asset", {"assets": ["assets/images", "assets/icons"]}], ["expo-splash-screen", {"image": "./assets/images/app_icon_android_adaptive_foreground.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#191015"}]], "experiments": {"tsconfigPaths": true}, "extra": {"ignite": {"version": "10.5.3"}, "eas": {"projectId": "d7a47deb-b8d1-4b01-ba4d-a36f77d3af3a"}}}}