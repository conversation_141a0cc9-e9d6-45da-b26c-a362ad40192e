{"version": "6", "dialect": "sqlite", "id": "dfb11e83-2196-4195-b99c-bbe41670715c", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"communities": {"name": "communities", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "short_description": {"name": "short_description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "memberCount": {"name": "memberCount", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "backgroundImage": {"name": "backgroundImage", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "isPrivate": {"name": "isPrivate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "isJoined": {"name": "isJoined", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "stats": {"name": "stats", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "adminsAndModerators": {"name": "adminsAndModerators", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "achievements": {"name": "achievements", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "rules": {"name": "rules", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "upcomingEvents": {"name": "upcomingEvents", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pastEvents": {"name": "pastEvents", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "members": {"name": "members", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "chatMessages": {"name": "chatMessages", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "surname": {"name": "surname", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "country_code": {"name": "country_code", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mobile_no": {"name": "mobile_no", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "dob": {"name": "dob", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "trophies": {"name": "trophies", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_updated": {"name": "last_updated", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}