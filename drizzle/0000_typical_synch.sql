CREATE TABLE `communities` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`short_description` text,
	`memberCount` integer,
	`backgroundImage` text,
	`isPrivate` integer,
	`isJoined` integer,
	`stats` text,
	`address` text,
	`adminsAndModerators` text,
	`achievements` text,
	`rules` text,
	`upcomingEvents` text,
	`pastEvents` text,
	`members` text,
	`chatMessages` text
);
--> statement-breakpoint
CREATE TABLE `users` (
	`user_id` text PRIMARY KEY NOT NULL,
	`name` text,
	`surname` text,
	`email` text NOT NULL,
	`country_code` text,
	`mobile_no` text,
	`gender` text,
	`dob` text,
	`description` text,
	`location` text,
	`trophies` integer,
	`created_at` text,
	`last_updated` text
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);

INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'A243HG3',
    '<PERSON>',
    'Silva',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Padel Enthusiast',
    'Unknown',
    4,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'B789KL4',
    'John',
    'Doe',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Weekend Warrior',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'C456MN7',
    'Sarah',
    'Smith',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Advanced Player',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'D123OP8',
    'Mike',
    'Johnson',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Beginner Enthusiast',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'E890QR5',
    'Emma',
    'Wilson',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Competitive Player',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'F567ST9',
    'Anna',
    'Martinez',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Padel Coach',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'G234UV6',
    'David',
    'Brown',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Community Manager',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'H901WX3',
    'Lisa',
    'Garcia',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Tournament Organizer',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'J678YZ1',
    'Alex',
    'Taylor',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Pro Player',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'K111AA1',
    'Oliver',
    'King',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Club Member',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'L222BB2',
    'Sophia',
    'Davis',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Enthusiast',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'M333CC3',
    'Ethan',
    'Clark',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Beginner',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'N444DD4',
    'Mia',
    'Lopez',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Intermediate',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'P555EE5',
    'Noah',
    'Walker',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Coach',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'Q666FF6',
    'Ava',
    'Patel',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Advanced',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'R777GG7',
    'Lucas',
    'Turner',
    '<EMAIL>',
    'US',
    '+1234567890',
    'Unknown',
    '2000-01-01',
    'Pro',
    'Unknown',
    0,
    '2025-07-08T17:05:43.766Z',
    '2025-07-08T17:05:43.766Z'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '2a106880852d24490f22f676443c5b55',
    'Dummy16',
    'User16',
    '<EMAIL>',
    'US',
    '+12345678916',
    'Male',
    '1990-01-17',
    'Enthusiastic padel player number 16',
    'City 16, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '261895a63901b0ee73a620b7c02c6684',
    'Dummy17',
    'User17',
    '<EMAIL>',
    'US',
    '+12345678917',
    'Female',
    '1990-01-18',
    'Enthusiastic padel player number 17',
    'City 17, Country 2',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'b79745362145b2713f0c19a91c8901aa',
    'Dummy18',
    'User18',
    '<EMAIL>',
    'US',
    '+12345678918',
    'Male',
    '1990-01-19',
    'Enthusiastic padel player number 18',
    'City 18, Country 3',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd8d0f19c00b21645c1109a15a20b0804',
    'Dummy19',
    'User19',
    '<EMAIL>',
    'US',
    '+12345678919',
    'Female',
    '1990-01-20',
    'Enthusiastic padel player number 19',
    'City 19, Country 4',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '649f3e4942b083c7499696c73918a514',
    'Dummy20',
    'User20',
    '<EMAIL>',
    'US',
    '+12345678920',
    'Male',
    '1990-01-21',
    'Enthusiastic padel player number 20',
    'City 20, Country 0',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '1721522f676037b5a8e0f590457635c0',
    'Dummy21',
    'User21',
    '<EMAIL>',
    'US',
    '+12345678921',
    'Female',
    '1990-01-22',
    'Enthusiastic padel player number 21',
    'City 21, Country 1',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '87c805a5a1f2e1a384f67676f4e1f727',
    'Dummy22',
    'User22',
    '<EMAIL>',
    'US',
    '+12345678922',
    'Male',
    '1990-01-23',
    'Enthusiastic padel player number 22',
    'City 22, Country 2',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'b417e0b571168f0413247c4939b8c049',
    'Dummy23',
    'User23',
    '<EMAIL>',
    'US',
    '+12345678923',
    'Female',
    '1990-01-24',
    'Enthusiastic padel player number 23',
    'City 23, Country 3',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'f31505d93333469e3a6a1614749f7e52',
    'Dummy24',
    'User24',
    '<EMAIL>',
    'US',
    '+12345678924',
    'Male',
    '1990-01-25',
    'Enthusiastic padel player number 24',
    'City 24, Country 4',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd8a7c2e3612d26f74a005086b973a005',
    'Dummy25',
    'User25',
    '<EMAIL>',
    'US',
    '+12345678925',
    'Female',
    '1990-01-26',
    'Enthusiastic padel player number 25',
    'City 25, Country 0',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '1a4c8a58a6984e72323c048c084f937e',
    'Dummy26',
    'User26',
    '<EMAIL>',
    'US',
    '+12345678926',
    'Male',
    '1990-01-27',
    'Enthusiastic padel player number 26',
    'City 26, Country 1',
    8,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '1d36184711319760775d7b5f25a74e92',
    'Dummy27',
    'User27',
    '<EMAIL>',
    'US',
    '+12345678927',
    'Female',
    '1990-01-28',
    'Enthusiastic padel player number 27',
    'City 27, Country 2',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd81577a7605e55e3734027720986754b',
    'Dummy28',
    'User28',
    '<EMAIL>',
    'US',
    '+12345678928',
    'Male',
    '1990-01-29',
    'Enthusiastic padel player number 28',
    'City 28, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '8e8e7a048a1a384f676f4e1f727b134d',
    'Dummy29',
    'User29',
    '<EMAIL>',
    'US',
    '+12345678929',
    'Female',
    '1990-01-30',
    'Enthusiastic padel player number 29',
    'City 29, Country 4',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '223b123605a5a1f2e1a384f67676f4e1',
    'Dummy30',
    'User30',
    '<EMAIL>',
    'US',
    '+12345678930',
    'Male',
    '1990-01-31',
    'Enthusiastic padel player number 30',
    'City 30, Country 0',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '112b2347c4939b8c049d8a7c2e3612d2',
    'Dummy31',
    'User31',
    '<EMAIL>',
    'US',
    '+12345678931',
    'Female',
    '1990-02-01',
    'Enthusiastic padel player number 31',
    'City 31, Country 1',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '334c3458a6984e72323c048c084f937e',
    'Dummy32',
    'User32',
    '<EMAIL>',
    'US',
    '+12345678932',
    'Male',
    '1990-02-02',
    'Enthusiastic padel player number 32',
    'City 32, Country 2',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '445d4569f7e521a4c8a58a6984e72323',
    'Dummy33',
    'User33',
    '<EMAIL>',
    'US',
    '+12345678933',
    'Female',
    '1990-02-03',
    'Enthusiastic padel player number 33',
    'City 33, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '556e567a005086b973a0051a4c8a58a6',
    'Dummy34',
    'User34',
    '<EMAIL>',
    'US',
    '+12345678934',
    'Male',
    '1990-02-04',
    'Enthusiastic padel player number 34',
    'City 34, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '667f678b134d1d36184711319760775d',
    'Dummy35',
    'User35',
    '<EMAIL>',
    'US',
    '+12345678935',
    'Female',
    '1990-02-05',
    'Enthusiastic padel player number 35',
    'City 35, Country 0',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '778g789c02c6684b79745362145b2713',
    'Dummy36',
    'User36',
    '<EMAIL>',
    'US',
    '+12345678936',
    'Male',
    '1990-02-06',
    'Enthusiastic padel player number 36',
    'City 36, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '889h890d0804d8a7c2e3612d26f74a00',
    'Dummy37',
    'User37',
    '<EMAIL>',
    'US',
    '+12345678937',
    'Female',
    '1990-02-07',
    'Enthusiastic padel player number 37',
    'City 37, Country 2',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '990i901e1f727b134d1d361847113197',
    'Dummy38',
    'User38',
    '<EMAIL>',
    'US',
    '+12345678938',
    'Male',
    '1990-02-08',
    'Enthusiastic padel player number 38',
    'City 38, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'a01j012f25a74e92d81577a7605e55e3',
    'Dummy39',
    'User39',
    '<EMAIL>',
    'US',
    '+12345678939',
    'Female',
    '1990-02-09',
    'Enthusiastic padel player number 39',
    'City 39, Country 4',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'b12k123f39b8c049d8a7c2e3612d26f7',
    'Dummy40',
    'User40',
    '<EMAIL>',
    'US',
    '+12345678940',
    'Male',
    '1990-02-10',
    'Enthusiastic padel player number 40',
    'City 40, Country 0',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'c23l234g49f7e521a4c8a58a6984e723',
    'Dummy41',
    'User41',
    '<EMAIL>',
    'US',
    '+12345678941',
    'Female',
    '1990-02-11',
    'Enthusiastic padel player number 41',
    'City 41, Country 1',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd34m345h590457635c0223b123605a5a',
    'Dummy42',
    'User42',
    '<EMAIL>',
    'US',
    '+12345678942',
    'Male',
    '1990-02-12',
    'Enthusiastic padel player number 42',
    'City 42, Country 2',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'e45n456i696c73918a5141721522f676',
    'Dummy43',
    'User43',
    '<EMAIL>',
    'US',
    '+12345678943',
    'Female',
    '1990-02-13',
    'Enthusiastic padel player number 43',
    'City 43, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'f56o567j734027720986754b8e8e7a04',
    'Dummy44',
    'User44',
    '<EMAIL>',
    'US',
    '+12345678944',
    'Male',
    '1990-02-14',
    'Enthusiastic padel player number 44',
    'City 44, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'g67p678k83c7499696c73918a5141721',
    'Dummy45',
    'User45',
    '<EMAIL>',
    'US',
    '+12345678945',
    'Female',
    '1990-02-15',
    'Enthusiastic padel player number 45',
    'City 45, Country 0',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'h78q789l93333469e3a6a1614749f7e5',
    'Dummy46',
    'User46',
    '<EMAIL>',
    'US',
    '+12345678946',
    'Male',
    '1990-02-16',
    'Enthusiastic padel player number 46',
    'City 46, Country 1',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'i89r890m00b21645c1109a15a20b0804',
    'Dummy47',
    'User47',
    '<EMAIL>',
    'US',
    '+12345678947',
    'Female',
    '1990-02-17',
    'Enthusiastic padel player number 47',
    'City 47, Country 2',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'j90s901n1aa261895a63901b0ee73a62',
    'Dummy48',
    'User48',
    '<EMAIL>',
    'US',
    '+12345678948',
    'Male',
    '1990-02-18',
    'Enthusiastic padel player number 48',
    'City 48, Country 3',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'k01t012o2c6684b79745362145b2713f',
    'Dummy49',
    'User49',
    '<EMAIL>',
    'US',
    '+12345678949',
    'Female',
    '1990-02-19',
    'Enthusiastic padel player number 49',
    'City 49, Country 4',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'l12u123p3f0c19a91c8901aad8d0f19c',
    'Dummy50',
    'User50',
    '<EMAIL>',
    'US',
    '+12345678950',
    'Male',
    '1990-02-20',
    'Enthusiastic padel player number 50',
    'City 50, Country 0',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'm23v234q45c1109a15a20b0804649f3e',
    'Dummy51',
    'User51',
    '<EMAIL>',
    'US',
    '+12345678951',
    'Female',
    '1990-02-21',
    'Enthusiastic padel player number 51',
    'City 51, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'n34w345r52b083c7499696c73918a514',
    'Dummy52',
    'User52',
    '<EMAIL>',
    'US',
    '+12345678952',
    'Male',
    '1990-02-22',
    'Enthusiastic padel player number 52',
    'City 52, Country 2',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'o45x456s6037b5a8e0f590457635c022',
    'Dummy53',
    'User53',
    '<EMAIL>',
    'US',
    '+12345678953',
    'Female',
    '1990-02-23',
    'Enthusiastic padel player number 53',
    'City 53, Country 3',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'p56y567t71f2e1a384f67676f4e1f727',
    'Dummy54',
    'User54',
    '<EMAIL>',
    'US',
    '+12345678954',
    'Male',
    '1990-02-24',
    'Enthusiastic padel player number 54',
    'City 54, Country 4',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'q67z678u81168f0413247c4939b8c049',
    'Dummy55',
    'User55',
    '<EMAIL>',
    'US',
    '+12345678955',
    'Female',
    '1990-02-25',
    'Enthusiastic padel player number 55',
    'City 55, Country 0',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'r78a789v93333469e3a6a1614749f7e5',
    'Dummy56',
    'User56',
    '<EMAIL>',
    'US',
    '+12345678956',
    'Male',
    '1990-02-26',
    'Enthusiastic padel player number 56',
    'City 56, Country 1',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    's89b890w005086b973a0051a4c8a58a6',
    'Dummy57',
    'User57',
    '<EMAIL>',
    'US',
    '+12345678957',
    'Female',
    '1990-02-27',
    'Enthusiastic padel player number 57',
    'City 57, Country 2',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    't90c901x134d1d36184711319760775d',
    'Dummy58',
    'User58',
    '<EMAIL>',
    'US',
    '+12345678958',
    'Male',
    '1990-02-28',
    'Enthusiastic padel player number 58',
    'City 58, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'u01d012y25a74e92d81577a7605e55e3',
    'Dummy59',
    'User59',
    '<EMAIL>',
    'US',
    '+12345678959',
    'Female',
    '1990-03-01',
    'Enthusiastic padel player number 59',
    'City 59, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'v12e123z39b8c049d8a7c2e3612d26f7',
    'Dummy60',
    'User60',
    '<EMAIL>',
    'US',
    '+12345678960',
    'Male',
    '1990-03-02',
    'Enthusiastic padel player number 60',
    'City 60, Country 0',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'w23f234a49f7e521a4c8a58a6984e723',
    'Dummy61',
    'User61',
    '<EMAIL>',
    'US',
    '+12345678961',
    'Female',
    '1990-03-03',
    'Enthusiastic padel player number 61',
    'City 61, Country 1',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'x34g345b590457635c0223b123605a5a',
    'Dummy62',
    'User62',
    '<EMAIL>',
    'US',
    '+12345678962',
    'Male',
    '1990-03-04',
    'Enthusiastic padel player number 62',
    'City 62, Country 2',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'y45h456c696c73918a5141721522f676',
    'Dummy63',
    'User63',
    '<EMAIL>',
    'US',
    '+12345678963',
    'Female',
    '1990-03-05',
    'Enthusiastic padel player number 63',
    'City 63, Country 3',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'z56i567d734027720986754b8e8e7a04',
    'Dummy64',
    'User64',
    '<EMAIL>',
    'US',
    '+12345678964',
    'Male',
    '1990-03-06',
    'Enthusiastic padel player number 64',
    'City 64, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '067j678e83c7499696c73918a5141721',
    'Dummy65',
    'User65',
    '<EMAIL>',
    'US',
    '+12345678965',
    'Female',
    '1990-03-07',
    'Enthusiastic padel player number 65',
    'City 65, Country 0',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '178k789f93333469e3a6a1614749f7e5',
    'Dummy66',
    'User66',
    '<EMAIL>',
    'US',
    '+12345678966',
    'Male',
    '1990-03-08',
    'Enthusiastic padel player number 66',
    'City 66, Country 1',
    8,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '289l890g00b21645c1109a15a20b0804',
    'Dummy67',
    'User67',
    '<EMAIL>',
    'US',
    '+12345678967',
    'Female',
    '1990-03-09',
    'Enthusiastic padel player number 67',
    'City 67, Country 2',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '390m901h1aa261895a63901b0ee73a62',
    'Dummy68',
    'User68',
    '<EMAIL>',
    'US',
    '+12345678968',
    'Male',
    '1990-03-10',
    'Enthusiastic padel player number 68',
    'City 68, Country 3',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '401n012i2c6684b79745362145b2713f',
    'Dummy69',
    'User69',
    '<EMAIL>',
    'US',
    '+12345678969',
    'Female',
    '1990-03-11',
    'Enthusiastic padel player number 69',
    'City 69, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '512o123j3f0c19a91c8901aad8d0f19c',
    'Dummy70',
    'User70',
    '<EMAIL>',
    'US',
    '+12345678970',
    'Male',
    '1990-03-12',
    'Enthusiastic padel player number 70',
    'City 70, Country 0',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '623p234k45c1109a15a20b0804649f3e',
    'Dummy71',
    'User71',
    '<EMAIL>',
    'US',
    '+12345678971',
    'Female',
    '1990-03-13',
    'Enthusiastic padel player number 71',
    'City 71, Country 1',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '734q345l52b083c7499696c73918a514',
    'Dummy72',
    'User72',
    '<EMAIL>',
    'US',
    '+12345678972',
    'Male',
    '1990-03-14',
    'Enthusiastic padel player number 72',
    'City 72, Country 2',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '845r456m6037b5a8e0f590457635c022',
    'Dummy73',
    'User73',
    '<EMAIL>',
    'US',
    '+12345678973',
    'Female',
    '1990-03-15',
    'Enthusiastic padel player number 73',
    'City 73, Country 3',
    5,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '956s567n71f2e1a384f67676f4e1f727',
    'Dummy74',
    'User74',
    '<EMAIL>',
    'US',
    '+12345678974',
    'Male',
    '1990-03-16',
    'Enthusiastic padel player number 74',
    'City 74, Country 4',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'a67t678o81168f0413247c4939b8c049',
    'Dummy75',
    'User75',
    '<EMAIL>',
    'US',
    '+12345678975',
    'Female',
    '1990-03-17',
    'Enthusiastic padel player number 75',
    'City 75, Country 0',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'b78u789p93333469e3a6a1614749f7e5',
    'Dummy76',
    'User76',
    '<EMAIL>',
    'US',
    '+12345678976',
    'Male',
    '1990-03-18',
    'Enthusiastic padel player number 76',
    'City 76, Country 1',
    8,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'c89v890q005086b973a0051a4c8a58a6',
    'Dummy77',
    'User77',
    '<EMAIL>',
    'US',
    '+12345678977',
    'Female',
    '1990-03-19',
    'Enthusiastic padel player number 77',
    'City 77, Country 2',
    9,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd90w901r134d1d36184711319760775d',
    'Dummy78',
    'User78',
    '<EMAIL>',
    'US',
    '+12345678978',
    'Male',
    '1990-03-20',
    'Enthusiastic padel player number 78',
    'City 78, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'e01x012s25a74e92d81577a7605e55e3',
    'Dummy79',
    'User79',
    '<EMAIL>',
    'US',
    '+12345678979',
    'Female',
    '1990-03-21',
    'Enthusiastic padel player number 79',
    'City 79, Country 4',
    6,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'f12y123t39b8c049d8a7c2e3612d26f7',
    'Dummy80',
    'User80',
    '<EMAIL>',
    'US',
    '+12345678980',
    'Male',
    '1990-03-22',
    'Enthusiastic padel player number 80',
    'City 80, Country 0',
    7,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'g23z234u49f7e521a4c8a58a6984e723',
    'Dummy81',
    'User81',
    '<EMAIL>',
    'US',
    '+12345678981',
    'Female',
    '1990-03-23',
    'Enthusiastic padel player number 81',
    'City 81, Country 1',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'h34a345v590457635c0223b123605a5a',
    'Dummy82',
    'User82',
    '<EMAIL>',
    'US',
    '+12345678982',
    'Male',
    '1990-03-24',
    'Enthusiastic padel player number 82',
    'City 82, Country 2',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'i45b456w696c73918a5141721522f676',
    'Dummy83',
    'User83',
    '<EMAIL>',
    'US',
    '+12345678983',
    'Female',
    '1990-03-25',
    'Enthusiastic padel player number 83',
    'City 83, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'j56c567x734027720986754b8e8e7a04',
    'Dummy84',
    'User84',
    '<EMAIL>',
    'US',
    '+12345678984',
    'Male',
    '1990-03-26',
    'Enthusiastic padel player number 84',
    'City 84, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'k67d678y83c7499696c73918a5141721',
    'Dummy85',
    'User85',
    '<EMAIL>',
    'US',
    '+12345678985',
    'Female',
    '1990-03-27',
    'Enthusiastic padel player number 85',
    'City 85, Country 0',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'l78e789z93333469e3a6a1614749f7e5',
    'Dummy86',
    'User86',
    '<EMAIL>',
    'US',
    '+12345678986',
    'Male',
    '1990-03-28',
    'Enthusiastic padel player number 86',
    'City 86, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'm89f890a00b21645c1109a15a20b0804',
    'Dummy87',
    'User87',
    '<EMAIL>',
    'US',
    '+12345678987',
    'Female',
    '1990-03-29',
    'Enthusiastic padel player number 87',
    'City 87, Country 2',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'n90g901b1aa261895a63901b0ee73a62',
    'Dummy88',
    'User88',
    '<EMAIL>',
    'US',
    '+12345678988',
    'Male',
    '1990-03-30',
    'Enthusiastic padel player number 88',
    'City 88, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'o01h012c2c6684b79745362145b2713f',
    'Dummy89',
    'User89',
    '<EMAIL>',
    'US',
    '+12345678989',
    'Female',
    '1990-03-31',
    'Enthusiastic padel player number 89',
    'City 89, Country 4',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'p12i123d3f0c19a91c8901aad8d0f19c',
    'Dummy90',
    'User90',
    '<EMAIL>',
    'US',
    '+12345678990',
    'Male',
    '1990-04-01',
    'Enthusiastic padel player number 90',
    'City 90, Country 0',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'q23j234e45c1109a15a20b0804649f3e',
    'Dummy91',
    'User91',
    '<EMAIL>',
    'US',
    '+12345678991',
    'Female',
    '1990-04-02',
    'Enthusiastic padel player number 91',
    'City 91, Country 1',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'r34k345f52b083c7499696c73918a514',
    'Dummy92',
    'User92',
    '<EMAIL>',
    'US',
    '+12345678992',
    'Male',
    '1990-04-03',
    'Enthusiastic padel player number 92',
    'City 92, Country 2',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    's45l456g6037b5a8e0f590457635c022',
    'Dummy93',
    'User93',
    '<EMAIL>',
    'US',
    '+12345678993',
    'Female',
    '1990-04-04',
    'Enthusiastic padel player number 93',
    'City 93, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    't56m567h71f2e1a384f67676f4e1f727',
    'Dummy94',
    'User94',
    '<EMAIL>',
    'US',
    '+12345678994',
    'Male',
    '1990-04-05',
    'Enthusiastic padel player number 94',
    'City 94, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'u67n678i81168f0413247c4939b8c049',
    'Dummy95',
    'User95',
    '<EMAIL>',
    'US',
    '+12345678995',
    'Female',
    '1990-04-06',
    'Enthusiastic padel player number 95',
    'City 95, Country 0',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'v78o789j93333469e3a6a1614749f7e5',
    'Dummy96',
    'User96',
    '<EMAIL>',
    'US',
    '+12345678996',
    'Male',
    '1990-04-07',
    'Enthusiastic padel player number 96',
    'City 96, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'w89p890k005086b973a0051a4c8a58a6',
    'Dummy97',
    'User97',
    '<EMAIL>',
    'US',
    '+12345678997',
    'Female',
    '1990-04-08',
    'Enthusiastic padel player number 97',
    'City 97, Country 2',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'x90q901l134d1d36184711319760775d',
    'Dummy98',
    'User98',
    '<EMAIL>',
    'US',
    '+12345678998',
    'Male',
    '1990-04-09',
    'Enthusiastic padel player number 98',
    'City 98, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'y01r012m25a74e92d81577a7605e55e3',
    'Dummy99',
    'User99',
    '<EMAIL>',
    'US',
    '+12345678999',
    'Female',
    '1990-04-10',
    'Enthusiastic padel player number 99',
    'City 99, Country 4',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'z12s123n39b8c049d8a7c2e3612d26f7',
    'Dummy100',
    'User100',
    '<EMAIL>',
    'US',
    '+123456789100',
    'Male',
    '1990-04-11',
    'Enthusiastic padel player number 100',
    'City 100, Country 0',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '023t234o49f7e521a4c8a58a6984e723',
    'Dummy101',
    'User101',
    '<EMAIL>',
    'US',
    '+123456789101',
    'Female',
    '1990-04-12',
    'Enthusiastic padel player number 101',
    'City 101, Country 1',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '134u345p590457635c0223b123605a5a',
    'Dummy102',
    'User102',
    '<EMAIL>',
    'US',
    '+123456789102',
    'Male',
    '1990-04-13',
    'Enthusiastic padel player number 102',
    'City 102, Country 2',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '245v456q696c73918a5141721522f676',
    'Dummy103',
    'User103',
    '<EMAIL>',
    'US',
    '+123456789103',
    'Female',
    '1990-04-14',
    'Enthusiastic padel player number 103',
    'City 103, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '356w567r734027720986754b8e8e7a04',
    'Dummy104',
    'User104',
    '<EMAIL>',
    'US',
    '+123456789104',
    'Male',
    '1990-04-15',
    'Enthusiastic padel player number 104',
    'City 104, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '467x678s83c7499696c73918a5141721',
    'Dummy105',
    'User105',
    '<EMAIL>',
    'US',
    '+123456789105',
    'Female',
    '1990-04-16',
    'Enthusiastic padel player number 105',
    'City 105, Country 0',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '578y789t93333469e3a6a1614749f7e5',
    'Dummy106',
    'User106',
    '<EMAIL>',
    'US',
    '+123456789106',
    'Male',
    '1990-04-17',
    'Enthusiastic padel player number 106',
    'City 106, Country 1',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '689z890u00b21645c1109a15a20b0804',
    'Dummy107',
    'User107',
    '<EMAIL>',
    'US',
    '+123456789107',
    'Female',
    '1990-04-18',
    'Enthusiastic padel player number 107',
    'City 107, Country 2',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '790a901v1aa261895a63901b0ee73a62',
    'Dummy108',
    'User108',
    '<EMAIL>',
    'US',
    '+123456789108',
    'Male',
    '1990-04-19',
    'Enthusiastic padel player number 108',
    'City 108, Country 3',
    2,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '801b012w2c6684b79745362145b2713f',
    'Dummy109',
    'User109',
    '<EMAIL>',
    'US',
    '+123456789109',
    'Female',
    '1990-04-20',
    'Enthusiastic padel player number 109',
    'City 109, Country 4',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    '912c123x3f0c19a91c8901aad8d0f19c',
    'Dummy110',
    'User110',
    '<EMAIL>',
    'US',
    '+123456789110',
    'Male',
    '1990-04-21',
    'Enthusiastic padel player number 110',
    'City 110, Country 0',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'a23d234y45c1109a15a20b0804649f3e',
    'Dummy111',
    'User111',
    '<EMAIL>',
    'US',
    '+123456789111',
    'Female',
    '1990-04-22',
    'Enthusiastic padel player number 111',
    'City 111, Country 1',
    3,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'b34e345z52b083c7499696c73918a514',
    'Dummy112',
    'User112',
    '<EMAIL>',
    'US',
    '+123456789112',
    'Male',
    '1990-04-23',
    'Enthusiastic padel player number 112',
    'City 112, Country 2',
    4,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'c45f456a6037b5a8e0f590457635c022',
    'Dummy113',
    'User113',
    '<EMAIL>',
    'US',
    '+123456789113',
    'Female',
    '1990-04-24',
    'Enthusiastic padel player number 113',
    'City 113, Country 3',
    1,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
INSERT INTO users (user_id, name, surname, email, country_code, mobile_no, gender, dob, description, location, trophies, created_at, last_updated) VALUES (
    'd56g567b71f2e1a384f67676f4e1f727',
    'Dummy114',
    'User114',
    '<EMAIL>',
    'US',
    '+123456789114',
    'Male',
    '1990-04-25',
    'Enthusiastic padel player number 114',
    'City 114, Country 4',
    0,
    'NOW',
    'NOW'
  );
--> statement-breakpoint
