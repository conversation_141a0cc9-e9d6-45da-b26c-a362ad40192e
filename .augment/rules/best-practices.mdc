---
type: "always_apply"
---

# Coding Rules & Best Practices

## 🏆 **Project Strengths - Leverage These Patterns**

Our codebase has been built with excellent architectural foundations. **USE THESE EXISTING PATTERNS** instead of reinventing the wheel:

### ✅ **Theming System** - `useAppTheme` Hook
**Location**: `app/utils/useAppTheme.ts`
```typescript
// ✅ ALWAYS use the theming system for consistent styling
import { useAppTheme } from "@/utils/useAppTheme"

const MyComponent = () => {
  const { themed, theme, themeScheme } = useAppTheme()
  
  const $container = themed(({ colors, spacing }) => ({
    backgroundColor: colors.background,
    padding: spacing.md,
  }))
  
  return <View style={$container}>...</View>
}
```

### ✅ **Icon System** - Centralized Icon Management
**Location**: `app/components/Icon.tsx`
```typescript
// ✅ Use the centralized Icon component
import { Icon } from "@/components"

<Icon icon="back" size={24} color={colors.text} />
<Icon icon="bell" onPress={handleNotification} />
```
**Available Icons**: See `assets/icons/` directory for all available icons

### ✅ **Component Preset System** - Smart Component Variants
**Examples in**: `app/components/Button.tsx`, `app/components/Text.tsx`, `app/components/Screen.tsx`
```typescript
// ✅ Leverage preset patterns for consistent UI
<Button preset="filled" text="Primary Action" />
<Button preset="reversed" text="Secondary Action" />
<Text preset="heading" text="Page Title" />
<Screen preset="scroll" safeAreaEdges={["top"]} />
```

### ✅ **MobX-State-Tree** - Predictable State Management
**Location**: `app/models/RootStore.ts`, `app/models/AuthenticationStore.ts`
```typescript
// ✅ Use MST stores for global state
import { useStores } from "@/models"

const MyComponent = observer(() => {
  const { authenticationStore } = useStores()
  
  return (
    <Text>{authenticationStore.isAuthenticated ? "Logged In" : "Not Logged In"}</Text>
  )
})
```

### ✅ **Type-Safe Navigation** - React Navigation Best Practices
**Location**: `app/navigators/AppNavigator.tsx`
```typescript
// ✅ Use typed navigation throughout the app
import { AppStackScreenProps } from "@/navigators"

interface ScreenProps extends AppStackScreenProps<"ScreenName"> {}

const MyScreen: FC<ScreenProps> = ({ navigation, route }) => {
  // Type-safe navigation parameters
  const { userId } = route.params
  
  // Type-safe navigation calls
  navigation.navigate("Profile", { userId: 123 })
}
```

### ✅ **Internationalization** - i18n Ready System
**Location**: `app/i18n/` directory
```typescript
// ✅ Use translation keys for all user-facing text
import { TxKeyPath, translate } from "@/i18n"

const welcomeText = translate("welcomeScreen.title")
// Supports multiple languages: en, es, fr, ar, hi, ja, ko
```

### ✅ **Safe Area Handling** - Mobile-Optimized Layout
**Location**: `app/utils/useSafeAreaInsetsStyle.ts`
```typescript
// ✅ Always handle device safe areas properly
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"

const $containerStyle = useSafeAreaInsetsStyle(["top", "bottom"])
```

### ✅ **Accessibility Support** - Built-in a11y Patterns
All components include proper accessibility props:
```typescript
// ✅ Accessibility is already built into base components
<Button 
  text="Submit"
  accessibilityLabel="Submit form"
  accessibilityHint="Submits the current form data"
/>
```

## 🏗️ **Component Structure Rules**

### File Size Limits
- **Main components**: 100-300 lines maximum
- **Sub-components**: 50-150 lines maximum  
- **Custom hooks**: 20-100 lines maximum
- **Utility functions**: Extract to separate files when > 50 lines

### Single Responsibility Principle
- Each component should have ONE clear purpose
- If a component handles multiple concerns, split it into smaller components
- Use composition over inheritance

### Component Organization
```
src/components/
├── [feature]/              # Group by feature, not by type
│   ├── [MainComponent]/    # Folder for complex components
│   │   ├── index.tsx       # Main component (entry point)
│   │   ├── [SubComponent].tsx
│   │   ├── hooks/          # Feature-specific hooks
│   │   ├── types.ts        # Component-specific types
│   │   └── constants.ts    # Component-specific constants
│   └── [SimpleComponent].tsx # Simple components can be single files
├── shared/                 # Reusable across features
└── ui/                    # Design system components
```

## 📝 **TypeScript Rules**

### Type Definitions
- **ALWAYS** define interfaces for props, state, and data structures
- Use `interface` for object shapes, `type` for unions/intersections
- Export types that are used by multiple components
- Use generics for reusable components

```typescript
// ✅ Good
interface EventCardProps {
  event: Event;
  onEventClick: (eventId: number) => void;
  isJoined?: boolean;
}

// ❌ Bad - no type definitions
const EventCard = ({ event, onEventClick, isJoined }) => {
```

### Naming Conventions
- **PascalCase**: Components, interfaces, types
- **camelCase**: variables, functions, hooks
- **SCREAMING_SNAKE_CASE**: constants
- **kebab-case**: file names (except components)

## ⚛️ **React Best Practices**

### Component Design
- Use functional components with hooks
- Prefer composition over complex prop interfaces
- Extract custom hooks for complex state logic
- Use `React.memo()` for expensive components

### State Management
- Keep state as close to where it's used as possible
- Use context sparingly - only for truly global state
- Extract complex state logic to custom hooks
- Use `useState` for simple state, `useReducer` for complex state

### Props and Data Flow
- Avoid prop drilling beyond 2-3 levels
- Use context or state management for deeply nested data
- Make props as specific as possible
- Use callback props for child-to-parent communication

### Performance
- Use `useMemo()` for expensive calculations
- Use `useCallback()` for functions passed as props
- Implement lazy loading for large components
- Avoid creating objects/functions in render

```typescript
// ✅ Good
const memoizedValue = useMemo(() => expensiveCalculation(data), [data]);
const handleClick = useCallback((id: number) => {
  onItemClick(id);
}, [onItemClick]);

// ❌ Bad
const value = expensiveCalculation(data); // Runs on every render
const handleClick = (id: number) => onItemClick(id); // New function on every render
```

## 🎯 **Component Extraction Rules**

### When to Extract a Component
1. **Reusability**: Used in 2+ places
2. **Complexity**: > 50 lines of JSX
3. **Responsibility**: Handles a distinct concern
4. **Testing**: Needs independent testing

### Extraction Priority
1. **Extract data logic** to custom hooks first
2. **Extract UI sections** into sub-components
3. **Extract constants** and types to separate files
4. **Extract utilities** to helper functions

### Custom Hooks
- Prefix with `use`
- Return objects for multiple values
- Keep hooks focused on single concerns
- Extract side effects from components

```typescript
// ✅ Good
const useEventManagement = (communityId: string) => {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(false);
  
  const createEvent = useCallback(async (eventData: CreateEventData) => {
    // logic here
  }, []);
  
  return { events, loading, createEvent };
};
```

## 📁 **File Organization Rules**

### Folder Structure
- **Feature-based**: Group related components together
- **Barrel exports**: Use index.ts files for clean imports
- **Separate concerns**: Types, constants, utils in separate files

### Import Organization
```typescript
// 1. React imports
import React, { useState, useEffect } from 'react';

// 2. Third-party imports
import { Button } from '@/components/ui/button';

// 3. Internal imports (relative)
import { useEventData } from './hooks/useEventData';
import { EVENT_TYPES } from './constants';
import type { EventProps } from './types';
```

### File Naming
- **Components**: PascalCase (`EventCard.tsx`)
- **Hooks**: camelCase starting with 'use' (`useEventData.ts`)
- **Types**: camelCase (`eventTypes.ts`)
- **Constants**: camelCase (`eventConstants.ts`)
- **Utils**: camelCase (`eventUtils.ts`)

## 🔧 **Code Quality Rules**

### General Guidelines
1. **ALWAYS** use TypeScript strict mode
2. **FOLLOW** ESLint configuration
3. **USE** Prettier for formatting
4. **WRITE** meaningful variable names
5. **ADD** JSDoc comments for complex functions
6. **PREFER** composition over inheritance
7. **KEEP** components small and focused
8. **EXTRACT** reusable logic into hooks

### Error Handling
- Always handle async operations with try/catch
- Use error boundaries for component errors
- Provide meaningful error messages
- Log errors appropriately
- Use try-catch in async operations
- Provide user-friendly error messages

```typescript
// ✅ Good error handling
const handleSubmit = async () => {
  try {
    setLoading(true);
    await submitData(formData);
  } catch (error) {
    console.error('Submit failed:', error);
    setError('Failed to submit. Please try again.');
  } finally {
    setLoading(false);
  }
};
```

### Accessibility
- Use semantic HTML elements
- Include ARIA labels where needed
- Ensure keyboard navigation works
- Test with screen readers
- Include testID props for important elements

```typescript
// ✅ Mobile accessibility patterns
<Button
  accessible={true}
  accessibilityLabel="Submit form"
  accessibilityHint="Submits the current form data"
  accessibilityRole="button"
  onPress={handleSubmit}
/>
```

### Security Guidelines
1. **NEVER** commit sensitive data
2. **USE** environment variables for API keys
3. **VALIDATE** all user inputs
4. **USE** secure storage for sensitive data

```typescript
// ✅ Secure storage for mobile
import { MMKV } from "react-native-mmkv"

const secureStorage = new MMKV({
  id: "secure-storage",
  encryptionKey: "your-encryption-key"
})

secureStorage.set("user.token", authToken)
```

## 🚀 **Performance Best Practices**

### Optimization Guidelines
1. **USE** `observer` for all components accessing stores
2. **USE** `useCallback` for event handlers in lists
3. **USE** `useMemo` for expensive calculations
4. **PREFER** `FlashList` over `FlatList` for large lists
5. **LAZY LOAD** heavy screens and components

### Mobile List Performance
```typescript
import { FlashList } from "@shopify/flash-list"

// ✅ Use FlashList for large datasets
<FlashList
  data={items}
  renderItem={renderItem}
  estimatedItemSize={60}
  keyExtractor={(item) => item.id}
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  windowSize={10}
/>
```

### Image Optimization
```typescript
// ✅ Use optimized image component
<AutoImage
  source={{ uri: imageUrl }}
  style={$imageStyle}
  resizeMode="cover"
  // Automatic loading states
/>
```

### Memory Management
```typescript
// ✅ Cleanup in useEffect
useEffect(() => {
  const subscription = someService.subscribe()
  
  return () => {
    subscription.unsubscribe()
  }
}, [])
```

### Bundle Size
- Use dynamic imports for large components
- Avoid importing entire libraries
- Tree-shake unused code
- Monitor bundle size regularly

### Runtime Performance
- Minimize re-renders with proper dependencies
- Use React DevTools Profiler
- Optimize expensive operations
- Cache API responses appropriately

## 🧪 **Testing Guidelines**

### Test Structure
```typescript
import { render, screen } from "@testing-library/react-native"
import { ComponentName } from "./ComponentName"

describe("ComponentName", () => {
  it("should render correctly", () => {
    render(<ComponentName />)
    expect(screen.getByText("Expected Text")).toBeTruthy()
  })
})
```

### Testing Rules
1. **USE** React Native Testing Library
2. **INCLUDE** testID props for important elements
3. **TEST** user interactions and state changes
4. **USE** Maestro for E2E tests

### Mobile Testing Strategy
```bash
# Device testing
npm run ios     # iOS Simulator
npm run android # Android Emulator

# E2E testing with Maestro
# .maestro/test-flow.yaml
```

### E2E Testing with Maestro
```yaml
# .maestro/test-flow.yaml
appId: com.padelcommunityapp
---
- launchApp
- tapOn: "Login Button"
- inputText: "<EMAIL>"
- tapOn: "Submit"
- assertVisible: "Welcome Screen"
```

### What to Test
- Component rendering with different props
- User interactions (clicks, form submissions)
- Custom hook behavior
- Error states and edge cases

### Testing Structure
```
src/
├── components/
│   └── EventCard/
│       ├── EventCard.tsx
│       ├── EventCard.test.tsx
│       └── __mocks__/
```

## 📦 **Asset Management Best Practices**

### Asset Organization
```
assets/
├── icons/          # App icons and small graphics
├── images/         # Larger images and photos
└── fonts/          # Custom fonts
```

### Asset Guidelines
1. **USE** `require()` for local assets
2. **PROVIDE** multiple resolutions (@2x, @3x)
3. **OPTIMIZE** images for mobile
4. **USE** vector formats when possible

## 🚢 **Build & Deployment Best Practices**

### EAS Build Configuration
- Use profiles: development, preview, production
- Always test on physical devices
- Use proper versioning

```json
// eas.json
{
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal"
    },
    "production": {}
  }
}
```

### Deployment Guidelines
1. **TEST** thoroughly before production builds
2. **USE** staging environment for testing
3. **FOLLOW** semantic versioning
4. **UPDATE** changelog for releases

### Build Commands
```bash
# Development build
eas build --profile development --platform ios

# Production build
eas build --profile production --platform all
```

### Mobile Development Checklist

#### Before Development
- [ ] Understand target devices and OS versions
- [ ] Review platform design guidelines
- [ ] Plan for offline scenarios
- [ ] Consider accessibility requirements

#### During Development
- [ ] Test on physical devices regularly
- [ ] Handle safe areas and notches
- [ ] Optimize for touch interactions
- [ ] Implement proper loading states
- [ ] Add accessibility support

#### Before Release
- [ ] Test on various screen sizes
- [ ] Verify offline functionality
- [ ] Check performance with large datasets
- [ ] Validate accessibility
- [ ] Test on slow networks

## 📋 **Code Review Checklist**

### Before Submitting
- [ ] Component is under size limits
- [ ] Types are properly defined
- [ ] No prop drilling beyond 2 levels
- [ ] Error handling is implemented
- [ ] Performance optimizations applied where needed
- [ ] Tests are written and passing
- [ ] Accessibility requirements met
- [ ] No console.logs in production code
- [ ] Safe areas properly handled (mobile)
- [ ] Touch targets meet minimum requirements (mobile)

### Refactoring Triggers
- Component exceeds 300 lines
- Function exceeds 50 lines
- Prop drilling beyond 2-3 levels
- Duplicate code in multiple places
- Complex state management in component
- Performance issues identified

## 🔄 **Migration Strategy**

### For Existing Large Components
1. **Audit current component**
2. **Identify extraction opportunities**
3. **Start with data logic** (custom hooks)
4. **Extract UI sections** incrementally
5. **Move types and constants**
6. **Test after each extraction**
7. **Update imports across codebase**

## 📱 **Mobile-Native Development**

For comprehensive mobile-native development rules and iOS/Android conversion guidelines, see **[mobile-guide.mdc](mdc:mobile-guide.mdc)**.

Key mobile considerations:
- Screen-based architecture over conditional rendering
- Navigation parameter management
- Touch-friendly UI patterns (44pt minimum touch targets)
- Style abstraction for React Native compatibility
- Performance optimization for mobile constraints

## ⚡ **Quick Reference**

### 🏆 **Use These Existing Patterns FIRST**
- **Theming**: `const { themed } = useAppTheme()` → `app/utils/useAppTheme.ts`
- **Icons**: `<Icon icon="back" />` → `app/components/Icon.tsx`
- **Buttons**: `<Button preset="filled" />` → `app/components/Button.tsx`
- **Text**: `<Text preset="heading" />` → `app/components/Text.tsx`
- **Screens**: `<Screen preset="scroll" />` → `app/components/Screen.tsx`
- **Navigation**: `AppStackScreenProps<"Screen">` → `app/navigators/`
- **State**: `const { authStore } = useStores()` → `app/models/`
- **i18n**: `translate("key")` → `app/i18n/`
- **Safe Areas**: `useSafeAreaInsetsStyle(["top"])` → `app/utils/`

### Mobile-Native Checklist
See **[mobile-guide.mdc](mdc:mobile-guide.mdc)** for detailed mobile-native development checklist

### Component Size Red Flags
- 🚨 **> 500 lines**: Immediate refactoring needed
- ⚠️ **300-500 lines**: Plan refactoring
- ✅ **< 300 lines**: Good size

### Extraction Patterns
- **Custom Hook**: `const { data, loading, error } = useFeature()`
- **Sub-component**: `<FeatureSection {...props} />`
- **Utility**: `const result = processData(input)`
- **Constant**: `export const FEATURE_CONFIG = { ... }`

---

*Follow these best practices to maintain clean, scalable, and maintainable code in the Padel Community Connect app.* 