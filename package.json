{"name": "padel-community-app", "version": "0.0.1", "private": true, "main": "index.tsx", "scripts": {"compile": "tsc --noEmit -p . --pretty", "lint": "eslint . --fix", "lint:check": "eslint .", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test -e MAESTRO_APP_ID=com.padelcommunityapp -e IS_DEV=true .maestro/flows", "test:maestro:ci": "maestro test -e MAESTRO_APP_ID=com.padelcommunityapp -e IS_DEV=false .maestro/flows", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "build:ios:sim": "eas build --profile development --platform ios --local --output sim.tar.gz && eas build:run --platform ios --path sim.tar.gz", "build:ios:dev": "eas build --profile development:device --platform ios --local", "build:ios:preview": "eas build --profile preview --platform ios --local", "build:ios:prod": "eas build --profile production --platform ios --local", "build:android:sim": "eas build --profile development --platform android --local", "build:android:dev": "eas build --profile development:device --platform android --local", "build:android:preview": "eas build --profile preview --platform android --local", "build:android:prod": "eas build --profile production --platform android --local", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "bundle:web": "npx expo export --platform web", "serve:web": "npx serve dist", "prebuild:clean": "npx expo prebuild --clean", "studio": "drizzle-kit studio --config ./drizzle.config.ts", "drizzle:generate": "drizzle-kit generate --config ./drizzle.config.ts"}, "dependencies": {"@expo/metro-runtime": "~4.0.0", "@expo/ngrok": "^4.1.3", "@gorhom/bottom-sheet": "^5.1.6", "@react-native-community/datetimepicker": "8.2.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@shopify/flash-list": "1.7.3", "@supabase/supabase-js": "^2.50.3", "apisauce": "3.0.1", "better-sqlite3": "^12.2.0", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "expo": "^52.0.44", "expo-application": "~6.0.1", "expo-asset": "~11.0.5", "expo-build-properties": "~0.13.1", "expo-dev-client": "~5.0.1", "expo-font": "~13.0.1", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.2", "expo-localization": "~16.0.0", "expo-router": "^5.1.3", "expo-splash-screen": "~0.29.10", "expo-sqlite": "~15.1.4", "expo-status-bar": "~2.0.0", "expo-system-ui": "~4.0.3", "i18next": "^23.14.0", "intl-pluralrules": "^2.0.1", "mobx": "6.10.2", "mobx-react-lite": "4.0.5", "mobx-state-tree": "5.3.0", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.0.1", "react-native": "0.76.9", "react-native-calendars": "^1.1313.0", "react-native-drawer-layout": "^4.0.1", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-keyboard-controller": "^1.12.7", "react-native-mmkv": "^2.12.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-url-polyfill": "^1.3.0", "react-native-web": "~0.19.6", "toastify-react-native": "^7.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@expo/metro-config": "^0.20.15", "@testing-library/react-native": "^12.5.2", "@types/better-sqlite3": "^7.6.13", "@types/jest": "^29.2.1", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/uuid": "^10.0.0", "babel-jest": "^29.2.1", "eslint": "^8.57.0", "eslint-config-expo": "~8.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-native": "^4.1.0", "eslint-plugin-reactotron": "^0.1.2", "jest": "^29.2.1", "jest-expo": "~52.0.1", "prettier": "^3.3.3", "react-native-vector-icons": "^10.2.0", "react-test-renderer": "18.2.0", "reactotron-core-client": "^2.9.4", "reactotron-mst": "^3.1.7", "reactotron-react-js": "^3.3.11", "reactotron-react-native": "^5.0.5", "reactotron-react-native-mmkv": "^0.2.6", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "engines": {"node": "^18.18.0 || >=20.0.0"}}