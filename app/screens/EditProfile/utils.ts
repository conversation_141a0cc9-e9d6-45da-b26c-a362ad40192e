import type { CountryCode, GenderOption, EditProfileFormData } from "./types"
import { translate } from "@/i18n"

// Country codes with flags for phone number selection
export const COUNTRY_CODES: CountryCode[] = [
  { code: "AE +971", label: "UAE +971", flag: "🇦🇪" },
  { code: "US +1", label: "USA +1", flag: "🇺🇸" },
  { code: "UK +44", label: "UK +44", flag: "🇬🇧" },
  { code: "ES +34", label: "Spain +34", flag: "🇪🇸" },
  { code: "FR +33", label: "France +33", flag: "🇫🇷" },
  { code: "DE +49", label: "Germany +49", flag: "🇩🇪" },
  { code: "SA +966", label: "Saudi Arabia +966", flag: "🇸🇦" },
]

// Gender options for selection
export const GENDER_OPTIONS: GenderOption[] = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
  { value: "Other", label: "Other" },
  { value: "Prefer not to say", label: "Prefer not to say" },
]

// Default profile data
export const DEFAULT_PROFILE: EditProfileFormData = {
  name: "",
  email: "",
  countryCode: "AE +971",
  phone: "",
  gender: "Male",
  dateOfBirth: new Date(1995, 5, 21), // June 21, 1995
  description: "",
  location: "",
}

// Validation functions
export const validateEmail = (email: string): string | null => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email) return translate("editProfileErrors:emailRequired")
  if (!emailRegex.test(email)) return translate("editProfileErrors:emailInvalid")
  return null
}

export const validateName = (name: string): string | null => {
  if (!name.trim()) return translate("editProfileErrors:nameRequired")
  if (name.trim().length < 2) return translate("editProfileErrors:nameTooShort")
  return null
}

export const validatePhone = (phone: string): string | null => {
  if (!phone) return translate("editProfileErrors:phoneRequired")
  const phoneRegex = /^[0-9]{7,15}$/
  if (!phoneRegex.test(phone.replace(/\s/g, ""))) {
    return translate("editProfileErrors:phoneInvalid")
  }
  return null
}

export const validateDescription = (description: string): string | null => {
  if (description.length > 160) return translate("editProfileErrors:descriptionTooLong")
  return null
}

export const validateLocation = (location: string): string | null => {
  if (!location.trim()) return translate("editProfileErrors:locationRequired")
  return null
}

export const validateAge = (dateOfBirth: Date): string | null => {
  const today = new Date()
  const age = today.getFullYear() - dateOfBirth.getFullYear()
  const monthDiff = today.getMonth() - dateOfBirth.getMonth()
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
    // Subtract 1 from age if birthday hasn't occurred this year
  }
  
  if (age < 13) return translate("editProfileErrors:minimumAge")
  if (age > 120) return translate("editProfileErrors:dateOfBirthInvalid")
  return null
}

// Form validation
export const validateForm = (formData: EditProfileFormData): Record<string, string> => {
  const errors: Record<string, string> = {}

  const nameError = validateName(formData.name)
  if (nameError) errors.name = nameError

  const emailError = validateEmail(formData.email)
  if (emailError) errors.email = emailError

  const phoneError = validatePhone(formData.phone)
  if (phoneError) errors.phone = phoneError

  const descriptionError = validateDescription(formData.description)
  if (descriptionError) errors.description = descriptionError

  const locationError = validateLocation(formData.location)
  if (locationError) errors.location = locationError

  const ageError = validateAge(formData.dateOfBirth)
  if (ageError) errors.dateOfBirth = ageError

  return errors
}

// Date formatting utility for display
export const formatDateForDisplay = (date: Date): string => {
  return `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}/${date.getFullYear()}`
}

// Generate initials from name for avatar placeholder
export const generateInitials = (name: string): string => {
  if (!name) return "?"
  
  const nameParts = name.trim().split(" ")
  if (nameParts.length === 1) {
    return nameParts[0].charAt(0).toUpperCase()
  }
  
  return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase()
}

// Check if form has unsaved changes
export const hasUnsavedChanges = (
  formData: EditProfileFormData,
  initialProfile: Partial<EditProfileFormData>
): boolean => {
  const fieldsToCheck: (keyof EditProfileFormData)[] = [
    "name",
    "email",
    "countryCode", 
    "phone",
    "gender",
    "description",
    "location",
  ]

  return fieldsToCheck.some(field => {
    const formValue = formData[field]
    const initialValue = initialProfile[field] || DEFAULT_PROFILE[field]
    
    if (field === "dateOfBirth") {
      const formDate = formData.dateOfBirth
      const initialDate = initialProfile.dateOfBirth || DEFAULT_PROFILE.dateOfBirth
      return formDate.getTime() !== initialDate.getTime()
    }
    
    return formValue !== initialValue
  })
} 