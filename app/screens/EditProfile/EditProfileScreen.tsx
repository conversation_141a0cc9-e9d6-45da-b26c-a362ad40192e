import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { EditProfileContent } from "./components/EditProfileContent"
import type { UserProfile } from "./types"
import { AppStackScreenProps } from "@/navigators"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"
import { SupabaseProfileService } from "@/services/supabase/profile-service"
import { useStores } from "@/models"
import { showErrorToast, showSuccessToast } from "@/utils/toast"
import { useHeader } from "@/utils/useHeader"
import { translate } from "@/i18n"
import { useEditProfileForm } from "./hooks"

interface EditProfileScreenProps extends AppStackScreenProps<"EditProfile"> {}

export const EditProfileScreen: FC<EditProfileScreenProps> = observer(function EditProfileScreen({
  navigation,
  route,
}) {
  const { initialProfile } = route.params

  // Convert date string (if any) to Date object
  const processedInitialProfile = initialProfile ? {
    ...initialProfile,
    dateOfBirth:
      typeof initialProfile.dateOfBirth === "string"
        ? new Date(initialProfile.dateOfBirth)
        : initialProfile.dateOfBirth,
  } : undefined

  // Local state holding profile used to populate the form
  const [profile, setProfile] = useState<UserProfile | undefined>(processedInitialProfile)
  const [loading, setLoading] = useState(false)

  const { userProfileStore } = useStores()

  useEffect(() => {
    if (!processedInitialProfile) {
      SupabaseProfileService.fetchCurrentUserProfile()
        .then((data) => {
          if (data) setProfile(data)
          if (data) {
            userProfileStore.setProfile({
              userId: userProfileStore.userId,
              displayName: data.name,
              email: data.email,
              countryCode: data.countryCode,
              phone: data.phone,
              gender: data.gender,
              dateOfBirth: data.dateOfBirth,
              description: data.description,
              location: data.location,
            })
          }
        })
        .catch((e) => console.error("Failed to fetch profile:", e))
    }
  }, [])

  // Form management hook
  const {
    formData,
    isValid,
    errors,
    handleFieldChange,
  } = useEditProfileForm({
    initialProfile: profile,
    onSave: () => {}, // We'll handle save at screen level
  })



  const handleBack = () => {
    navigation.goBack()
  }

  const handleSave = async () => {
    if (!isValid) {
      return
    }

    setLoading(true)
    try {
      await SupabaseProfileService.updateCurrentUserProfile(formData)
      // update cache store
      userProfileStore.setProfile({
        userId: userProfileStore.userId,
        displayName: formData.name,
        email: formData.email,
        countryCode: formData.countryCode,
        phone: formData.phone,
        gender: formData.gender,
        dateOfBirth: formData.dateOfBirth,
        description: formData.description,
        location: formData.location,
      })
      // Show success toast *before* closing the screen so it's visible
      showSuccessToast("Profile updated successfully")
      navigation.goBack()
    } catch (error) {
      console.error("Error saving profile:", error)
      showErrorToast("Failed to save profile. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Configure header with back button and save button
  useHeader({
    title: translate("editProfileScreen:editProfileTitle"),
    showBackButton: true,
    onBackPress: handleBack,
    rightButtons: [
      {
        text: translate("common:save"),
        onPress: handleSave,
        style: {
          opacity: (!isValid || loading) ? 0.5 : 1,
          pointerEvents: (!isValid || loading) ? 'none' : 'auto'
        },
      },
    ],
  }, [isValid, loading, formData])

  // Wrap with a local BottomSheetModalProvider because iOS modal presentations
  // render in a separate React tree that does not have access to the root-level
  // provider defined in `AppNavigator.tsx`. Placing the provider here ensures
  // the bottom sheet works consistently across platforms.
  return (
    <BottomSheetModalProvider>
      <EditProfileContent
        formData={formData}
        errors={errors}
        onFieldChange={handleFieldChange}
        loading={loading}
      />
    </BottomSheetModalProvider>
  )
})
