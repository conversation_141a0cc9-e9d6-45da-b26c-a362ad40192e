import { FC } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { observer } from "mobx-react-lite"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { generateInitials } from "../utils"

interface ProfileAvatarProps {
  name: string
  avatar?: string
  onImagePress: () => void
}

export const ProfileAvatar: FC<ProfileAvatarProps> = observer(function ProfileAvatar({
  name,
  avatar,
  onImagePress,
}) {
  const { themed } = useAppTheme()
  const initials = generateInitials(name)

  return (
    <View style={themed($container)}>
      <View style={themed($avatarContainer)}>
        <TouchableOpacity style={themed($avatar)} onPress={onImagePress}>
          <Text text={initials} style={themed($initialsText)} size="xxl" weight="bold" />
        </TouchableOpacity>
        <TouchableOpacity style={themed($cameraButton)} onPress={onImagePress}>
          <Icon icon="view" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
      <TouchableOpacity onPress={onImagePress}>
        <Text 
          tx="editProfileScreen:changeProfilePicture" 
          style={themed($changeText)} 
          size="sm" 
          weight="medium" 
        />
      </TouchableOpacity>
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginBottom: spacing.lg,
})

const $avatarContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  position: "relative",
  marginBottom: spacing.sm,
})

const $avatar: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 96,
  height: 96,
  borderRadius: 48,
  backgroundColor: colors.palette.secondary500,
  justifyContent: "center",
  alignItems: "center",
})

const $initialsText: ThemedStyle<TextStyle> = () => ({
  color: "#FFFFFF",
})

const $cameraButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: -4,
  right: -4,
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: colors.palette.secondary500,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 2,
  borderColor: colors.background,
})

const $changeText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.secondary500,
}) 