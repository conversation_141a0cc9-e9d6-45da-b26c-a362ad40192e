import { FC, useState } from "react"
import { View, ViewStyle, TextStyle, Modal, TouchableOpacity, Platform } from "react-native"
import DateTimePicker, { DateTimePickerEvent } from "@react-native-community/datetimepicker"
import { observer } from "mobx-react-lite"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface DatePickerModalProps {
  visible: boolean
  onClose: () => void
  onSelect: (date: Date) => void
  value: Date
  title?: string
  maximumDate?: Date
  minimumDate?: Date
  mode?: "date" | "time"
}

export const DatePickerModal: FC<DatePickerModalProps> = observer(function DatePickerModal({
  visible,
  onClose,
  onSelect,
  value,
  title = translate("editProfileScreen:selectDateTitle"),
  maximumDate,
  minimumDate,
  mode = "date",
}) {
  const { themed } = useAppTheme()
  const [selectedDate, setSelectedDate] = useState(value)

  // Normalize date to midnight in the device's locale to avoid timezone offset issues
  const normalizeDate = (date: Date) =>
    new Date(date.getFullYear(), date.getMonth(), date.getDate())

  const handleDateChange = (_event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      setSelectedDate(normalizeDate(date))
    }
  }

  const handleConfirm = () => {
    onSelect(normalizeDate(selectedDate))
    onClose()
  }

  const handleCancel = () => {
    setSelectedDate(value) // Reset to original value
    onClose()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      presentationStyle="overFullScreen"
      onRequestClose={onClose}
    >
      <View style={themed($overlay)}>
        <View style={themed($container)}>
          {/* Header */}
          <View style={themed($header)}>
            <TouchableOpacity onPress={handleCancel}>
              <Text tx="common:cancel" style={themed($cancelText)} size="md" />
            </TouchableOpacity>
            <Text text={title} style={themed($headerTitle)} size="lg" weight="semiBold" />
            <TouchableOpacity onPress={handleConfirm}>
              <Text tx="common:done" style={themed($doneText)} size="md" weight="medium" />
            </TouchableOpacity>
          </View>

          {/* Date Picker */}
          <View style={themed($pickerContainer)}>
            <DateTimePicker
              value={selectedDate}
              mode={mode}
              display="spinner"
              onChange={handleDateChange}
              maximumDate={maximumDate}
              minimumDate={minimumDate}
              style={themed($picker)}
            />
          </View>
        </View>
      </View>
    </Modal>
  )
})

const $overlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
})

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
  borderTopLeftRadius: 16,
  borderTopRightRadius: 16,
})

const $header: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

const $cancelText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral500,
})

const $doneText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.secondary500,
})

const $pickerContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: spacing.xl,
})

const $picker: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "transparent",
}) 