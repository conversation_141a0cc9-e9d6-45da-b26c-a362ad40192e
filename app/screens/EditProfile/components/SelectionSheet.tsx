import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { BottomSheetScrollView } from "@gorhom/bottom-sheet"

import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { BaseBottomSheet } from "@/components"
import { Text } from "@/components"
import { Icon } from "@/components"

export interface SelectionItem {
  value: string
  label: string
  flag?: string
}

interface SelectionSheetProps {
  visible: boolean
  onClose: () => void
  onSelect: (value: string) => void
  items: SelectionItem[]
  title: string
  selectedValue?: string
  snapPoints?: Array<string | number>
}

/**
 * Bottom-sheet picker used in Edit Profile (country, gender, etc.).
 */
export const SelectionSheet: FC<SelectionSheetProps> = observer(function SelectionSheet({
  visible,
  onClose,
  onSelect,
  items,
  title,
  selectedValue,
  snapPoints, // defaults handled by BaseBottomSheet
}) {
  const { themed } = useAppTheme()

  const handleSelect = (value: string) => {
    onSelect(value)
    onClose()
  }

  return (
    <BaseBottomSheet
      visible={visible}
      onClose={onClose}
      title={title}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
    >
      <BottomSheetScrollView
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
      >
        {items.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              themed($item),
              selectedValue === item.value && themed($selectedItem),
            ]}
            onPress={() => handleSelect(item.value)}
          >
            <View style={themed($itemContent)}>
              {item.flag && <Text text={item.flag} style={themed($flag)} size="lg" />}
              <Text
                text={item.label}
                style={[
                  themed($itemText),
                  selectedValue === item.value && themed($selectedItemText),
                ]}
                size="md"
                weight={selectedValue === item.value ? "medium" : "normal"}
              />
            </View>
            {selectedValue === item.value && <Icon icon="check" size={20} />}
          </TouchableOpacity>
        ))}
      </BottomSheetScrollView>
    </BaseBottomSheet>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $item: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: "#F3F4F6",
})

const $itemContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
  gap: spacing.sm,
})

const $flag: ThemedStyle<ViewStyle> = () => ({
  minWidth: 30,
})

const $itemText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $selectedItemText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.secondary500,
})

const $selectedItem: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral100,
}) 