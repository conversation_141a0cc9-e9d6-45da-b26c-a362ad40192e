import { useState, useCallback, useEffect } from "react"
import type { 
  UseEditProfileFormProps, 
  UseEditProfileFormReturn,
  EditProfileFormData 
} from "../types"
import { DEFAULT_PROFILE, validateForm } from "../utils"

/**
 * Hook for managing EditProfile form state and validation
 * Handles form data, validation, and field changes
 */
export const useEditProfileForm = ({
  initialProfile,
  onSave,
}: UseEditProfileFormProps): UseEditProfileFormReturn => {
  // Initialize form data with default values and initial profile
  const [formData, setFormData] = useState<EditProfileFormData>(() => ({
    ...DEFAULT_PROFILE,
    ...initialProfile,
    // Ensure dateOfBirth is a Date object
    dateOfBirth: initialProfile?.dateOfBirth 
      ? new Date(initialProfile.dateOfBirth) 
      : DEFAULT_PROFILE.dateOfBirth,
  }))

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isValid, setIsValid] = useState(false)

  // Validate form whenever formData changes
  useEffect(() => {
    const validationErrors = validateForm(formData)
    setErrors(validationErrors)
    setIsValid(Object.keys(validationErrors).length === 0)
  }, [formData])

  // When the parent provides a new initialProfile (e.g., after async fetch),
  // sync it into the form state.
  useEffect(() => {
    if (initialProfile) {
      setFormData((prev) => ({
        ...prev,
        ...initialProfile,
        dateOfBirth: initialProfile.dateOfBirth
          ? new Date(initialProfile.dateOfBirth)
          : prev.dateOfBirth,
      }))
    }
  }, [initialProfile])

  // Handle field changes with proper typing
  const handleFieldChange = useCallback((
    field: keyof EditProfileFormData, 
    value: string | Date
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }))
  }, [])

  // Handle form submission
  const handleSubmit = useCallback(() => {
    const validationErrors = validateForm(formData)
    
    if (Object.keys(validationErrors).length === 0) {
      onSave(formData)
    } else {
      setErrors(validationErrors)
    }
  }, [formData, onSave])

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData({
      ...DEFAULT_PROFILE,
      ...initialProfile,
      dateOfBirth: initialProfile?.dateOfBirth 
        ? new Date(initialProfile.dateOfBirth) 
        : DEFAULT_PROFILE.dateOfBirth,
    })
    setErrors({})
  }, [initialProfile])

  return {
    formData,
    isValid,
    errors,
    handleFieldChange,
    handleSubmit,
    resetForm,
  }
} 