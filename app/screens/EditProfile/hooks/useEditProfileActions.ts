import { useCallback } from "react"
import { Alert } from "react-native"
import { useImagePicker } from "@/hooks/useImagePicker"
import { safeAsync } from "@/utils/safeAsync"
import type { 
  UseEditProfileActionsProps, 
  UseEditProfileActionsReturn 
} from "../types"
import { hasUnsavedChanges } from "../utils"

/**
 * Hook for managing EditProfile user actions
 * Handles save, cancel, and image picker interactions
 */
export const useEditProfileActions = ({
  onClose,
  onSave,
  formData,
  isValid,
}: UseEditProfileActionsProps): UseEditProfileActionsReturn => {

  // Handle save action with validation
  const handleSave = useCallback(() => {
    if (!isValid) {
      Alert.alert(
        "Validation Error",
        "Please fix the errors in the form before saving.",
        [{ text: "OK" }]
      )
      return
    }

    safeAsync(() => Promise.resolve(onSave(formData)), {
      onError: () =>
        Alert.alert(
          "Save Error",
          "There was an error saving your profile. Please try again.",
          [{ text: "OK" }],
        ),
    })
  }, [formData, isValid, onSave])

  // Handle cancel action with unsaved changes check
  const handleCancel = useCallback(() => {
    const hasChanges = hasUnsavedChanges(formData, {})

    if (hasChanges) {
      Alert.alert(
        "Discard Changes",
        "You have unsaved changes. Are you sure you want to discard them?",
        [
          {
            text: "Keep Editing",
            style: "cancel",
          },
          {
            text: "Discard",
            style: "destructive",
            onPress: onClose,
          },
        ]
      )
    } else {
      onClose()
    }
  }, [formData, onClose])

  // Use shared image picker hook (placeholder implementation)
  const { handleImagePicker } = useImagePicker("Change Profile Picture")

  return {
    handleSave,
    handleCancel,
    handleImagePicker,
  }
} 