import { observer } from "mobx-react-lite"
import { FC } from "react"
import { Screen } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { $styles } from "@/theme"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { useAppTheme } from "@/utils/useAppTheme"
import { useHeader } from "@/utils/useHeader"
import { translate } from "@/i18n"
import { ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

import { useEventsData, useEventsActions, useEventsNavigation, useEventsScrollToTop } from "./hooks"
import { EventsContent } from "./components"
import { scrollManager } from "./utils"

export const EventsScreen: FC<AppStackScreenProps<"Events">> = observer(
  function EventsScreen(props) {
    const {
      themed,
      theme: { colors },
    } = useAppTheme()

    // Data hook - only events data
    const { filteredEvents } = useEventsData()

    // Business actions hook - event business logic
    const { toggleFilter, handleJoinEvent, handleCreatePress } = useEventsActions({ navigation: props.navigation })

    // Navigation hook - all navigation actions
    const { handleEventPress, handleBackPress } = useEventsNavigation({
        navigation: props.navigation,
      })

    // Scroll management hook
    const { eventsListRef } = useEventsScrollToTop()

    // Configure native header
    useHeader({
      title: translate("eventsScreen:title"),
      showBackButton: true,
      onBackPress: handleBackPress,
      rightButtons: [
        {
          icon: "add",
          onPress: handleCreatePress,
          style: themed($createHeaderButton),
          textColor: colors.palette.neutral100,
        },
      ],
    })

    return (
      <Screen
        preset="fixed"
        safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
        contentContainerStyle={$styles.flex1}
        statusBarStyle="light"
        backgroundColor={colors.background}
      >
        {/* Content */}
        <EventsContent
          eventsListRef={eventsListRef}
          filteredEvents={filteredEvents}
          onEventPress={handleEventPress}
          onJoinEvent={handleJoinEvent}
        />
      </Screen>
    )
  },
)

// Export scroll function for use in navigator
export const triggerEventsScreenScrollToTop = () => {
  scrollManager.triggerScrollToTop()
}

const $createHeaderButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.success,
  padding: 8,
  borderRadius: 20,
})
