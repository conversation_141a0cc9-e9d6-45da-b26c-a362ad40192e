import type { User } from "./types"

// Global reference to track active screen and scroll function
let activeEventsScreenScrollToTop: (() => void) | null = null

/**
 * Manages the global scroll-to-top functionality for EventsScreen
 */
export const scrollManager = {
  /**
   * Sets the active scroll function for the events screen
   */
  setActiveScrollFunction: (scrollFn: (() => void) | null) => {
    activeEventsScreenScrollToTop = scrollFn
  },

  /**
   * Triggers scroll to top if an active function is set
   */
  triggerScrollToTop: () => {
    if (activeEventsScreenScrollToTop) {
      activeEventsScreenScrollToTop()
    }
  },
}

/**
 * Gets user greeting data
 * TODO: Replace with actual user data from store/API
 */
export const getUserData = (): User => {
  return {
    name: "<PERSON>",
    greeting: "Hi, <PERSON>!",
  }
}

/**
 * Gets the create button style configuration
 */
export const getCreateButtonStyle = (colors: any) => ({
  paddingHorizontal: 16,
  paddingVertical: 6,
  borderRadius: 20,
  backgroundColor: "#22c55e",
  minHeight: 36,
  justifyContent: "center" as const,
})

/**
 * Gets the create button text style configuration
 */
export const getCreateButtonTextStyle = () => ({
  fontSize: 14,
  fontWeight: "600" as const,
  textAlign: "center" as const,
})
