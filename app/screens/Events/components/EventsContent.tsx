import { FC } from "react"
import { EventsList } from "@/components/EventsList"
import { ListViewRef } from "@/components/ListView"

interface EventsContentProps {
  eventsListRef: React.RefObject<ListViewRef<any>>
  filteredEvents: any[]
  onJoinEvent: (event: any) => void
  onEventPress: (event: any) => void
}

export const EventsContent: FC<EventsContentProps> = ({
  eventsListRef,
  filteredEvents,
  onJoinEvent,
  onEventPress,
}) => {
  return (
    <EventsList
      ref={eventsListRef}
      events={filteredEvents}
      onJoinEvent={onJoinEvent}
      onEventPress={onEventPress}
    />
  )
}
