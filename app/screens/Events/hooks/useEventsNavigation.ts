interface UseEventsNavigationProps {
  navigation: any
}

export const useEventsNavigation = ({ navigation }: UseEventsNavigationProps) => {
  const handleEventPress = (event: any) => {
    if (event.status === "finished") {
      navigation.navigate("EventResult", { eventId: event.id })
    } else {
      navigation.navigate("EventDetail", { eventId: event.id })
    }
  }

  const handleBackPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack()
    }
  }

  return {
    // Navigation actions
    handleEventPress,
    handleBackPress,
  }
} 