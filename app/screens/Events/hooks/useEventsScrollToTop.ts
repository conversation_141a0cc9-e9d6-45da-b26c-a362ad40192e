import { useRef, useCallback } from "react"
import { useFocusEffect } from "@react-navigation/native"
import { ListViewRef } from "@/components/ListView"
import { scrollManager } from "../utils"

export const useEventsScrollToTop = () => {
  const eventsListRef = useRef<ListViewRef<any>>(null)

  const scrollToTop = useCallback(() => {
    if (eventsListRef.current) {
      eventsListRef.current.scrollToOffset({ offset: 0, animated: true })
    }
  }, [])

  useFocusEffect(
    useCallback(() => {
      // Set this screen as the active one for scroll-to-top
      scrollManager.setActiveScrollFunction(scrollToTop)

      return () => {
        // Clear when screen loses focus
        scrollManager.setActiveScrollFunction(null)
      }
    }, [scrollToTop]),
  )

  return {
    eventsListRef,
    scrollToTop,
  }
}
