export interface CreateCommunityFormData {
  /** Community display name */
  name: string
  /** Short description (max ~160 chars) */
  shortDescription: string
  /** Full description / about community */
  description: string
  /** Rules for the community */
  rules: string[]
  /** Optional avatar image uri */
  avatar?: string
  /** Optional textual location */
  location: string
  /** Whether the community is private (invite-only) */
  isPrivate: boolean
  /** Whether members can invite others (only relevant if isPrivate) */
  allowMemberInvites: boolean
  /** Maximum allowed users (null or undefined for unlimited) */
  userLimit?: string
  /** Whether chat is enabled in the community */
  chatEnabled: boolean
  /** Whether moderation is enabled */
  moderationEnabled: boolean
}

// ────────────────────────────────────────────────────────────────────────────────
// Hook prop/return contracts

export interface UseCreateCommunityFormProps {
  /** Optional initial values (for editing flow) */
  initialData?: Partial<CreateCommunityFormData>
  /** Callback executed when form is successfully validated & submitted */
  onCreate: (data: CreateCommunityFormData) => void
}

export interface UseCreateCommunityFormReturn {
  formData: CreateCommunityFormData
  isValid: boolean
  errors: Record<string, string>
  handleFieldChange: (
    field: keyof CreateCommunityFormData,
    value: string | boolean,
  ) => void
  handleSubmit: () => void
  resetForm: () => void
}

export interface UseCreateCommunityActionsProps {
  /** Current creation step */
  step: 1 | 2 | 3
  setStep: (step: 1 | 2 | 3) => void
  onClose: () => void
  /** Form data needed for creation */
  formData: CreateCommunityFormData
  /** Whether the current form state is valid */
  isValid: boolean
  /** React Navigation object for performing app-wide navigation */
  navigation?: import("@/navigators").AppStackScreenProps<"CreateCommunity">["navigation"]
  /** Setter to toggle success modal visibility */
  setSuccessVisible?: (visible: boolean) => void
}

export interface UseCreateCommunityActionsReturn {
  handleNext: () => void
  handleBack: () => void
  handleClose: () => void
  handleHomePress: () => void
  handleSharePress: () => void
} 