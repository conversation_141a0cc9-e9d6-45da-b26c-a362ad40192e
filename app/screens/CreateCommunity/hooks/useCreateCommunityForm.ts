import { useState, useEffect, useCallback } from "react"
import type {
  CreateCommunityFormData,
  UseCreateCommunityFormProps,
  UseCreateCommunityFormReturn,
} from "../types"

// Default values for new community
const DEFAULT_COMMUNITY: CreateCommunityFormData = {
  name: "",
  shortDescription: "",
  description: "",
  rules: [],
  avatar: undefined,
  location: "",
  isPrivate: false,
  allowMemberInvites: true,
  chatEnabled: true,
  moderationEnabled: false,
}

// Simple validations (can be expanded later)
const validateName = (name: string): string | null => {
  if (!name.trim()) return "Community name is required"
  if (name.trim().length < 3) return "Name must be at least 3 characters"
  return null
}

const validateShortDescription = (shortDescription: string): string | null => {
  if (!shortDescription.trim()) return "Short description is required"
  if (shortDescription.length > 160) return "Short description must be under 160 characters"
  return null
}

const validateDescription = (description: string): string | null => {
  if (!description.trim()) return "Description is required"
  return null
}

const validateRules = (rules: string[]): string | null => {
  if (rules.length === 0) return "Rules are required"
  return null
}

const validateForm = (formData: CreateCommunityFormData): Record<string, string> => {
  const errors: Record<string, string> = {}
  const nameErr = validateName(formData.name)
  if (nameErr) errors.name = nameErr
  const shortDescErr = validateShortDescription(formData.shortDescription)
  if (shortDescErr) errors.shortDescription = shortDescErr
  const descErr = validateDescription(formData.description)
  if (descErr) errors.description = descErr
  const rulesErr = validateRules(formData.rules)
  if (rulesErr) errors.rules = rulesErr
  return errors
}

export function useCreateCommunityForm({
  initialData,
  onCreate,
}: UseCreateCommunityFormProps): UseCreateCommunityFormReturn {
  // Merge defaults with any provided initial data (for edit flow)
  const [formData, setFormData] = useState<CreateCommunityFormData>(() => ({
    ...DEFAULT_COMMUNITY,
    ...initialData,
  }))

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isValid, setIsValid] = useState(false)

  // Revalidate whenever form data changes
  useEffect(() => {
    const validation = validateForm(formData)
    setErrors(validation)
    setIsValid(Object.keys(validation).length === 0)
  }, [formData])

  const handleFieldChange = useCallback(
    (field: keyof CreateCommunityFormData, value: string | boolean) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }))
    },
    [],
  )

  const handleSubmit = useCallback(() => {
    const validation = validateForm(formData)
    if (Object.keys(validation).length === 0) {
      onCreate(formData)
    } else {
      setErrors(validation)
    }
  }, [formData, onCreate])

  const resetForm = useCallback(() => {
    setFormData({ ...DEFAULT_COMMUNITY, ...initialData })
    setErrors({})
  }, [initialData])

  return {
    formData,
    isValid,
    errors,
    handleFieldChange,
    handleSubmit,
    resetForm,
  }
} 