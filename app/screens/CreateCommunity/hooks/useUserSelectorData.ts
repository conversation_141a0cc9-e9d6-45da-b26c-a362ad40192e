import { useState, useMemo, useCallback } from "react"
import userData from "@/data/user_data.json"

export interface UserProfile {
  id: string
  name: string
  initials?: string
}

interface UseUserSelectorDataParams {
  /** Users that should appear as already selected on mount */
  initialSelected?: UserProfile[]
  /** Notified whenever the selection changes */
  onSelectionChange?: (selected: UserProfile[]) => void
}

/**
 * Feature-specific data hook powering the UserSelector.
 *
 *   • Loads the full user list from the bundled json.
 *   • Manages search & suggestion filtering.
 *   • Keeps selected users state and exposes add / remove helpers.
 *
 * All UI concerns stay in UserSelectorUI – this hook is PURE logic.
 */
export const useUserSelectorData = ({
  initialSelected = [],
  onSelectionChange,
}: UseUserSelectorDataParams) => {
  // 1️⃣  Prepare a flat array of user profiles from JSON once
  const allUsers: UserProfile[] = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore – raw JSON typing
    return Object.values(userData.users).map((u: any) => ({
      id: u.id as string,
      name: u.profile.name as string,
      initials: u.profile.initials as string,
    })) as UserProfile[]
  }, [])

  // 2️⃣  Local state
  const [searchQuery, setSearchQuery] = useState("")
  const [selected, setSelected] = useState<UserProfile[]>(initialSelected)

  // 3️⃣  Compute suggestions memo-ising heavy calculations
  const suggestions = useMemo(() => {
    // Exclude already selected users first
    const notSelected = allUsers.filter((u) => !selected.some((m) => m.id === u.id))

    // No search query ➡️ return random 10 suggestions for discovery
    if (searchQuery.trim().length === 0) {
      const shuffled = [...notSelected].sort(() => 0.5 - Math.random())
      return shuffled.slice(0, 10)
    }

    // Search by case-insensitive name match
    const query = searchQuery.toLowerCase()
    return notSelected.filter((u) => u.name.toLowerCase().includes(query))
  }, [allUsers, selected, searchQuery])

  // 4️⃣  Handlers
  const handleAdd = useCallback(
    (user: UserProfile) => {
      setSelected((prev) => {
        const updated = [...prev, user]
        onSelectionChange?.(updated)
        return updated
      })
    },
    [onSelectionChange],
  )

  const handleRemove = useCallback(
    (userId: string) => {
      setSelected((prev) => {
        const updated = prev.filter((u) => u.id !== userId)
        onSelectionChange?.(updated)
        return updated
      })
    },
    [onSelectionChange],
  )

  return {
    // State
    searchQuery,
    setSearchQuery,
    selected,
    // Derived
    suggestions,
    // Actions
    handleAdd,
    handleRemove,
  }
}

export type UseUserSelectorDataReturn = ReturnType<typeof useUserSelectorData>; 