import { useCallback } from "react"
import { <PERSON><PERSON>, Share } from "react-native"
import type {
  UseCreateCommunityActionsProps,
  UseCreateCommunityActionsReturn,
} from "../types"
import { safeAsync } from "@/utils/safeAsync"
import { SupabaseCommunityService } from "@/services/supabase/community-service"

export function useCreateCommunityActions({
  step,
  setStep,
  onClose,
  formData,
  isValid,
  navigation,
  setSuccessVisible,
}: UseCreateCommunityActionsProps): UseCreateCommunityActionsReturn {
  /* ------------------------------------------------------------------ */
  // Step navigation & community creation

  const handleNext = useCallback(() => {
    // Step 1 → 2
    if (step === 1) {
      return setStep(2)
    }
    // Step 2 → 3
    if (step === 2) {
      return setStep(3)
    }

    // Step 3 – validate & attempt creation
    if (!isValid) {
      Alert.alert(
        "Invalid Form",
        "Please fix the highlighted errors before continuing.",
      )
      return
    }

    SupabaseCommunityService.createCommunity({
      name: formData.name,
      shortDescription: formData.shortDescription,
      description: formData.description,
      rules: formData.rules,
      isPrivate: formData.isPrivate,
      maxUsers: formData.userLimit ? parseInt(formData.userLimit, 10) : null,
      chatEnabled: formData.chatEnabled,
      moderationEnabled: formData.moderationEnabled,
      location: formData.location,
      // TODO: location & other fields can be added later
    })
      .then(() => {
        // Show success modal
        setSuccessVisible?.(true)
      })
      .catch((error: unknown) => {
        Alert.alert(
          "Create Community Failed",
          (error as Error)?.message || "Please try again later.",
        )
      })
  }, [step, setStep, isValid, formData, setSuccessVisible])

  const handleBack = useCallback(() => {
    if (step > 1) {
      setStep((step - 1) as 1 | 2 | 3)
    }
  }, [step, setStep])

  /* ------------------------------------------------------------------ */
  // Close / discard flow

  const handleClose = useCallback(() => {
    Alert.alert(
      "Discard Community Creation?",
      "Are you sure you want to exit? Your progress will be lost.",
      [
        { text: "Stay", style: "cancel" },
        {
          text: "Discard",
          style: "destructive",
          onPress: () => {
            // Prefer provided onClose callback, fallback to nav.goBack()
            if (onClose) {
              onClose()
            } else {
              navigation?.goBack()
            }
          },
        },
      ],
    )
  }, [navigation, onClose])

  /* ------------------------------------------------------------------ */
  // Success modal actions

  const handleHomePress = useCallback(() => {
    setSuccessVisible?.(false)
    navigation?.reset({ index: 0, routes: [{ name: "Main" }] })
  }, [navigation, setSuccessVisible])

  const handleSharePress = useCallback(async () => {
    await safeAsync(async () => {
      await Share.share({
        message: "I just created a new community on Padel Community App! Join me.",
      })
    })
  }, [])

  return {
    handleNext,
    handleBack,
    handleClose,
    handleHomePress,
    handleSharePress,
  }
} 