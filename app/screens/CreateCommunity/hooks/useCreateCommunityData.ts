import { useState, useCallback } from "react"
import type { CreateCommunityFormData } from "../types"

/**
 * Centralized data hook for the Create Community flow.
 *
 * This hook is **data-only** – it should NOT contain any navigation or business
 * logic (handled by the dedicated navigation & actions hooks).
 *
 * Responsibilities:
 * 1. Hold the full form state across the 3-step flow so that information is
 *    persisted when users navigate between pages.
 * 2. Provide simple, memo-stable setters for each field/section.
 * 3. Expose a reset helper for discard flows.
 *
 * NOTE: This is the **minimal** v1 implementation. As the modal evolves (rules
 * list, moderators, members, etc.) extend the `CreateCommunityData` interface
 * and add the corresponding setters here – *never* inside UI components.
 */

// ────────────────────────────────────────────────────────────────────────────────
// Types

export interface CreateCommunityData {
  /** Basic info collected on the Details page */
  details: {
    name: string
    shortDescription: string
    description: string
    location: string
    rules: string[]
    avatar?: string
  }
  /** Settings selected on the Settings page */
  settings: {
    communityType: "Public" | "Private"
    limitEnabled: boolean
    userLimit: string // keep as string for easier TextField binding
    moderationEnabled: boolean
    chatEnabled: boolean
  }
  /** Initial member invitations selected on the Members page */
  members: Array<{ id: string; name: string; initials?: string }>
  /** Moderators selected on the Settings page (if moderation enabled) */
  moderators: Array<{ id: string; name: string; initials?: string }>
}

export interface UseCreateCommunityDataReturn {
  data: CreateCommunityData
  // Individual setters – these are memo-stable and can be safely passed to children
  setDetails: (details: CreateCommunityData["details"]) => void
  setSettings: (settings: CreateCommunityData["settings"]) => void
  setMembers: (members: CreateCommunityData["members"]) => void
  setModerators: (moderators: CreateCommunityData["moderators"]) => void
  // Field-level helpers (optional quality of life)
  updateField: <T extends keyof CreateCommunityFormData>(
    field: T,
    value: CreateCommunityFormData[T],
  ) => void
  // Reset back to the pristine state (used when discarding changes)
  reset: () => void
}

// ────────────────────────────────────────────────────────────────────────────────
// Default pristine state – keep in sync with future form changes

const DEFAULT_DATA: CreateCommunityData = {
  details: {
    name: "",
    shortDescription: "",
    description: "",
    location: "",
    rules: [],
    avatar: undefined,
  },
  settings: {
    communityType: "Public",
    limitEnabled: false,
    userLimit: "",
    moderationEnabled: false,
    chatEnabled: true,
  },
  members: [],
  moderators: [],
}

// ────────────────────────────────────────────────────────────────────────────────
// Hook implementation

export function useCreateCommunityData(initial?: Partial<CreateCommunityData>): UseCreateCommunityDataReturn {
  const [data, setData] = useState<CreateCommunityData>(() => ({
    ...DEFAULT_DATA,
    ...initial,
    details: { ...DEFAULT_DATA.details, ...initial?.details },
    settings: { ...DEFAULT_DATA.settings, ...initial?.settings },
    members: initial?.members ?? DEFAULT_DATA.members,
    moderators: initial?.moderators ?? DEFAULT_DATA.moderators,
  }))

  const setDetails = useCallback((details: CreateCommunityData["details"]) => {
    setData((prev) => ({ ...prev, details }))
  }, [])

  const setSettings = useCallback((settings: CreateCommunityData["settings"]) => {
    setData((prev) => ({ ...prev, settings }))
  }, [])

  const setMembers = useCallback((members: CreateCommunityData["members"]) => {
    setData((prev) => ({ ...prev, members }))
  }, [])

  const setModerators = useCallback((moderators: CreateCommunityData["moderators"]) => {
    setData((prev) => ({ ...prev, moderators }))
  }, [])

  const updateField = useCallback(<T extends keyof CreateCommunityFormData>(
    field: T,
    value: CreateCommunityFormData[T],
  ) => {
    setData((prev) => ({
      ...prev,
      details: {
        ...prev.details,
        [field]: value,
      },
    }))
  }, [])

  const reset = useCallback(() => {
    setData(DEFAULT_DATA)
  }, [])

  return {
    data,
    setDetails,
    setSettings,
    setMembers,
    setModerators,
    updateField,
    reset,
  }
} 