import type { ViewStyle, TextStyle } from "react-native"
import type { ThemedStyle } from "@/theme"
import { colors as themeColors } from "@/theme/colors"

// Styles extracted from CreateCommunityScreen

export const $keyboardContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

export const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

export const $buttonContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.md,
  alignItems: "center",
})

export const $nextButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.secondary500,
  minHeight: 48,
  width: "80%",
  borderRadius: 24,
})

export const $nextButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
})

export const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

export const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: spacing.lg,
  paddingBottom: spacing.xl,
  paddingHorizontal: spacing.md,
})

export const $progressContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingTop: spacing.md,
})

export const $progressLabel: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  marginLeft: spacing.sm,
  color: colors.text,
})

// Non-themed style – kept as plain object to avoid unnecessary theme re-computations
export const $progressBarWrapper = {
  flex: 1,
} as ViewStyle

// Color constants (non-themed)
export const NEXT_ICON_COLOR = themeColors.palette.neutral100 