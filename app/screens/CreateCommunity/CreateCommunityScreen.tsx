import { observer } from "mobx-react-lite"
import React, { FC, useState } from "react"
import { View, ScrollView, KeyboardAvoidingView, Platform, Modal } from "react-native"
import { Screen, Button, Icon, ProgressBar, Text, Success } from "@/components"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useCreateCommunityActions, useCreateCommunityData } from "./hooks"
import type { CreateCommunityFormData } from "./types"
import { ModalHeader } from "./shared"
import { Details } from "./components/Details"
import { Members } from "./components/Members"
import { Settings } from "./components/Settings"
import { STEP_CONFIG, TOTAL_STEPS, calculateProgressPercentage } from "./utils"
import { translate } from "@/i18n"
import {
  $keyboardContainer,
  $container,
  $buttonContainer,
  $nextButton,
  $nextButtonText,
  $scrollView,
  $scrollContent,
  $progressContainer,
  $progressLabel,
  $progressBarWrapper,
  NEXT_ICON_COLOR,
} from "./styles"

interface CreateCommunityScreenProps extends AppStackScreenProps<"CreateCommunity"> {}

export const CreateCommunityScreen: FC<CreateCommunityScreenProps> = observer(function CreateCommunityScreen({ route, navigation }) {
  const isIOS = Platform.OS === "ios"
  const [visible, setVisible] = useState(!isIOS)

  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  const [step, setStep] = useState<1 | 2 | 3>(1)
  const [successVisible, setSuccessVisible] = useState(false)

  const isEditMode = route.params?.mode === "edit"

  // Centralized form data hook – holds state across all steps. Prefill if initialData provided
  const { data, setDetails, setSettings, setMembers, setModerators } = useCreateCommunityData(route.params?.initialData)

  // Consolidated actions & navigation hook
  const {
    handleClose: _handleDiscard,
    handleNext,
    handleBack,
    handleHomePress,
    handleSharePress,
  } = useCreateCommunityActions({
    step,
    setStep,
    // Custom onClose: hide Android modal then navigate back
    onClose: () => {
      if (!isIOS) setVisible(false)
      navigation.goBack()
    },
    // Map form data into the expected structure
    formData: {
      name: data.details.name,
      shortDescription: data.details.shortDescription,
      description: data.details.description,
      rules: data.details.rules,
      location: data.details.location,
      avatar: data.details.avatar,
      isPrivate: data.settings.communityType === "Private",
      allowMemberInvites: true,
      userLimit: data.settings.limitEnabled ? data.settings.userLimit : undefined,
      chatEnabled: data.settings.chatEnabled,
      moderationEnabled: data.settings.moderationEnabled,
    } as CreateCommunityFormData,
    isValid: true,
    navigation,
    setSuccessVisible,
  })

  // Local close handler for header (back button X)
  const handleClose = _handleDiscard

  const { title: rawTitle, nextLabel } = STEP_CONFIG[step]
  const pageTitle = isEditMode && step === 1 ? translate("createCommunityScreen:editCommunityTitle") : rawTitle
  const finalNextLabel = step === 3 && isEditMode ? translate("common:save") : nextLabel

  const innerContent = (
    <Screen preset="fixed" safeAreaEdges={[]} contentContainerStyle={themed($container)}>
      {/* Header */}
      <ModalHeader title={pageTitle} onClose={handleClose} onBack={step > 1 ? handleBack : undefined} />

      {/* Progress Bar */}
      <View style={themed($progressContainer)}>
        <View style={$progressBarWrapper}>
          <ProgressBar segments={TOTAL_STEPS} filledSegments={step} height={6} />
        </View>
        <Text text={`${step}/${TOTAL_STEPS}`} style={themed($progressLabel)} size="sm" weight="medium" />
      </View>

      {/* Content */}
      <KeyboardAvoidingView style={themed($keyboardContainer)} behavior={Platform.OS === "ios" ? "padding" : "height"}>
        <ScrollView
          style={themed($scrollView)}
          contentContainerStyle={[themed($scrollContent), $bottomInset]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {step === 1 && <Details details={data.details} onChange={setDetails} />}
          {step === 2 && (
            <Settings
              values={data.settings}
              onChange={setSettings}
              moderators={data.moderators}
              onModeratorsChange={setModerators}
            />
          )}
          {step === 3 && <Members members={data.members} onChange={setMembers} />}
        </ScrollView>

        {/* Bottom Next button */}
        <View style={[themed($buttonContainer), $bottomInset]}>
          <Button
            {...(!isEditMode && step !== 3 ? { tx: "createCommunityScreen:nextButton" } : { text: finalNextLabel })}
            preset="filled"
            RightAccessory={(props) => <Icon icon="caretRight" size={20} color={NEXT_ICON_COLOR} {...props} />}
            style={themed($nextButton)}
            textStyle={themed($nextButtonText)}
            onPress={handleNext}
          />
        </View>
      </KeyboardAvoidingView>

      {/* Success Modal */}
      <Success
        visible={successVisible}
        onClose={() => setSuccessVisible(false)}
        buttons={[
          { text: translate("createCommunityScreen:viewButton"), onPress: () => {} },
          { icon: "home", onPress: handleHomePress },
          { icon: "share", onPress: handleSharePress },
        ]}
      />
    </Screen>
  )

  const content = <BottomSheetModalProvider>{innerContent}</BottomSheetModalProvider>

  if (isIOS) return content

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <BottomSheetModalProvider>
        {innerContent}
      </BottomSheetModalProvider>
    </Modal>
  )
})
