import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle } from "react-native"
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete"
import { BaseBottomSheet, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

export interface LocationSelectionProps {
  visible: boolean
  onClose: () => void
  onSelect: (location: string) => void
}

export const LocationSelection: FC<LocationSelectionProps> = observer(function LocationSelection({ visible, onClose, onSelect }) {
  const {
    themed,
    theme: { colors, spacing },
  } = useAppTheme()

  return (
    <BaseBottomSheet
      visible={visible}
      onClose={onClose}
      title={undefined}
      snapPoints={["60%", "75%"]}
      enableDynamicSizing={false}
    >
      <View style={themed($content)}>
        <GooglePlacesAutocomplete
          fetchDetails={false}
          placeholder={translate("detailsAbout:searchPlaceholder")}
          query={{
            key: process.env.EXPO_PUBLIC_GOOGLE_PLACES_API_KEY || "",
            language: "en",
          }}
          onPress={(data /* , details */) => {
            onSelect(data.description)
          }}
          onFail={(error) => {
            console.warn("Google Places API error", error)
          }}
          debounce={300}
          renderLeftButton={() => (
            <View style={themed($searchLeft)}>
              <Icon icon="search" size={20} color={colors.textDim} />
            </View>
          )}
          styles={{
            textInputContainer: themed($searchContainer),
            textInput: themed($textInput),
            listView: themed($list),
            row: themed($row),
            separator: themed($separator),
          }}
        />
      </View>
    </BaseBottomSheet>
  )
})

const $content: ThemedStyle<ViewStyle> = ({ spacing }) => ({ flex: 1 })

const $searchContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  marginHorizontal: spacing.md,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  height: 40,
  paddingHorizontal: spacing.sm,
})

const $textInput: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  backgroundColor: "transparent",
  color: colors.text,
  height: 40,
  flex: 1,
  marginTop: 5,
})

const $searchLeft: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: spacing.md,
})

const $list: ThemedStyle<ViewStyle> = () => ({ flex: 1 })

const $row: ThemedStyle<ViewStyle> = ({ spacing }) => ({ paddingVertical: spacing.md, paddingHorizontal: spacing.md })

const $separator: ThemedStyle<ViewStyle> = ({ colors }) => ({ height: 1, backgroundColor: colors.separator }) 