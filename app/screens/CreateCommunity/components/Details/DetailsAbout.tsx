import { FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle } from "react-native"
import { TextField, Text, PressableIcon } from "@/components"
import { LocationSelection } from "./LocationSelection"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface DetailsAboutProps {
  initialName?: string
  initialShortDescription?: string
  initialDescription?: string
  initialLocation?: string
  initialRules?: string[]
  onChange?: (values: { name: string; shortDescription: string; description: string; location: string; rules: string[] }) => void
}

export const DetailsAbout: FC<DetailsAboutProps> = observer(function DetailsAbout({
  initialName = "",
  initialShortDescription = "",
  initialDescription = "",
  initialLocation = "",
  initialRules = [],
  onChange,
}) {
  const { themed } = useAppTheme()
  const [name, setName] = useState(initialName)
  const [shortDescription, setShortDescription] = useState(initialShortDescription)
  const [description, setDescription] = useState(initialDescription)
  const [location, setLocation] = useState(initialLocation)
  const [rules, setRules] = useState<string[]>(initialRules)
  const [newRule, setNewRule] = useState("")
  const [locationSheetVisible, setLocationSheetVisible] = useState(false)

  function handleName(text: string) {
    setName(text)
    onChange?.({ name: text, shortDescription, description, location, rules })
  }

  function handleShortDescription(text: string) {
    setShortDescription(text)
    onChange?.({ name, shortDescription: text, description, location, rules })
  }

  function handleDescription(text: string) {
    setDescription(text)
    onChange?.({ name, shortDescription, description: text, location, rules })
  }

  function handleAddRule() {
    const trimmed = newRule.trim()
    if (!trimmed) return
    const updated = [...rules, trimmed]
    setRules(updated)
    setNewRule("")
    onChange?.({ name, shortDescription, description, location, rules: updated })
  }

  function handleDeleteRule(index: number) {
    const updated = rules.filter((_, i) => i !== index)
    setRules(updated)
    onChange?.({ name, shortDescription, description, location, rules: updated })
  }

  function handleLocationSelect(selected: string) {
    setLocation(selected)
    setLocationSheetVisible(false)
    onChange?.({ name, shortDescription, description, location: selected, rules })
  }

  return (
    <>
      <View style={themed($container)}>
        <Text tx="detailsAbout:communityDetailsTitle" size="lg" weight="semiBold" style={themed($title)} />

        <TextField
          labelTx="detailsAbout:communityNameLabel"
          value={name}
          onChangeText={handleName}
          placeholderTx="detailsAbout:communityNamePlaceholder"
          containerStyle={themed($fieldContainer)}
        />

        <TextField
          labelTx="detailsAbout:shortDescriptionLabel"
          value={shortDescription}
          onChangeText={handleShortDescription}
          placeholderTx="detailsAbout:shortDescriptionPlaceholder"
          containerStyle={themed($fieldContainer)}
        />

        <TextField
          labelTx="detailsAbout:descriptionLabel"
          value={description}
          onChangeText={handleDescription}
          placeholderTx="detailsAbout:descriptionPlaceholder"
          multiline
          containerStyle={themed($fieldContainer)}
        />

        {/* Location Field */}
        <TextField
          labelTx="detailsAbout:locationLabel"
          value={location}
          placeholderTx="detailsAbout:locationPlaceholder"
          editable={false}
          RightAccessory={(props) => (
            <PressableIcon
              icon="location"
              size={20}
              containerStyle={props.style}
              onPress={() => setLocationSheetVisible(true)}
            />
          )}
          containerStyle={themed($fieldContainer)}
          onPressIn={() => setLocationSheetVisible(true)}
        />

        <TextField
          labelTx="detailsAbout:rulesLabel"
          value={newRule}
          onChangeText={setNewRule}
          placeholderTx="detailsAbout:rulesPlaceholder"
          RightAccessory={(props) => (
            <PressableIcon
              icon="add"
              size={20}
              containerStyle={props.style}
              onPress={handleAddRule}
            />
          )}
          containerStyle={themed($fieldContainer)}
        />

        {rules.length > 0 && (
          <View style={themed($rulesListContainer)}>
            {rules.map((item, index) => (
              <View key={index} style={themed($ruleItem)}>
                <Text text={`${index + 1}. ${item}`} style={themed($ruleText)} />
                <PressableIcon icon="x" size={18} onPress={() => handleDeleteRule(index)} />
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Location Selection Bottom Sheet */}
      <LocationSelection
        visible={locationSheetVisible}
        onClose={() => setLocationSheetVisible(false)}
        onSelect={handleLocationSelect}
      />
    </>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
})

const $title: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $rulesListContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.md,
})

const $ruleItem: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: spacing.sm,
  borderBottomWidth: 1,
  borderColor: colors.separator,
})

const $ruleText: ThemedStyle<TextStyle> = ({ spacing }) => ({
  flex: 1,
  marginRight: spacing.sm,
})
