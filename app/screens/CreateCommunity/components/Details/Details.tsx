import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { CommunityAvatar } from "./CommunityAvatar"
import { DetailsAbout } from "./DetailsAbout"
import { useImagePicker } from "@/hooks/useImagePicker"

interface DetailsData {
  name: string
  shortDescription: string
  description: string
  rules: string[]
  avatar?: string
  location: string
}

interface DetailsProps {
  /** Controlled details data coming from useCreateCommunityData hook */
  details: DetailsData
  /** Change handler propagated back to central form state */
  onChange: (data: DetailsData) => void
}

export const Details: FC<DetailsProps> = observer(function Details({ details, onChange }) {
  const { themed } = useAppTheme()

  // Image picker hook (placeholder implementation)
  const { handleImagePicker } = useImagePicker("Change Community Picture")

  return (
    <View style={themed($container)}>
      <CommunityAvatar name={details.name} avatar={details.avatar} onImagePress={handleImagePicker} />

      <DetailsAbout
        initialName={details.name}
        initialShortDescription={details.shortDescription}
        initialDescription={details.description}
        initialLocation={details.location}
        initialRules={details.rules}
        onChange={(partial) => onChange({ ...details, ...partial })}
      />
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
}) 