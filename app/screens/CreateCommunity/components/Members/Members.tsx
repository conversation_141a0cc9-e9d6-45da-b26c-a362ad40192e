import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { UserSelector } from "../../shared"

// Shape of a selected member (matches CreateCommunityData.members)
export interface MemberProfile {
  id: string
  name: string
  initials?: string
}

interface MembersProps {
  /** Currently selected members */
  members: MemberProfile[]
  /** Handler triggered when selection changes */
  onChange: (members: MemberProfile[]) => void
}

export const Members: FC<MembersProps> = observer(function Members({ members, onChange }) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <UserSelector
        title="Members"
        initialSelected={members}
        onSelectionChange={onChange}
      />
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
}) 