import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { BasicSettings, BasicSettingsValues } from "./BasicSettings"
import { UserSelector } from "../../shared"
import { translate } from "@/i18n"
import type { MemberProfile } from "../Members/Members"

interface SettingsProps {
  /** Current settings values – controlled by parent */
  values: BasicSettingsValues
  /** Change handler to update settings in parent */
  onChange: (values: BasicSettingsValues) => void
  /** List of currently selected moderators */
  moderators: MemberProfile[]
  /** Handler when moderators selection changes */
  onModeratorsChange: (moderators: MemberProfile[]) => void
}

export const Settings: FC<SettingsProps> = observer(function Settings({ values, onChange, moderators, onModeratorsChange }) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Basic Settings Section */}
      <BasicSettings values={values} onChange={onChange} />

      {/* Moderators Section - shown only when moderation enabled */}
      {values.moderationEnabled && (
        <UserSelector
          title={translate("settingsScreen:moderatorsTitle")}
          initialSelected={moderators}
          onSelectionChange={onModeratorsChange}
        />
      )}
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
})
