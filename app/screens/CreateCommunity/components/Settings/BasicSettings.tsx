import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text, Switch, TextField, SegmentedSwitch } from "@/components"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

export interface BasicSettingsValues {
  communityType: "Public" | "Private"
  limitEnabled: boolean
  userLimit: string
  moderationEnabled: boolean
  /** Whether chat is enabled for the community */
  chatEnabled: boolean
}

interface BasicSettingsProps {
  values: BasicSettingsValues
  onChange: (values: BasicSettingsValues) => void
}

export const BasicSettings: FC<BasicSettingsProps> = observer(function BasicSettings({ values, onChange }) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  function handleCommunityType(type: "Public" | "Private") {
    onChange({ ...values, communityType: type })
  }

  function handleLimitEnabled(val: boolean) {
    const updated: BasicSettingsValues = {
      ...values,
      limitEnabled: val,
      // Clear limit if disabled
      userLimit: val ? values.userLimit : "",
    }
    onChange(updated)
  }

  function handleChatEnabled(enabled: boolean) {
    onChange({ ...values, chatEnabled: enabled })
  }

  function handleUserLimit(text: string) {
    onChange({ ...values, userLimit: text })
  }

  function handleModeration(val: boolean) {
    onChange({ ...values, moderationEnabled: val })
  }

  return (
    <View style={themed($container)}>
      {/* Community Type */}
      <View style={themed($rowBetween)}>
        <Text tx="basicSettings:communityType" size="md" weight="semiBold" />
        <SegmentedSwitch
          options={[
            { label: translate("basicSettings:public"), value: "Public" },
            { label: translate("basicSettings:private"), value: "Private" },
          ]}
          value={values.communityType}
          onChange={handleCommunityType}
          selectionColor={colors.palette.primary500}
          style={themed($segmented)}
        />
      </View>

      {/* Enable Chat */}
      <View style={themed($rowBetween)}>
        <Text tx="basicSettings:enableChat" size="md" weight="semiBold" />
        <SegmentedSwitch
          options={[
            { label: translate("basicSettings:yes"), value: true },
            { label: translate("basicSettings:no"), value: false },
          ]}
          value={values.chatEnabled}
          onChange={handleChatEnabled}
          selectionColor={colors.palette.primary500}
          style={themed($segmented)}
        />
      </View>

      {/* Max Users Limit */}
      <View style={themed($rowBetween)}>
        <Text tx="basicSettings:maxUsersLimit" size="md" weight="semiBold" />
        <Switch value={values.limitEnabled} onValueChange={handleLimitEnabled} />
      </View>
      {values.limitEnabled && (
        <TextField
          labelTx="basicSettings:enterUserLimit"
          placeholderTx="basicSettings:valuePlaceholder"
          keyboardType="numeric"
          value={values.userLimit}
          onChangeText={handleUserLimit}
          containerStyle={themed($fieldContainer)}
        />
      )}

      {/* Moderation */}
      <View style={themed($rowBetween)}>
        <Text tx="basicSettings:moderation" size="md" weight="semiBold" />
        <Switch value={values.moderationEnabled} onValueChange={handleModeration} />
      </View>
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
})

const $rowBetween: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: spacing.xl,
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $segmented: ThemedStyle<ViewStyle> = () => ({
  minWidth: 140,
})
