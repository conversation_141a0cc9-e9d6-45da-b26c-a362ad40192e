import { FC } from "react"
import { observer } from "mobx-react-lite"
import { useUserSelectorData, UserProfile } from "@/screens/CreateCommunity/hooks/useUserSelectorData"
import { UserSelectorUI } from "./UserSelectorUI"

export interface UserSelectorProps {
  /** Title shown above selected list (e.g., "Moderators", "Members") */
  title: string
  /** Callback when selected users change */
  onSelectionChange?: (selected: UserProfile[]) => void
  /** Pre-selected users */
  initialSelected?: UserProfile[]
}

export const UserSelector: FC<UserSelectorProps> = observer(function UserSelector({
  title,
  onSelectionChange,
  initialSelected = [],
}) {
  const {
    searchQuery,
    setSearchQuery,
    suggestions,
    selected,
    handleAdd,
    handleRemove,
  } = useUserSelectorData({ initialSelected, onSelectionChange })

  return (
    <UserSelectorUI
      title={title}
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      suggestions={suggestions}
      selected={selected}
      onAdd={handleAdd}
      onRemove={handleRemove}
    />
  )
}) 