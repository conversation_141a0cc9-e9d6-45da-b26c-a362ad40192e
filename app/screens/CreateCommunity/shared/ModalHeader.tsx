import { FC } from "react"
import { View, TouchableOpacity, ViewStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface ModalHeaderProps {
  title: string
  /**
   * Callback when the back arrow is pressed. If not provided, the back arrow is not rendered.
   */
  onBack?: () => void
  /**
   * Callback when the close (X) button is pressed.
   */
  onClose: () => void
}

export const ModalHeader: FC<ModalHeaderProps> = observer(function ModalHeader({
  title,
  onBack,
  onClose,
}) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($header)}>
      {/* Back Button (optional) */}
      {onBack ? (
        <TouchableOpacity onPress={onBack} style={themed($headerButton)}>
          <Icon icon="back" size={24} />
        </TouchableOpacity>
      ) : (
        // Maintain layout spacing when back button is absent
        <View style={themed([$headerButton, $placeholder])} />
      )}

      {/* Title */}
      <Text text={title} style={themed($headerTitle)} size="lg" weight="semiBold" />

      {/* Close Button */}
      <TouchableOpacity onPress={onClose} style={themed($headerButton)}>
        <Icon icon="x" size={24} />
      </TouchableOpacity>
    </View>
  )
})

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.sm,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  backgroundColor: colors.background,
})

const $headerButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  minWidth: 60,
  alignItems: "center",
  justifyContent: "center",
})

const $placeholder: ThemedStyle<ViewStyle> = () => ({
  opacity: 0,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
}) 