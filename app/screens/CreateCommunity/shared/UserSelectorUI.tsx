import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, ScrollView, Pressable, TextInput, TextStyle } from "react-native"
import { Avatar, Button, Icon, Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { UserProfile } from "@/screens/CreateCommunity/hooks/useUserSelectorData"

interface UserSelectorUIProps {
  /** Section title shown above the selected list */
  title: string
  /** Current search query */
  searchQuery: string
  /** Called when search input changes */
  onSearchChange: (query: string) => void
  /** Suggested users based on search */
  suggestions: UserProfile[]
  /** Currently selected users */
  selected: UserProfile[]
  /** Add user to selection */
  onAdd: (user: UserProfile) => void
  /** Remove user from selection */
  onRemove: (userId: string) => void
}

/**
 * Pure presentational component – **NO business logic**.
 * Renders search bar, suggestions carousel and selected list.
 */
export const UserSelectorUI: FC<UserSelectorUIProps> = observer(function UserSelectorUI({
  title,
  searchQuery,
  onSearchChange,
  suggestions,
  selected,
  onAdd,
  onRemove,
}) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($wrapper)}>
      <View style={themed($container)}>
        {/* 🔍 Search Bar */}
        <View style={themed($searchBarContainer)}>
          <Icon icon="view" size={20} color={colors.textDim} />
          <TextInput
            placeholder="Search users..."
            placeholderTextColor={colors.textDim}
            style={themed($searchInput)}
            value={searchQuery}
            onChangeText={onSearchChange}
          />
        </View>

        {/* 🎯 Suggestions */}
        <View style={themed($titleContainer)}>
          <Text text="Suggestions" preset="subheading" style={themed($sectionTitle)} />
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={themed($suggestionsContainer)}
          style={themed($suggestionsScroll)}
        >
          {suggestions.map((user) => (
            <View key={user.id} style={themed($suggestionItem)}>
              <View style={themed($suggestionContent)}>
                <Avatar
                  name={user.name}
                  size={48}
                  backgroundColor="#6366f1"
                  textSize="sm"
                  textWeight="medium"
                />
                <Text text={user.name.split(" ")[0]} style={themed($suggestionName)} size="xs" />
              </View>
              <Button
                text="Add"
                preset="default"
                style={themed($addButton)}
                textStyle={themed($addButtonText)}
                onPress={() => onAdd(user)}
              />
            </View>
          ))}
        </ScrollView>

        {/* ✅ Selected */}
        <View style={themed($titleContainer)}>
          <Text text={title} preset="subheading" style={themed($sectionTitle)} />
        </View>
        <View style={themed($listContainer)}>
          {selected.map((usr) => (
            <View key={usr.id} style={themed($listItem)}>
              <Avatar
                name={usr.name}
                size={40}
                backgroundColor="#6366f1"
                textSize="sm"
                textWeight="medium"
              />
              <Text text={usr.name} style={themed($listItemName)} weight="medium" />
              <Pressable onPress={() => onRemove(usr.id)} style={themed($deleteButton)}>
                <Icon icon="x" size={18} color={colors.palette.neutral600} />
              </Pressable>
            </View>
          ))}
        </View>
      </View>
    </View>
  )
})

// ————————————————————————————————————————————————————————————————
// STYLES – kept identical to previous implementation for visual parity
// ————————————————————————————————————————————————————————————————
const $wrapper: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginHorizontal: -spacing.md,
})

const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $searchBarContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.md,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  height: 44,
  marginHorizontal: spacing.lg,
})

const $searchInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  flex: 1,
  fontSize: 16,
  color: colors.text,
  marginLeft: 8,
})

const $titleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  marginTop: spacing.lg,
  marginBottom: spacing.sm,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
})

const $suggestionsScroll: ThemedStyle<ViewStyle> = () => ({
  flexGrow: 0,
})

const $suggestionsContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  gap: spacing.sm,
})

const $suggestionItem: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  width: 150, // Increased from 100 to make it more square
  height: 150,
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  padding: spacing.sm,
  borderRadius: spacing.md,
  justifyContent: "space-between",
})

const $suggestionContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  flex: 1,
  justifyContent: "flex-start",
  paddingTop: spacing.xxs,
})

const $suggestionName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  marginTop: 4,
  marginBottom: 4,
  textAlign: "center",
  fontSize: 18,
  fontWeight: "600",
})

const $addButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: spacing.xs,
  paddingVertical: spacing.xxxs,
  borderRadius: 6,
  minHeight: 24,
  minWidth: 60,
})

const $addButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 12,
  fontWeight: "600",
  textAlign: "center",
})

const $listContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs,
  paddingHorizontal: spacing.lg,
})

const $listItem: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  padding: spacing.sm,
  borderRadius: 8,
  borderColor: colors.palette.neutral300,
  borderWidth: 1,
  gap: spacing.sm,
})

const $listItemName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $deleteButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
}) 