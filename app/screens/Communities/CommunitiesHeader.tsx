import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Icon, TextField } from "@/components"
import { CommunityHeaderProps } from "./types"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const CommunitiesHeader: FC<CommunityHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onFilterPress,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($header)}>
      {/* Search Bar Container */}
      <View style={themed($searchContainer)}>
        <View style={themed($searchInputContainer)}>
          <Icon icon="view" size={20} color={colors.textDim} />
          <TextField
            value={searchQuery}
            onChangeText={onSearchChange}
            placeholderTx="communitiesScreen:searchCommunitiesPlaceholder"
            placeholderTextColor={colors.textDim}
            style={themed($searchInput)}
            containerStyle={themed($searchFieldContainer)}
            inputWrapperStyle={themed($searchFieldWrapper)}
          />
        </View>

        {/* Filter Button */}
        <TouchableOpacity style={themed($iconButton)} onPress={onFilterPress}>
          <Icon icon="filter" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral200,
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.md,
  paddingBottom: spacing.md,
})

const $searchContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
})

const $searchInputContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.md,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  height: 44,
})

const $searchFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  marginLeft: 8,
})

const $searchFieldWrapper: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "transparent",
  borderWidth: 0,
  paddingHorizontal: 0,
  paddingVertical: 0,
})

const $searchInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontSize: 16,
  color: colors.text,
  paddingVertical: 0,
})

const $iconButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.sm,
  alignItems: "center",
  justifyContent: "center",
})
