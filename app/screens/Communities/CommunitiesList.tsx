import { forwardRef } from "react"
import { View, ScrollView, ViewStyle, TextStyle } from "react-native"
import { Text, EmptyState, CommunityCard } from "@/components"
import { CommunityListProps } from "./types"
import { CommunityData } from "@/types/communities"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const CommunitiesList = forwardRef<ScrollView, CommunityListProps>(
  ({ searchQuery, communities, onCommunityPress, onJoinPress, onRequestPress }, ref) => {
    const { themed } = useAppTheme()

    const renderEmptyState = () => {
      if (searchQuery.trim()) {
        return (
          <EmptyState
            headingTx="communitiesScreen:noResultsFoundHeading"
            contentTx="communitiesScreen:noResultsFoundContent"
            contentTxOptions={{ query: searchQuery }}
            button=""
            buttonOnPress={() => {}}
          />
        )
      } else {
        return (
          <EmptyState
            headingTx="communitiesScreen:noCommunitiesAvailableHeading"
            contentTx="communitiesScreen:noCommunitiesAvailableContent"
            button=""
            buttonOnPress={() => {}}
          />
        )
      }
    }

    return (
      <ScrollView
        ref={ref}
        style={themed($container)}
        contentContainerStyle={themed($scrollContent)}
        showsVerticalScrollIndicator={false}
      >
        <View style={themed($section)}>
          <View style={themed($sectionContent)}>
            {communities.length > 0
              ? communities.map((community) => (
                  <CommunityCard
                    key={community.id}
                    community={community}
                    onPress={onCommunityPress}
                    onJoinPress={onJoinPress}
                    onRequestPress={onRequestPress}
                  />
                ))
              : renderEmptyState()}
          </View>
        </View>
      </ScrollView>
    )
  },
)

CommunitiesList.displayName = "CommunitiesList"

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
})

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  marginBottom: spacing.md,
})

const $sectionContent: ThemedStyle<ViewStyle> = () => ({
  // Content styles handled by individual cards
})
