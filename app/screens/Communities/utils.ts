// Global reference to track active screen and scroll function
let activeCommunitiesScreenScrollToTop: (() => void) | null = null

/**
 * Manages the global scroll-to-top functionality for CommunitiesScreen
 */
export const scrollManager = {
  /**
   * Sets the active scroll function for the communities screen
   */
  setActiveScrollFunction: (scrollFn: (() => void) | null) => {
    activeCommunitiesScreenScrollToTop = scrollFn
  },

  /**
   * Triggers scroll to top if an active function is set
   */
  triggerScrollToTop: () => {
    if (activeCommunitiesScreenScrollToTop) {
      activeCommunitiesScreenScrollToTop()
    }
  },
} 