import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Screen, Icon } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { $styles } from "@/theme"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { CommunitiesHeader } from "./CommunitiesHeader"
import { CommunitiesList } from "./CommunitiesList"
import { useAppTheme } from "@/utils/useAppTheme"
import { 
  useCommunitiesData, 
  useCommunitiesActions, 
  useCommunitiesNavigation,
  useCommunitiesScrollToTop 
} from "./hooks"
import { scrollManager } from "./utils"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"
import { useHeader } from "@/utils/useHeader"

export const CommunitiesScreen: FC<AppStackScreenProps<"Communities">> = observer(
  function CommunitiesScreen(props) {
    const {
      themed,
      theme: { colors },
    } = useAppTheme()

    // Data hook - communities data and search state
    const { searchQuery, updateSearchQuery, filteredCommunities } = useCommunitiesData()

    // Action hook - business logic only
    const { 
      handleJoinPress, 
      handleRequestPress, 
      handleFilterPress 
    } = useCommunitiesActions()

    // Navigation hook - navigation actions only
    const { handleBackPress, handleCommunityPress, handleCreateNavigationPress } = useCommunitiesNavigation({ 
      navigation: props.navigation 
    })

    // Scroll hook - scroll functionality only
    const { scrollViewRef } = useCommunitiesScrollToTop()

    useHeader({
      title: translate("communitiesScreen:title"),
      showBackButton: true,
      onBackPress: handleBackPress,
      rightButtons: [
        {
          icon: "add",
          onPress: handleCreateNavigationPress,
          style: themed($createHeaderButton),
          textColor: colors.palette.neutral100,
        },
      ],
    })

    return (
      <Screen
        preset="fixed"
        safeAreaEdges={getSafeAreaConfig("NONE")}
        contentContainerStyle={$styles.flex1}
        statusBarStyle="light"
        backgroundColor={colors.background}
      >
        <View style={$styles.flex1}>
          <CommunitiesHeader
            searchQuery={searchQuery}
            onSearchChange={updateSearchQuery}
            onFilterPress={handleFilterPress}
          />

          <CommunitiesList
            ref={scrollViewRef}
            searchQuery={searchQuery}
            communities={filteredCommunities}
            onCommunityPress={handleCommunityPress}
            onJoinPress={handleJoinPress}
            onRequestPress={handleRequestPress}
          />
        </View>
      </Screen>
    )
  },
)

// Export the scroll function for use in navigator
export const triggerCommunitiesScreenScrollToTop = () => {
  scrollManager.triggerScrollToTop()
}

const $createHeaderButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.success,
  padding: 8, // Add padding to make the icon clickable and visually consistent
  borderRadius: 20, // Make it circular or rounded
})
