import { CommunityData } from "@/types/communities"

interface UseCommunitiesNavigationProps {
  navigation: any
}

export const useCommunitiesNavigation = ({ navigation }: UseCommunitiesNavigationProps) => {
  const handleBackPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack()
    }
  }

  const handleCommunityPress = (community: CommunityData) => {
    navigation.navigate("CommunityDetail", { communityId: community.id })
  }

  const handleCreateNavigationPress = () => {
    navigation.navigate("CreateCommunity")
  }

  return {
    // Navigation actions only
    handleBackPress,
    handleCommunityPress,
    handleCreateNavigationPress,
  }
} 