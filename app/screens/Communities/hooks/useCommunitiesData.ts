import { useState, useMemo } from "react"
import { CommunityData } from "@/types/communities"
import communitiesDataRaw from "@/data/communities.json"

const typedCommunitiesData: { communities: CommunityData[] } = communitiesDataRaw as any

export const useCommunitiesData = () => {
  const [searchQuery, setSearchQuery] = useState("")

  // Filter communities based on search query
  const filteredCommunities = useMemo(() => {
    if (!searchQuery.trim()) {
      return typedCommunitiesData.communities
    }
    
    return typedCommunitiesData.communities.filter((community) =>
      community.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      community.address.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }, [searchQuery])

  const updateSearchQuery = (query: string) => {
    setSearchQuery(query)
  }

  return {
    // Data only
    communities: typedCommunitiesData.communities,
    filteredCommunities,
    searchQuery,
    updateSearchQuery,
  }
} 