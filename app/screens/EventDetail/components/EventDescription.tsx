import { FC } from "react"
import { TextStyle } from "react-native"
import { Text, SectionCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EventDetail } from "../types"
import { translate } from "@/i18n"

interface EventDescriptionProps {
  event: EventDetail
}

export const EventDescription: FC<EventDescriptionProps> = ({ event }) => {
  const { themed } = useAppTheme()

  return (
    <SectionCard title={translate("eventDetailScreen:aboutEventTitle")}>
      <Text text={event.description} style={themed($descriptionText)} />
    </SectionCard>
  )
}

const $descriptionText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  lineHeight: 20,
})
