import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text, Icon, SectionCard, Avatar } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { Club } from "../types"
import { translate } from "@/i18n"

interface EventLocationProps {
  club: Club
  onClubPress: () => void
}

export const EventLocation: FC<EventLocationProps> = ({ club, onClubPress }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <SectionCard title={translate("eventDetailScreen:locationSectionTitle")}>
      <Pressable style={themed($clubItem)} onPress={onClubPress}>
        <Avatar
          name={club.name}
          size="lg"
          style={[themed($clubAvatar), { backgroundColor: "#e04f6b" }]}
          textStyle={themed($clubInitials)}
          textWeight="medium"
          textSize="md"
        />
        <View style={themed($clubDetails)}>
          <Text text={club.name} style={themed($clubName)} weight="bold" size="lg" />
          <Text text={club.address} style={themed($clubAddress)} size="sm" />
        </View>
        <Icon icon="caretRight" size={24} color={colors.palette.primary500} />
      </Pressable>
    </SectionCard>
  )
}

const $clubItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.xs,
})

const $clubAvatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 56,
  height: 56,
  borderRadius: 28,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.md,
})

const $clubInitials: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 18,
})

const $clubDetails: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  marginRight: spacing.sm,
})

const $clubName: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  marginBottom: spacing.xs,
})

const $clubAddress: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  lineHeight: 16,
})
