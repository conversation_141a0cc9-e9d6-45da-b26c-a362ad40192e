import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon, SectionCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EventDetail } from "../types"
import { translate } from "@/i18n"

interface EventInfoProps {
  event: EventDetail
}

export const EventInfo: FC<EventInfoProps> = ({ event }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <SectionCard title={translate("eventDetailScreen:eventDetailsTitle")}>
      <View style={themed($cardGrid)}>
        <View style={themed($infoItem)}>
          <Icon icon="calendar" size={20} color={colors.textDim} />
          <View style={themed($infoTextContainer)}>
            <Text tx="eventDetailScreen:dateTimeLabel" style={themed($infoLabel)} size="xs" />
            <Text text={event.date} style={themed($infoValue)} weight="medium" />
          </View>
        </View>

        <View style={themed($infoItem)}>
          <Icon icon="clock" size={20} color={colors.textDim} />
          <View style={themed($infoTextContainer)}>
            <Text tx="eventDetailScreen:durationLabel" style={themed($infoLabel)} size="xs" />
            <Text text={event.duration} style={themed($infoValue)} weight="medium" />
          </View>
        </View>

        <View style={themed($infoItem)}>
          <Icon icon="pin" size={20} color={colors.textDim} />
          <View style={themed($infoTextContainer)}>
            <Text tx="eventDetailScreen:locationLabel" style={themed($infoLabel)} size="xs" />
            <Text text={event.location} style={themed($infoValue)} weight="medium" />
          </View>
        </View>

        <View style={themed($infoItem)}>
          <Icon icon="aed" size={20} color={colors.textDim} />
          <View style={themed($infoTextContainer)}>
            <Text tx="eventDetailScreen:priceLabel" style={themed($infoLabel)} size="xs" />
            <Text text={event.price} style={themed($infoValue)} weight="medium" />
          </View>
        </View>
      </View>
    </SectionCard>
  )
}

const $cardGrid: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "space-between",
})

const $infoItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  width: "48%",
  marginBottom: spacing.sm,
})

const $infoTextContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginLeft: spacing.sm,
  flex: 1,
})

const $infoLabel: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $infoValue: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
})
