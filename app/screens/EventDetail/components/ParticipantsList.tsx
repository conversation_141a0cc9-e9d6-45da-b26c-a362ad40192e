import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon, SectionCard, UserCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EventDetail, Participant } from "../types"
import { translate } from "@/i18n"

interface ParticipantsListProps {
  event: EventDetail
  participants: Participant[]
  onParticipantPress: (participant: Participant) => void
  onParticipantMessage?: (participant: Participant) => void
}

export const ParticipantsList: FC<ParticipantsListProps> = ({
  event,
  participants,
  onParticipantPress,
  onParticipantMessage,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <SectionCard title={translate("eventDetailScreen:participantsSectionTitle")}>
      <View style={themed($participantsHeader)}>
        <View style={themed($participantCount)}>
          <Icon icon="community" size={16} color={colors.textDim} />
          <Text
            text={translate("eventDetailScreen:participantsCount", { current: event.participants, max: event.maxParticipants })}
            style={themed($countText)}
            size="sm"
          />
        </View>
      </View>

      <View style={themed($participantsList)}>
        {participants.map((participant) => (
          <UserCard
            key={participant.id}
            user={{
              id: participant.id.toString(),
              name: participant.name,
              role: participant.level,
            }}
            onUserPress={(participantId) => {
              const foundParticipant = participants.find(p => p.id.toString() === participantId)
              if (foundParticipant) {
                onParticipantPress(foundParticipant)
              }
            }}
            onMessagePress={onParticipantMessage ? (participantId) => {
              const foundParticipant = participants.find(p => p.id.toString() === participantId)
              if (foundParticipant) {
                onParticipantMessage(foundParticipant)
              }
            } : undefined}
            showMessageButton={!!onParticipantMessage}
            avatarColor="#6366f1"
            containerStyle={themed($participantCard)}
          />
        ))}
      </View>
    </SectionCard>
  )
}

const $participantsHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $participantCount: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
})

const $countText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $participantsList: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xxxs,
})

const $participantCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
})