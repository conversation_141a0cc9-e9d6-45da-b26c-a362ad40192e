import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Button } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EventDetail } from "../types"

interface JoinButtonProps {
  event: EventDetail
  onJoin: () => void
}

export const JoinButton: FC<JoinButtonProps> = ({ event, onJoin }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($joinButtonContainer)}>
      <Button
        text={event.status === "full" ? "Event Full" : "Join Event"}
        preset="filled"
        style={[
          themed($joinButton),
          event.status === "full" && { backgroundColor: colors.palette.neutral400 },
        ]}
        textStyle={themed($joinButtonText)}
        disabled={event.status === "full"}
        onPress={onJoin}
      />
    </View>
  )
}

const $joinButtonContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
  paddingTop: spacing.md,
  paddingBottom: 0, // Remove bottom padding to keep button next to safe area
  alignItems: "center", // Center the button horizontally
})

const $joinButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.secondary500,
  minHeight: 48,
  width: "80%", // Reduce button width to 80% of container
  borderRadius: 24, // Add corner radius (half of minHeight for pill shape)
})

const $joinButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
})
