import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { EventDetail } from "../types"
import { getEventTypeColor } from "../utils"
import { translate } from "@/i18n"

interface EventHeaderProps {
  event: EventDetail
}

export const EventHeader: FC<EventHeaderProps> = ({ event }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const organizerText = translate("eventDetailScreen:organizedBy", { organizer: event.organizer })

  return (
    <View style={themed($eventHeader)}>
      <View style={themed($eventTitleContainer)}>
        <Text text={event.title} style={themed($eventTitle)} weight="bold" size="lg" />
        <Text
          text={organizerText}
          style={themed($eventOrganizer)}
          size="sm"
        />
      </View>
      <View style={themed($eventTypeContainer)}>
        <View
          style={[
            themed($eventTypeBadge),
            { backgroundColor: getEventTypeColor(event.type, colors) },
          ]}
        >
          <Text text={event.type} style={themed($eventTypeText)} weight="medium" size="xs" />
        </View>
      </View>
    </View>
  )
}

const $eventHeader: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  backgroundColor: colors.palette.neutral800,
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.md,
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "flex-start",
})

const $eventTitleContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  marginRight: 12,
})

const $eventTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  marginBottom: 4,
  fontWeight: "bold", // Explicitly set fontWeight for cross-platform consistency
})

const $eventOrganizer: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral300,
})

const $eventTypeContainer: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-end",
})

const $eventTypeBadge: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 12,
})

const $eventTypeText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})
