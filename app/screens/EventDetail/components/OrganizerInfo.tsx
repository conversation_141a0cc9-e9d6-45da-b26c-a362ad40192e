import { FC } from "react"
import { SectionCard, UserCard } from "@/components"
import { translate } from "@/i18n"
import type { EventDetail } from "../types"

interface OrganizerInfoProps {
  event: EventDetail
  onOrganizerPress?: (organizer: string) => void
  onOrganizerMessage?: (organizer: string) => void
}

export const OrganizerInfo: FC<OrganizerInfoProps> = ({
  event,
  onOrganizerPress,
  onOrganizerMessage,
}) => {
  // Hardcoded organizer profile from user_data.json
  const organizerProfile = {
    id: "H901WX3",
    name: "<PERSON>",
    description: "Tournament Organizer",
    initials: "LG"
  }

  const handleOrganizerPress = (organizerId: string) => {
    onOrganizerPress?.(organizerId)
  }

  const handleOrganizerMessage = (organizerId: string) => {
    onOrganizerMessage?.(organizerId)
  }

  return (
    <SectionCard title={translate("eventDetailScreen:organizerSectionTitle")}>
      <UserCard
        user={{
          id: organizerProfile.id,
          name: organizerProfile.name,
          role: organizerProfile.description,
        }}
        onUserPress={handleOrganizerPress}
        onMessagePress={handleOrganizerMessage}
        showMessageButton={!!onOrganizerMessage}
        avatarColor="#6366f1"
      />
    </SectionCard>
  )
}