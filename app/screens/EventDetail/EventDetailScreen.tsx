import { FC, useState } from "react"
import { View, ViewStyle, StatusBar, ScrollView } from "react-native"
import { Screen, MapsSelectionModal, EmptyState } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { AppStackScreenProps } from "@/navigators"
import type { ThemedStyle } from "@/theme"

import { useEventDetail, useEventActions, useEventNavigation } from "./hooks"
import {
  EventHeader,
  EventInfo,
  EventDescription,
  ParticipantsList,
  OrganizerInfo,
  EventRules,
  EventLocation,
  JoinButton,
} from "./components"
import { useHeader } from "@/utils/useHeader"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { observer } from "mobx-react-lite"

type EventDetailScreenProps = AppStackScreenProps<"EventDetail">

export const EventDetailScreen: FC<EventDetailScreenProps> = observer(function EventDetailScreen({ navigation, route }) {
  const { eventId } = route.params
  const [showMapsModal, setShowMapsModal] = useState(false)

  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Data hook - event data only
  const { event, participants, club, loading } = useEventDetail(eventId)

  // Business actions hook - business logic only
  const {
    handleSharePress,
    handleSettingsPress,
    handleJoin,
  } = useEventActions({ event })

  // Navigation hook - navigation actions only
  const {
    handleBack,
    handleParticipantPress,
    handleParticipantMessage,
    handleOrganizerPress,
    handleOrganizerMessage,
  } = useEventNavigation({ navigation })

  // Configure navigation header
  useHeader(
    {
      showBackButton: true,
      onBackPress: handleBack,
      rightButtons: [
        {
          icon: "share",
          onPress: handleSharePress,
        },
        {
          icon: "settings",
          onPress: handleSettingsPress,
        },
      ],
    },
    [],
  )

  const handleNavigateToClub = () => {
    if (!club) return
    setShowMapsModal(true)
  }

  // Loading state
  if (loading) {
    return (
      <Screen preset="fixed" safeAreaEdges={["top", "bottom"]} contentContainerStyle={themed($container)}>
        <EmptyState headingTx="eventDetailScreen:loadingEvent" />
      </Screen>
    )
  }

  // Error / not found state
  if (!event) {
    return (
      <Screen preset="fixed" safeAreaEdges={["top", "bottom"]} contentContainerStyle={themed($container)}>
        <EmptyState
          headingTx="eventDetailScreen:errorHeading"
          contentTx="eventDetailScreen:eventNotFound"
          buttonTx="common:goBack"
          buttonOnPress={handleBack}
        />
      </Screen>
    )
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
      contentContainerStyle={themed($container)}
      statusBarStyle="light"
      backgroundColor={colors.background}
    >
      <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />

      {/* Event Header (info block) */}
      <EventHeader event={event} />

      {/* Scrollable Content */}
      <ScrollView style={themed($scrollContent)} showsVerticalScrollIndicator={false}>
        <View style={themed($contentContainer)}>
          {/* Event Info */}
          <EventInfo event={event} />

          {/* Event Description */}
          <EventDescription event={event} />

          {/* Organizer */}
          <OrganizerInfo
            event={event}
            onOrganizerPress={handleOrganizerPress}
            onOrganizerMessage={handleOrganizerMessage}
          />

          {/* Participants */}
          <ParticipantsList
            event={event}
            participants={participants}
            onParticipantPress={handleParticipantPress}
            onParticipantMessage={handleParticipantMessage}
          />

          {/* Event Rules */}
          <EventRules event={event} />

          {/* Location */}
          {club && <EventLocation club={club} onClubPress={handleNavigateToClub} />}
        </View>
      </ScrollView>

      {/* Maps Selection Modal */}
      <MapsSelectionModal
        visible={showMapsModal}
        club={club}
        onClose={() => setShowMapsModal(false)}
      />

      {/* Join Button */}
      <JoinButton event={event} onJoin={handleJoin} />
    </Screen>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $scrollContent: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.sm,
})
