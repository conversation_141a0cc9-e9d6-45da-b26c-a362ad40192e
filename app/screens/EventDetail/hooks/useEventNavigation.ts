import type { Participant } from "../types"

interface UseEventNavigationProps {
  navigation: any
}

export const useEventNavigation = ({ navigation }: UseEventNavigationProps) => {
  const handleBack = () => {
    navigation.goBack()
  }

  const handleParticipantPress = (participant: Participant) => {
    navigation.navigate("MemberProfile", { userId: participant.userId })
  }

  const handleParticipantMessage = (participant: Participant) => {
    navigation.navigate("PrivateChat", {
      participantId: participant.userId,
      participantName: participant.name,
    })
  }

  const handleOrganizerPress = (organizer: string) => {
    // Navigate to organizer profile
    navigation.navigate("MemberProfile", { userId: organizer })
  }

  const handleOrganizerMessage = (organizer: string) => {
    // Navigate to chat with organizer
    navigation.navigate("PrivateChat", {
      participantId: organizer,
      participantName: "<PERSON> <PERSON>", // Use the actual name instead of user ID
    })
  }

  return {
    handleBack,
    handleParticipantPress,
    handleParticipantMessage,
    handleOrganizerPress,
    handleOrganizerMessage,
  }
} 