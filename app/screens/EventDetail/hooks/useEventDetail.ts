import { useState, useEffect } from "react"
import type { EventDetail, Participant, Club } from "../types"
import eventsData from "@/data/events.json"
import clubsData from "@/data/clubs.json"

export const useEventDetail = (eventId: number) => {
  const [event, setEvent] = useState<EventDetail | null>(null)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [club, setClub] = useState<Club | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadEventData = async () => {
      setLoading(true)

      // Find event in data
      const foundEvent = (eventsData as any).find((e: any) => e.id === eventId)

      if (foundEvent) {
        // Extend with additional details for the detail view
        const detailedEvent: EventDetail = {
          ...foundEvent,
          description:
            "Join us for an exciting padel session. Perfect for players of all levels. Equipment provided if needed.",
          address: foundEvent.location + ", Dubai Sports Complex",
          courts: 2,
          duration: "2 hours",
          organizer: "Elite Padel Club",
          rules: [
            "Arrive 15 minutes before start time",
            "Proper sports attire required",
            "No refunds 24 hours before event",
            "Respect all players and equipment",
          ],
        }
        setEvent(detailedEvent)

        // Find club by clubId
        const foundClub = (clubsData as any).find((c: any) => c.id === foundEvent.clubId)
        if (foundClub) {
          setClub(foundClub)
        }

        // Mock participants data with correct user IDs from user_data.json
        const participantData = [
          { id: 1, name: "John Doe", level: "Intermediate", userId: "B789KL4" },
          { id: 2, name: "Sarah Smith", level: "Advanced", userId: "C456MN7" },
          { id: 3, name: "Mike Johnson", level: "Beginner", userId: "D123OP8" },
          { id: 4, name: "Emma Wilson", level: "Intermediate", userId: "E890QR5" },
          { id: 5, name: "Carlos Silva", level: "Advanced", userId: "A243HG3" },
          { id: 6, name: "Anna Martinez", level: "Intermediate", userId: "F567ST9" },
          { id: 7, name: "David Brown", level: "Intermediate", userId: "G234UV6" },
          { id: 8, name: "Lisa Garcia", level: "Advanced", userId: "H901WX3" },
        ]

        // Use only the number of participants specified in the event
        setParticipants(participantData.slice(0, foundEvent.participants))
      }

      setLoading(false)
    }

    loadEventData()
  }, [eventId])

  return {
    event,
    participants,
    club,
    loading,
  }
}
