import { Alert, Share } from "react-native"
import { safeAsync } from "@/utils/safeAsync"
import type { EventDetail, Participant } from "../types"

interface UseEventActionsProps {
  event: EventDetail | null
}

export const useEventActions = ({ event }: UseEventActionsProps) => {

  const handleSharePress = async () => {
    if (!event) return

    await safeAsync(async () => {
      const participantsText = event.participants > 1 ? "participants" : "participant"
      const locationText = event.location ? `\n📍 ${event.location}` : ""
      const priceText = event.price ? `\n💰 ${event.price}` : ""

      return await Share.share({
        message: `Check out this ${event.type} event: ${event.title}!${locationText}\n\n📅 ${event.date}${priceText}\n👥 ${event.participants}/${event.maxParticipants} ${participantsText}\n\nOrganized by ${event.organizer}\n\n${event.description || "Join us for an exciting padel session!"}`,
        title: `${event.title} - ${event.type}`,
      })
    })
  }

  const handleSettingsPress = () => {
    // TODO: Implement event settings logic
    console.log("Event settings pressed")
  }

  const handleJoin = () => {
    if (!event) return

    if (event.status === "full") {
      Alert.alert("Event Full", "This event is currently full.")
      return
    }

    Alert.alert("Join Event", `Are you sure you want to join "${event.title}"?`, [
      { text: "Cancel", style: "cancel" },
      {
        text: "Join",
        onPress: () => {
          Alert.alert("Joined successfully!", `You have joined "${event.title}"`)
        },
      },
    ])
  }

  return {
    // Business actions only
    handleSharePress,
    handleSettingsPress,
    handleJoin,
  }
}
