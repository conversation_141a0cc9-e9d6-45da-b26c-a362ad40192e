export interface EventDetail {
  id: number
  type: string
  title: string
  description: string
  date: string
  location: string
  clubId: number
  address: string
  price: string
  participants: number
  maxParticipants: number
  courts: number
  duration: string
  organizer: string
  status: string
  rules: string[]
}

export interface Club {
  id: number
  name: string
  initials: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  phone: string
  amenities: string[]
}

export interface Participant {
  id: number
  name: string
  level: string
  userId: string
}
