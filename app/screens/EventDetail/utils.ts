import { ThemedStyle } from "@/theme"
export { generateInitials } from "@/utils/avatar"

/**
 * Gets the color for event type badges
 * @param type - Event type string
 * @param colors - Theme colors object
 * @returns Color string
 */
export const getEventTypeColor = (type: string, colors: any) => {
  switch (type) {
    case "Americano":
      return colors.palette.secondary500
    case "Tournament":
      return colors.palette.primary500
    case "League":
      return colors.palette.angry500
    case "Social":
      return colors.palette.accent400
    case "Mexicano":
      return colors.palette.accent500
    default:
      return colors.palette.neutral500
  }
}
