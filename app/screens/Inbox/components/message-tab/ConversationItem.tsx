import { FC, useMemo, useRef, useCallback } from "react"
import { observer } from "mobx-react-lite"
import { useNavigation } from "@react-navigation/native"
import type { AppStackScreenProps } from "@/navigators"

// React Native imports
import { View, Pressable, ViewStyle, TextStyle, Animated, useWindowDimensions } from "react-native"

// Third-party imports
import Swipeable from "react-native-gesture-handler/ReanimatedSwipeable"
import * as Haptics from "expo-haptics"

// Local component imports
import { Avatar } from "@/components/Avatar"
import { Text } from "@/components/Text"
import { Icon } from "@/components/Icon"

// Local utility imports
import { useAppTheme } from "@/utils/useAppTheme"
import { formatDate } from "@/utils/formatDate"

// Type imports
import { ConversationSummary } from "@/types/messages"
import { ThemedStyle } from "@/theme"

interface ConversationItemProps {
  conversation: ConversationSummary
  onToggleRead: (id: string) => void
  onDelete: (id: string) => void
}

export const ConversationItem: FC<ConversationItemProps> = observer(
  function ConversationItem({ conversation, onToggleRead, onDelete }) {
    const { themed, theme } = useAppTheme()
    const { colors } = theme
    const { width } = useWindowDimensions()
    const navigation = useNavigation<AppStackScreenProps<"PrivateChat">["navigation"]>()
    const swipeableRef = useRef<any>(null)

    const dateText = useMemo(
      () => formatDate(conversation.lastMessageTimestamp, "MMM d"),
      [conversation.lastMessageTimestamp],
    )

    const handleSwipeableOpen = useCallback(
      (direction: "left" | "right") => {
        // Trigger haptic feedback for a more satisfying interaction
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy).catch(() => {})

        // Perform action based on the swipe direction
        if (direction === "left") {
          onToggleRead(conversation.id)
        } else {
          onDelete(conversation.id)
        }

        // Programmatically close the swipeable to finish the animation
        swipeableRef.current?.close()
      },
      [conversation.id, onDelete, onToggleRead],
    )

    const renderLeftActions = (
      _progress: unknown,
      _translation: unknown,
    ) => {
      return (
        <View style={themed($leftAction)}>
          <View style={themed($actionIcon)}>
            <Icon icon="inbox" size={24} color={colors.background} />
          </View>
        </View>
      )
    }

    const renderRightActions = (
      _progress: unknown,
      _translation: unknown,
    ) => {
      return (
        <View style={themed($rightAction)}>
          <View style={themed($actionIcon)}>
            <Icon icon="x" size={24} color={colors.background} />
          </View>
        </View>
      )
    }

    return (
      <Swipeable
        ref={swipeableRef}
        renderLeftActions={renderLeftActions}
        renderRightActions={renderRightActions}
        onSwipeableOpen={handleSwipeableOpen}
        friction={2}
        leftThreshold={width * 0.25}
        rightThreshold={width * 0.25}
      >
        <Pressable
          style={themed($container)}
          onPress={() => {
            navigation.navigate("PrivateChat", {
              participantId: conversation.otherUserId,
              participantName: conversation.otherUserName,
            })
            if (conversation.unread) onToggleRead(conversation.id)
          }}
        >
          {conversation.unread && <View style={themed($unreadDot)} accessibilityLabel="Unread" />}
          <Avatar name={conversation.otherUserName} size="md" />
          <View style={themed($textContainer)}>
            <Text
              text={conversation.otherUserName}
              size="sm"
              weight="bold"
              numberOfLines={1}
            />
            <Text
              text={conversation.lastMessageText}
              size="xs"
              weight="normal"
              numberOfLines={2}
              style={$previewText}
            />
          </View>
          <Text text={dateText} size="xxs" style={themed($dateText)} />
        </Pressable>
      </Swipeable>
    )
  },
)

const $container: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  position: "relative",
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.lg,
  backgroundColor: colors.background,
})

const $textContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  marginLeft: spacing.sm,
  marginRight: spacing.sm,  
})

const $previewText: TextStyle = {
  marginTop: 2,
}

const $dateText: ThemedStyle<TextStyle> = ({ colors }) => ({
  marginLeft: "auto",
  color: colors.textDim,
})

const $leftAction: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-start",
  backgroundColor: colors.success,
  gap: spacing.xs,
})

const $rightAction: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.error,
  justifyContent: "flex-end",
  alignItems: "center",
  flexDirection: "row",
})

const $actionIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 30,
  marginHorizontal: spacing.md,
})

const $unreadDot: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  position: "absolute",
  left: spacing.sm,
  top: "50%",
  marginTop: -4, // half of dot size to vertically center
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: colors.error,
})
