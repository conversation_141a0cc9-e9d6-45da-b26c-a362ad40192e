// 1. React imports
import { FC } from "react"
import { observer } from "mobx-react-lite"

// 2. React Native imports
import { View, ViewStyle } from "react-native"

// 3. Third-party imports
import { FlashList } from "@shopify/flash-list"

// 4. Local component imports
import { ConversationItem } from "./ConversationItem"

// 5. Local utility imports
import { useAppTheme } from "@/utils/useAppTheme"
import { useMessagesData } from "@/screens/Inbox/hooks/useMessagesData"

// 6. Type imports
import { ThemedStyle } from "@/theme"

export const MessageTab: FC = observer(function MessageTab() {
  const { themed } = useAppTheme()
  const { conversations, toggleRead, deleteConversation } = useMessagesData()

  return (
    <View style={themed($list)}>
      <FlashList
        data={conversations}
        keyExtractor={(item) => item.id}
        estimatedItemSize={80}
        renderItem={({ item }) => (
          <ConversationItem
            conversation={item}
            onToggleRead={toggleRead}
            onDelete={deleteConversation}
          />
        )}
        contentContainerStyle={themed($contentContainer)}
      />
    </View>
  )
})

const $list: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $contentContainer: ThemedStyle<ViewStyle> = () => ({}) 