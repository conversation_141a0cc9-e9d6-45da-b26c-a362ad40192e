import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable, Keyboard } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface InboxTabsProps {
  activeTab: "messages" | "invites"
  onTabChange: (tab: "messages" | "invites") => void
}

export const InboxTabs: FC<InboxTabsProps> = ({ activeTab, onTabChange }) => {
  const { themed } = useAppTheme()

  const tabs = [
    { key: "messages" as const, title: translate("inboxTabs:messagesTabTitle") },
    { key: "invites" as const, title: translate("inboxTabs:invitesTabTitle") },
  ]

  return (
    <View style={themed($tabsContainer)}>
      {tabs.map((tab) => (
        <Pressable
          key={tab.key}
          style={themed([$tab, activeTab === tab.key && $activeTab])}
          onPress={() => {
            Keyboard.dismiss()
            onTabChange(tab.key)
          }}
        >
          <Text
            text={tab.title}
            style={themed([$tabText, activeTab === tab.key && $activeTabText])}
            size="sm"
          />
          {activeTab === tab.key && <View style={themed($activeIndicator)} />}
        </Pressable>
      ))}
    </View>
  )
}

const $tabsContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.border,
})

const $tab: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  alignItems: "center",
  paddingVertical: spacing.xs,
  position: "relative",
})

const $activeTab: ThemedStyle<ViewStyle> = () => ({})

const $tabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $activeTabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontWeight: "bold",
})

const $activeIndicator: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: colors.text,
}) 