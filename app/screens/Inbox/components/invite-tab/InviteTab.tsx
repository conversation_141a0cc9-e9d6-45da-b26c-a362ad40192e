import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const InviteTab: FC = () => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <Text text="No invites yet." size="md" />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  padding: spacing.lg,
  justifyContent: "center",
  alignItems: "center",
}) 