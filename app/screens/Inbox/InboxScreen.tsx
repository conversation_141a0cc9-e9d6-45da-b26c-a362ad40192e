import { observer } from "mobx-react-lite"
import { FC, useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useHeader } from "@/utils/useHeader"
import { translate } from "@/i18n"
import { AppStackScreenProps } from "@/navigators"
import { $styles } from "@/theme"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { useAppTheme } from "@/utils/useAppTheme"

import { InboxTabs, MessageTab, InviteTab } from "./components"
import type { ThemedStyle } from "@/theme"

export const InboxScreen: FC<AppStackScreenProps<"Inbox">> = observer(function InboxScreen({ navigation }) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const [activeTab, setActiveTab] = useState<"messages" | "invites">("messages")

  useHeader({
    title: translate("inboxScreen:title"),
    showBackButton: true,
    onBackPress: navigation.goBack,
  })

  const renderActiveTabContent = () => {
    switch (activeTab) {
      case "messages":
        return <MessageTab />
      case "invites":
        return <InviteTab />
      default:
        return <MessageTab />
    }
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
      contentContainerStyle={$styles.flex1}
      backgroundColor={colors.background}
      statusBarStyle="light"
    >
      <InboxTabs activeTab={activeTab} onTabChange={setActiveTab} />

      <View style={themed($contentContainer)}>{renderActiveTabContent()}</View>
    </Screen>
  )
})

const $contentContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})
