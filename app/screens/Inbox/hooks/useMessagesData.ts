import { useCallback, useEffect, useState } from "react"
import userData from "@/data/user_data.json"
import { MessageService } from "@/services/MessageService"
import { ConversationSummary } from "@/types/messages"

export const useMessagesData = () => {
  const currentUserId: string = (userData as any).currentUserId
  const [conversations, setConversations] = useState<ConversationSummary[]>([])

  useEffect(() => {
    const summaries = MessageService.getConversationSummaries(currentUserId)
    setConversations(summaries)
  }, [currentUserId])

  const toggleRead = useCallback((id: string) => {
    setConversations((prev) =>
      prev.map((conv) =>
        conv.id === id ? { ...conv, unread: !conv.unread } : conv,
      ),
    )
  }, [])

  const deleteConversation = useCallback((id: string) => {
    setConversations((prev) => prev.filter((conv) => conv.id !== id))
  }, [])

  return { conversations, toggleRead, deleteConversation }
} 