import eventResultsData from "@/data/event_result.json"
import { Game } from "./types"

export const parseRoundLabel = (label: string): number => Number(label.replace(/[^0-9]/g, ""))

interface RoundData {
  round: number
  games: Game[]
}

interface ResultsSchema {
  rounds: RoundData[]
}

const results = eventResultsData as unknown as ResultsSchema

export const getGamesForRound = (roundNumber: number): Game[] => {
  return results.rounds.find((r) => r.round === roundNumber)?.games ?? []
} 