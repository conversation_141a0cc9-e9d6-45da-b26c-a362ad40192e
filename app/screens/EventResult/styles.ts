import type { ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

export const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

export const $scroll: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

export const $scrollContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
  paddingTop: spacing.xl * 1.5,
})

export const $scoreCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl * 2,
}) 