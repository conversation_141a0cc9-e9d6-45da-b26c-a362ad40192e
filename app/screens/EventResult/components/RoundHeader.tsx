import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { SimpleTabs, TabItem } from "@/components"
import type { ThemedStyle } from "@/theme"

interface RoundHeaderProps {
  rounds: string[]
  activeRound: string
  onRoundChange: (round: string) => void
}

export const RoundHeader: FC<RoundHeaderProps> = ({ rounds, activeRound, onRoundChange }) => {
  const { themed } = useAppTheme()

  const tabs: TabItem[] = rounds.map((r) => ({ key: r, title: r }))

  return (
    <View style={themed($container)}>
      <SimpleTabs tabs={tabs} activeKey={activeRound} onTabChange={onRoundChange} scrollable tabWidth={90} />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({}) 