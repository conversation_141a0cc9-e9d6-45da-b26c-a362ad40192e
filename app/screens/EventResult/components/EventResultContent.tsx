import { FC } from "react"
import { ScrollView } from "react-native"
import { ScoreCard } from "@/components/ScoreCard"
import { useAppTheme } from "@/utils/useAppTheme"

import { EventHeader } from "./EventHeader"
import { RoundHeader } from "./RoundHeader"
import { Game } from "../types"
import { $scroll, $scrollContent, $scoreCard } from "../styles"

interface EventResultContentProps {
  event: any
  rounds: string[]
  activeRound: string
  onRoundChange: (round: string) => void
  gamesForRound: Game[]
}

export const EventResultContent: FC<EventResultContentProps> = ({
  event,
  rounds,
  activeRound,
  onRoundChange,
  gamesForRound,
}) => {
  const { themed } = useAppTheme()

  return (
    <>
      {event && <EventHeader event={event} />}

      <RoundHeader rounds={rounds} activeRound={activeRound} onRoundChange={onRoundChange} />

      <ScrollView
        style={themed($scroll)}
        contentContainerStyle={themed($scrollContent)}
        showsVerticalScrollIndicator={false}
      >
        {gamesForRound.map((game) => {
          const [team1, team2] = game.teams

          return (
            <ScoreCard
              key={game.id}
              team1Players={[team1.players[0], team1.players[1]] as [string, string]}
              team2Players={[team2.players[0], team2.players[1]] as [string, string]}
              team1Score={team1.score}
              team2Score={team2.score}
              courtName={`Court ${game.id}`}
              style={themed($scoreCard)}
            />
          )
        })}
      </ScrollView>
    </>
  )
} 