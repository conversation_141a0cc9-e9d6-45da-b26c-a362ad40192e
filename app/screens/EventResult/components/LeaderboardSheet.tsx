import { FC, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { View, TextStyle } from "react-native"
import { BottomSheetScrollView } from "@gorhom/bottom-sheet"

import { BaseBottomSheet, Text } from "@/components"
import { LeaderboardService } from "@/services"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import type { ThemedStyle } from "@/theme"
import type { ViewStyle } from "react-native"
import type { LeaderboardRow } from "@/services/LeaderboardService"

export interface LeaderboardSheetProps {
  visible: boolean
  onClose: () => void
}

export const LeaderboardSheet: FC<LeaderboardSheetProps> = observer(function LeaderboardSheet({ visible, onClose }) {
  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])
  const data = useMemo(() => LeaderboardService.calculateLeaderboard(), [])

  const renderHeader = () => (
    <View style={themed($headerRow)}>
      <Text text="#" style={themed([$headerCell, $cellBorder, $indexCell])} weight="semiBold" size="sm" />
      <Text text="Player" style={[themed([$headerCell, $cellBorder]), { flex: 2, textAlign: "left" }]} weight="semiBold" size="sm" />
      <Text text="W-T-L" style={themed([$headerCell, $cellBorder])} weight="semiBold" size="sm" />
      <Text text="Diff" style={themed([$headerCell, $cellBorder])} weight="semiBold" size="sm" />
      <Text text="Points" style={themed([$headerCell, $pointsColumn])} weight="semiBold" size="sm" />
    </View>
  )

  const renderItem = (item: LeaderboardRow, index: number) => (
    <View key={item.player} style={themed($row)}>
      <Text text={`${index + 1}`} style={themed([$cell, $cellBorder, $indexCell])} size="sm" />
      <Text text={item.player} style={[themed([$cell, $cellBorder]), { flex: 2, textAlign: "left" }]} size="sm" />
      <Text text={`${item.wins}-${item.ties}-${item.losses}`} style={themed([$cell, $cellBorder])} size="sm" />
      <Text text={`${item.diff}`} style={themed([$cell, $cellBorder])} size="sm" />
      <Text text={`${item.points}`} style={themed([$cell, $pointsText])} size="sm" />
    </View>
  )

  const renderFooter = () => (
    <View style={{ height: 32 + $bottomInset.paddingBottom }} />
  )

  return (
    <BaseBottomSheet visible={visible} onClose={onClose} snapPoints={["75%"]} enableDynamicSizing={false}>
      <View style={themed($container)}>
        {/* Fixed Header */}
        {renderHeader()}
        
        {/* Scrollable Content */}
        <BottomSheetScrollView 
          style={themed($scrollView)} 
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps="handled"
          bounces={true}
        >
          {data.map((item, index) => renderItem(item, index))}
          {renderFooter()}
        </BottomSheetScrollView>
      </View>
    </BaseBottomSheet>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
  flex: 1, // Ensure the container fills available space
})

const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1, // Ensure the scroll view fills available space
})

const $headerRow: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.palette.neutral800,
})

const $headerCell: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  flex: 1,
  textAlign: "center",
})

const $cellBorder: ThemedStyle<ViewStyle | TextStyle> = ({ colors }) => ({
  borderRightWidth: 1,
  borderRightColor: colors.palette.neutral200,
})

const $indexCell: ThemedStyle<TextStyle> = () => ({
  width: 28,
  textAlign: "center",
})

const $row: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
})

const $cell: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

const $pointsColumn: ThemedStyle<ViewStyle | TextStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral800,
  color: colors.palette.neutral100,
  paddingHorizontal: spacing.xs,
})

const $pointsText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral900,
  textAlign: "center",
})
