import { FC, useState } from "react"
import { observer } from "mobx-react-lite"

// 1. App imports
import { Screen } from "@/components"

// 2. Utils & hooks
import { useAppTheme } from "@/utils/useAppTheme"
import { useHeader } from "@/utils/useHeader"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"

// 3. Event hooks
import { useEventDetail } from "@/screens/EventDetail/hooks"
import { useRounds } from "@/screens/EventResult/hooks/useRounds"

// 4. Local utilities & components
import { parseRoundLabel, getGamesForRound } from "@/screens/EventResult/utils"
import { EventResultContent, LeaderboardSheet } from "@/screens/EventResult/components"
import { $container } from "@/screens/EventResult/styles"

// 5. Type imports
import type { AppStackScreenProps } from "@/navigators"

// ----------------------------------------------------------------------------
// Screen Props
// ----------------------------------------------------------------------------

type EventResultScreenProps = AppStackScreenProps<"EventResult">

// ----------------------------------------------------------------------------
// Component
// ----------------------------------------------------------------------------

export const EventResult: FC<EventResultScreenProps> = observer(function EventResult({ navigation, route }) {
  const { themed, theme } = useAppTheme()

  // --------------------------------------------------------------------------
  // Params & Data
  // --------------------------------------------------------------------------
  const { eventId } = route.params

  const { event } = useEventDetail(eventId)
  const { rounds, activeRound, handleRoundChange } = useRounds()

  // Games for the current active round
  const activeRoundNumber = parseRoundLabel(activeRound)
  const gamesForRound = getGamesForRound(activeRoundNumber)

  // --------------------------------------------------------------------------
  // Sheet state & Header configuration
  // --------------------------------------------------------------------------
  const [showLeaderboardSheet, setShowLeaderboardSheet] = useState(false)

  const handleBack = () => navigation.goBack()

  const handleSharePress = () => {
    // TODO: implement share functionality
  }

  const handleLeaderboardPress = () => {
    setShowLeaderboardSheet(true)
  }

  useHeader(
    {
      showBackButton: true,
      onBackPress: handleBack,
      rightButtons: [
        { icon: "share", onPress: handleSharePress },
        { icon: "leaderboard", onPress: handleLeaderboardPress },
      ],
    },
    [],
  )

  // --------------------------------------------------------------------------
  // Render
  // --------------------------------------------------------------------------
  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
      contentContainerStyle={themed($container)}
      statusBarStyle="light"
      backgroundColor={theme.colors.background}
    >
      <EventResultContent
        event={event as any}
        rounds={rounds}
        activeRound={activeRound}
        onRoundChange={handleRoundChange}
        gamesForRound={gamesForRound}
      />

      {/* Leaderboard bottom sheet */}
      <LeaderboardSheet
        visible={showLeaderboardSheet}
        onClose={() => setShowLeaderboardSheet(false)}
      />
    </Screen>
  )
}) 