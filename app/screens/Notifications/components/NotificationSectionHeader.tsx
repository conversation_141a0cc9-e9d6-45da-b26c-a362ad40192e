import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components/Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface NotificationSectionHeaderProps {
  title: string
}

export const NotificationSectionHeader: FC<NotificationSectionHeaderProps> = ({ title }) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($sectionHeader)}>
      <Text style={themed($sectionTitle)} weight="semiBold">
        {title}
      </Text>
    </View>
  )
}

const $sectionHeader: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  backgroundColor: colors.palette.neutral200,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontSize: 14,
  color: colors.text,
})
