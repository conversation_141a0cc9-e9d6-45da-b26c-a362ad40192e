import { FC } from "react"
import { View, ViewStyle, TextStyle, ImageStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const NotificationEmptyState: FC = () => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($emptyStateContainer)}>
      <View style={themed($emptyStateContent)}>
        <Icon
          icon="bell"
          size={64}
          color={colors.palette.neutral400}
          containerStyle={themed($emptyStateIcon)}
        />
        <Text style={themed($emptyStateHeading)}>No notifications yet</Text>
        <Text style={themed($emptyStateText)}>
          When you get notifications, they'll show up here.
        </Text>
      </View>
    </View>
  )
}

const $emptyStateContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 20,
})

const $emptyStateContent: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
})

const $emptyStateHeading: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 18,
  fontFamily: typography.primary?.semiBold,
  color: colors.text,
  textAlign: "center",
  marginBottom: 8,
})

const $emptyStateText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14,
  fontFamily: typography.primary?.normal,
  color: colors.textDim,
  textAlign: "center",
})

const $emptyStateIcon: ThemedStyle<ImageStyle> = () => ({
  marginBottom: 16,
})
