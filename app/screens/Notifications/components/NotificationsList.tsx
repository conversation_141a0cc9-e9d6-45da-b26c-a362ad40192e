import { FC } from "react"
import { FlatList, ViewStyle } from "react-native"
import { NotificationItem } from "@/components"
import { NotificationSectionHeader } from "./NotificationSectionHeader"
import { useAppTheme } from "@/utils/useAppTheme"
import { NotificationListItem, Notification } from "../types"
import { isNotificationSection } from "../utils"
import type { ThemedStyle } from "@/theme"

interface NotificationsListProps {
  data: NotificationListItem[]
  onFollowPress: (notificationId: string) => void
  onActionPress: (notificationId: string, action: string) => void
  onUserPress: (userId: string, userName: string) => void
  onEventPress: (eventName: string) => void
}

export const NotificationsList: FC<NotificationsListProps> = ({
  data,
  onFollowPress,
  onActionPress,
  onUserPress,
  onEventPress,
}) => {
  const { themed } = useAppTheme()

  const renderItem = ({ item }: { item: NotificationListItem }) => {
    // Render section header
    if (isNotificationSection(item)) {
      return <NotificationSectionHeader title={item.title} />
    }

    // Render notification item
    return (
      <NotificationItem
        notification={item as unknown as Notification}
        onFollowPress={onFollowPress}
        onActionPress={onActionPress}
        onUserPress={onUserPress}
        onEventPress={onEventPress}
      />
    )
  }

  const keyExtractor = (item: NotificationListItem) => {
    if (isNotificationSection(item)) {
      return item.id
    }
    return (item as unknown as Notification).id
  }

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      style={themed($list)}
    />
  )
}

const $list: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "#FFFFFF",
})
