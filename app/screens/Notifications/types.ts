export { Notification, NotificationGroup } from "@/types/notifications"

export interface NotificationSection {
  id: string
  isHeader: true
  title: string
}

export type NotificationListItem = Notification | NotificationSection

export interface UseNotificationsDataProps {
  // Props for data hook if needed in future
}

export interface UseNotificationsActionsProps {
  navigation: any
  onClearAll: () => void
  onFollowToggle: (notificationId: string) => void
  onUpdateNotifications?: React.Dispatch<React.SetStateAction<Notification[]>>
}
