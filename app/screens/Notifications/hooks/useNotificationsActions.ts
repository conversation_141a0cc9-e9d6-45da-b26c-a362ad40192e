import { useCallback } from "react"
import { <PERSON>ert } from "react-native"
import { UseNotificationsActionsProps } from "../types"
import { safeAsync } from "@/utils/safeAsync"
import { handleNotificationAction } from "../utils"

interface UseNotificationsActionsReturn {
  // Notification actions
  handleClearAllNotifications: () => void
  handleFollowPress: (notificationId: string) => void
  handleActionPress: (notificationId: string, action: string) => void
}

export const useNotificationsActions = ({
  onClearAll,
  onFollowToggle,
  onUpdateNotifications,
}: Omit<UseNotificationsActionsProps, 'navigation'>): UseNotificationsActionsReturn => {
  // Clear all notifications with confirmation
  const handleClearAllNotifications = useCallback(async () => {
    await safeAsync(async () => {
      Alert.alert("Clear All Notifications", "Are you sure you want to clear all notifications?", [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "YES",
          style: "destructive",
          onPress: onClearAll,
        },
      ])
    })
  }, [onClearAll])

  // Handle follow button press
  const handleFollowPress = useCallback(
    (notificationId: string) => {
      onFollowToggle(notificationId)
    },
    [onFollowToggle],
  )

  // Handle notification action press
  const handleActionPress = useCallback((notificationId: string, action: string) => {
    handleNotificationAction(notificationId, action)
  }, [])

  return {
    // Business actions only
    handleClearAllNotifications,
    handleFollowPress,
    handleActionPress,
  }
}
