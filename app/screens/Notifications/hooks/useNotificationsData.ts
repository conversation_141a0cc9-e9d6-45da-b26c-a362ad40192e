import { useState, useEffect } from "react"
import { Notification, UseNotificationsDataProps, NotificationListItem } from "../types"
import {
  groupNotificationsByTime,
  flattenNotificationsWithHeaders,
  toggleNotificationFollow,
} from "../utils"
import { safeAsync } from "@/utils/safeAsync"
import notificationsDataRaw from "@/data/notifications.json"

const typedNotificationsData: { notifications: Notification[] } = notificationsDataRaw as any

interface UseNotificationsDataReturn {
  // Data
  notifications: Notification[]
  flattenedNotifications: NotificationListItem[]

  // Loading states
  loading: boolean

  // Error states
  error: string | null

  // State setters
  setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>

  // Actions
  clearAllNotifications: () => void
  toggleFollowNotification: (notificationId: string) => void
}

export const useNotificationsData = (
  props: UseNotificationsDataProps = {},
): UseNotificationsDataReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Initialize notifications data
  useEffect(() => {
    const initializeNotifications = async () => {
      setLoading(true)
      setError(null)

      const result = await safeAsync(
        async () => {
          // Load notifications from data file
          return typedNotificationsData.notifications
        }
      )

      if (result.success) {
        setNotifications(result.data!)
      } else {
        setError(result.error?.message || "Failed to load notifications")
      }

      setLoading(false)
    }

    initializeNotifications()
  }, [])

  // Group and flatten notifications for list rendering
  const groupedNotifications = groupNotificationsByTime(notifications)
  const flattenedNotifications = flattenNotificationsWithHeaders(groupedNotifications)

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([])
  }

  // Toggle follow state for a notification
  const toggleFollowNotification = (notificationId: string) => {
    setNotifications((prev) => toggleNotificationFollow(prev, notificationId))
  }

  return {
    // Data
    notifications,
    flattenedNotifications,

    // Loading states
    loading,

    // Error states
    error,

    // State setters
    setNotifications,

    // Actions
    clearAllNotifications,
    toggleFollowNotification,
  }
}
