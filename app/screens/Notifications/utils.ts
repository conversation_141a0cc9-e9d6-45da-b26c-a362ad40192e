import { Notification, NotificationGroup, NotificationListItem, NotificationSection } from "./types"

/**
 * Groups notifications by time periods based on their timestamp
 */
export const groupNotificationsByTime = (notifications: Notification[]): NotificationGroup => {
  const groups: NotificationGroup = {
    "Last 7 days": [],
    "Last 30 days": [],
    "Older": [],
  }

  notifications.forEach((notification) => {
    if (notification.timestamp.includes("d") || notification.timestamp === "1w") {
      groups["Last 7 days"].push(notification)
    } else if (
      notification.timestamp.includes("w") &&
      notification.timestamp !== "1w" &&
      parseInt(notification.timestamp) <= 4
    ) {
      groups["Last 30 days"].push(notification)
    } else {
      groups["Older"].push(notification)
    }
  })

  return groups
}

/**
 * Flattens grouped notifications with section headers for FlatList rendering
 */
export const flattenNotificationsWithHeaders = (
  groupedNotifications: NotificationGroup,
): NotificationListItem[] => {
  const flattenedNotifications: NotificationListItem[] = []

  Object.entries(groupedNotifications).forEach(
    ([period, notifications]: [string, Notification[]]) => {
      if (notifications.length > 0) {
        // Add section header
        const sectionHeader: NotificationSection = {
          id: period,
          isHeader: true,
          title: period,
        }
        flattenedNotifications.push(sectionHeader)

        // Add notifications for this section
        flattenedNotifications.push(...(notifications as unknown as NotificationListItem[]))
      }
    },
  )

  return flattenedNotifications
}

/**
 * Toggles the follow state of a notification
 */
export const toggleNotificationFollow = (
  notifications: Notification[],
  notificationId: string,
): Notification[] => {
  return notifications.map((notification) =>
    notification.id === notificationId
      ? { ...notification, isFollowing: !notification.isFollowing }
      : notification,
  )
}

/**
 * Handles notification action press
 */
export const handleNotificationAction = (notificationId: string, action: string) => {
  console.log(`Action ${action} pressed for notification ${notificationId}`)
  // TODO: Implement specific action handling logic
}

/**
 * Handles user profile navigation
 */
export const handleUserProfilePress = (userId: string, userName: string) => {
  console.log(`User ${userName} (${userId}) pressed`)
  // TODO: Navigate to user profile
}

/**
 * Handles event navigation
 */
export const handleEventPress = (eventName: string) => {
  console.log(`Event ${eventName} pressed`)
  // TODO: Navigate to event details
}

/**
 * Checks if a list item is a notification section header
 */
export const isNotificationSection = (item: NotificationListItem): item is NotificationSection => {
  return "isHeader" in item && item.isHeader === true
}

/**
 * Gets the count of unread notifications
 */
export const getUnreadNotificationsCount = (notifications: Notification[]): number => {
  return notifications.filter((notification) => !notification.isRead).length
}

/**
 * Marks all notifications as read
 */
export const markAllNotificationsAsRead = (notifications: Notification[]): Notification[] => {
  return notifications.map((notification) => ({
    ...notification,
    isRead: true,
  }))
}
