import React, { FC, useState } from "react"
import { View, ViewStyle, StatusBar, TouchableOpacity, Modal, Platform } from "react-native"
import { Screen, Text, Icon, Button } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { AppStackScreenProps } from "@/navigators"
import { useNotificationsData, useNotificationsActions, useNotificationsNavigation } from "./hooks"
import { NotificationsList, NotificationEmptyState } from "./components"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import type { ThemedStyle } from "@/theme"

type NotificationsScreenProps = AppStackScreenProps<"Notifications">

export const NotificationsScreen: FC<NotificationsScreenProps> = ({ navigation }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  // Data hook - notifications data only
  const {
    notifications,
    flattenedNotifications,
    loading,
    error,
    setNotifications,
    clearAllNotifications,
    toggleFollowNotification,
  } = useNotificationsData()

  // Business actions hook - business logic only
  const {
    handleClearAllNotifications,
    handleFollowPress,
    handleActionPress,
  } = useNotificationsActions({
    onClearAll: clearAllNotifications,
    onFollowToggle: toggleFollowNotification,
  })

  // Navigation hook - navigation actions only
  const {
    handleBackPress: _navigateBack,
    handleUserPress,
    handleEventPress,
  } = useNotificationsNavigation({ navigation })

  // Modal visible state (Android only)
  const isIOS = Platform.OS === "ios"
  const [visible, setVisible] = useState(!isIOS)

  // Unified close handler: hides modal on Android then pops stack
  const handleBackPress = () => {
    if (!isIOS) setVisible(false)
    _navigateBack()
  }

  // -----------------------
  // 🎨 Build Screen Content
  // -----------------------

  let content: JSX.Element

  if (loading) {
    content = (
      <Screen preset="fixed" safeAreaEdges={[]} contentContainerStyle={themed($container)}>
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />
        {/* Modal Header */}
        <View style={themed($header)}>
          <TouchableOpacity onPress={handleBackPress} style={themed($headerButton)}>
            <Icon icon="x" size={24} />
          </TouchableOpacity>
          <Text text="Notifications" style={themed($headerTitle)} size="lg" weight="semiBold" />
          <View style={themed($headerButton)} />
        </View>
        <View style={themed($container)}>{/* Could add a loading spinner component here */}</View>
      </Screen>
    )
  } else if (error) {
    content = (
      <Screen preset="fixed" safeAreaEdges={[]} contentContainerStyle={themed($container)}>
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />
        {/* Modal Header */}
        <View style={themed($header)}>
          <TouchableOpacity onPress={handleBackPress} style={themed($headerButton)}>
            <Icon icon="x" size={24} />
          </TouchableOpacity>
          <Text text="Notifications" style={themed($headerTitle)} size="lg" weight="semiBold" />
          <View style={themed($headerButton)} />
        </View>
        <View style={themed($container)}>{/* Could add an error component here */}</View>
      </Screen>
    )
  } else {
    content = (
      <Screen preset="fixed" safeAreaEdges={[]} contentContainerStyle={themed($container)}>
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />

        {/* Modal Header */}
        <View style={themed($header)}>
          <TouchableOpacity onPress={handleBackPress} style={themed($headerButton)}>
            <Icon icon="x" size={24} />
          </TouchableOpacity>
          <Text text="Notifications" style={themed($headerTitle)} size="lg" weight="semiBold" />
          {notifications.length > 0 && (
            <Button
              text="Clear All"
              onPress={handleClearAllNotifications}
              style={themed($clearButton)}
              preset="filled"
            />
          )}
        </View>

        {/* Main content */}
        <View style={[themed($content), $bottomInset]}>
          {notifications.length === 0 ? (
            <NotificationEmptyState />
          ) : (
            <NotificationsList
              data={flattenedNotifications}
              onFollowPress={handleFollowPress}
              onActionPress={handleActionPress}
              onUserPress={handleUserPress}
              onEventPress={handleEventPress}
            />
          )}
        </View>
      </Screen>
    )
  }

  // --------------
  // 🛑 Return JSX
  // --------------

  if (isIOS) return content

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleBackPress}
    >
      {content}
    </Modal>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  backgroundColor: colors.background,
})

const $headerButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  minWidth: 60,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

const $clearButton: ThemedStyle<ViewStyle> = () => ({
  minWidth: 60,
  minHeight: 40,
  paddingHorizontal: 16,
  paddingVertical: 4,
})

const $content: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})
