import { observer } from "mobx-react-lite"
import { FC, useCallback } from "react"
import { ViewStyle } from "react-native"
import { Screen, Text, TextField, Button } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useResetPasswordActions } from "./hooks/useResetPasswordActions"
import type { ResetPasswordScreenProps } from "@/screens/ResetPassword/types"

export const ResetPasswordScreen: FC<ResetPasswordScreenProps> = observer(function ResetPasswordScreen(_props) {
  const { navigation } = _props
  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  const { authEmail, setAuthEmail, error, resetPassword, isLoading, attemptsCount } =
    useResetPasswordActions()

  const handleReset = useCallback(async () => {
    const ok = await resetPassword()
    if (ok) {
      navigation.navigate("Login")
    }
  }, [resetPassword, navigation])

  return (
    <Screen preset="fixed" safeAreaEdges={["top", "bottom"]} contentContainerStyle={[themed($container), $bottomInset]}>
      <Text text="Reset Password" preset="heading" style={themed($heading)} />
      <Text text="Enter your email to receive a reset link." preset="subheading" style={themed($subheading)} />

      {attemptsCount > 2 && (
        <Text text="Please check your email and try again." size="sm" weight="light" style={themed($hint)} />
      )}

      <TextField
        value={authEmail}
        onChangeText={setAuthEmail}
        containerStyle={themed($textField)}
        autoCapitalize="none"
        autoComplete="email"
        autoCorrect={false}
        keyboardType="email-address"
        label="Email"
        placeholder="<EMAIL>"
        helper={error}
        status={error ? "error" : undefined}
        onSubmitEditing={handleReset}
      />

      <Button
        text="Reset Password"
        preset="reversed"
        style={themed($tapButton)}
        onPress={handleReset}
        disabled={isLoading || authEmail.length === 0}
      />
    </Screen>
  )
})

// -----------------------------------------------------------------------------
// Styles
// -----------------------------------------------------------------------------
const $container: ViewStyle = {
  flex: 1,
  paddingHorizontal: 20,
  paddingTop: 40,
}

const $heading: ViewStyle = {
  marginBottom: 4,
}

const $subheading: ViewStyle = {
  marginBottom: 16,
}

const $hint: ViewStyle = {
  marginBottom: 16,
}

const $textField: ViewStyle = {
  marginBottom: 12,
}

const $tapButton: ViewStyle = {
  marginTop: 16,
} 