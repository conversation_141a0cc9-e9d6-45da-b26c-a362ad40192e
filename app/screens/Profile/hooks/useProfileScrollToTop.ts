import { useRef, useCallback } from "react"
import { ScrollView } from "react-native"
import { useFocusEffect } from "@react-navigation/native"
import { scrollManager } from "../utils"

export const useProfileScrollToTop = () => {
  const scrollViewRef = useRef<ScrollView>(null)

  const scrollToTop = useCallback(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: true })
    }
  }, [])

  useFocusEffect(
    useCallback(() => {
      // Set this screen as the active one for scroll-to-top
      scrollManager.setActiveScrollFunction(scrollToTop)

      return () => {
        // Clear when screen loses focus
        scrollManager.setActiveScrollFunction(null)
      }
    }, [scrollToTop]),
  )

  return {
    scrollViewRef,
    scrollToTop,
  }
}
