import { handleProfileShare } from "../utils"
import type { ProfileData, ProfileEvent, UserCommunity } from "../types"

interface UseProfileActionsProps {
  profileData: ProfileData
  onSettingsOpen: () => void
  onSettingsClose: () => void
  onEditProfileOpen?: () => void
  onChangePasswordOpen?: () => void
  setThemeContextOverride: (theme: "light" | "dark") => void
  themeContext: string | undefined
}

export const useProfileActions = ({
  profileData,
  onSettingsOpen,
  onSettingsClose,
  onEditProfileOpen,
  onChangePasswordOpen,
  setThemeContextOverride,
  themeContext,
}: UseProfileActionsProps) => {
  // Profile business actions
  const handleSharePress = async () => {
    await handleProfileShare(profileData)
  }

  const handleSettingsPress = () => {
    onSettingsOpen()
  }

  const handleCloseSettingsPopup = () => {
    onSettingsClose()
  }

  const handleToggleLightMode = (enabled: boolean) => {
    setThemeContextOverride(enabled ? "light" : "dark")
  }

  // Settings popup actions
  const handleEditProfile = () => {
    onSettingsClose()
    onEditProfileOpen?.()
  }

  const handleChangePassword = () => {
    onSettingsClose()
    onChangePasswordOpen?.()
  }

  const handleHelp = () => {
    // TODO: Implement help logic
    console.log("Help pressed")
    onSettingsClose()
  }

  return {
    // Profile actions
    handleSharePress,
    handleSettingsPress,
    handleCloseSettingsPopup,
    handleToggleLightMode,

    // Settings actions
    handleEditProfile,
    handleChangePassword,
    handleHelp,
  }
}
