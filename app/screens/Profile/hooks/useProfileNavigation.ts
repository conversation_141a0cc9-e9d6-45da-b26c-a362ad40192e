import type { ProfileEvent, UserCommunity } from "../types"

interface UseProfileNavigationProps {
  navigation: any
  isCurrentUser: boolean
}

export const useProfileNavigation = ({
  navigation,
  isCurrentUser,
}: UseProfileNavigationProps) => {
  // Navigation actions
  const handleBackPress = () => {
    navigation?.goBack()
  }

  // Event navigation
  const handleEventPress = (event: ProfileEvent) => {
    // TODO: Navigate to event details or results
    console.log("Event pressed:", event.id)
  }

  const handleEventDetailPress = (event: ProfileEvent) => {
    if (event.status === "finished") {
      navigation?.navigate("EventResult", { eventId: event.id })
    } else {
      navigation?.navigate("EventDetail", { eventId: event.id })
    }
  }

  // Community navigation
  const handleCommunityPress = (community: UserCommunity) => {
    navigation?.navigate("CommunityDetail", { communityId: community.id })
  }

  return {
    // Navigation actions
    handleBackPress,

    // Event navigation
    handleEventPress,
    handleEventDetailPress,

    // Community navigation
    handleCommunityPress,
  }
} 