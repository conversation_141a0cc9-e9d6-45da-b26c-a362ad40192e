import { useState } from "react"
import type { ProfileTab } from "../types"

interface UseProfileUIActionsProps {
  isCurrentUser: boolean
}

export const useProfileUIActions = ({ isCurrentUser }: UseProfileUIActionsProps) => {
  const [activeTab, setActiveTab] = useState<ProfileTab>("matches")
  const [settingsPopupVisible, setSettingsPopupVisible] = useState(false)

  const handleTabChange = (tab: ProfileTab) => {
    setActiveTab(tab)
  }

  const handleSettingsPopupOpen = () => {
    if (isCurrentUser) {
      setSettingsPopupVisible(true)
    }
  }

  const handleSettingsPopupClose = () => {
    setSettingsPopupVisible(false)
  }

  return {
    // UI State
    activeTab,
    settingsPopupVisible,

    // UI Actions
    handleTabChange,
    handleSettingsPopupOpen,
    handleSettingsPopupClose,
  }
} 