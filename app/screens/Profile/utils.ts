import { Share } from "react-native"
import { safeAsync } from "@/utils/safeAsync"
import { useStores } from "@/models"
import type { ProfileData } from "./types"
import profileDataRaw from "@/data/user_data.json"

// Type for the user data structure
interface UserDataStructure {
  users: Record<string, ProfileData>
  currentUserId: string
}

const userData: UserDataStructure = profileDataRaw as any

// Global reference to track active screen and scroll function
let activeProfileScreenScrollToTop: (() => void) | null = null

/**
 * Manages the global scroll-to-top functionality for ProfileScreen
 */
export const scrollManager = {
  /**
   * Sets the active scroll function for the profile screen
   */
  setActiveScrollFunction: (scrollFn: (() => void) | null) => {
    activeProfileScreenScrollToTop = scrollFn
  },

  /**
   * Triggers scroll to top if an active function is set
   */
  triggerScrollToTop: () => {
    if (activeProfileScreenScrollToTop) {
      activeProfileScreenScrollToTop()
    }
  },
}

/**
 * Gets user data by ID, defaults to current user
 * @param userId - Optional user ID, defaults to current user
 * @returns ProfileData for the specified user
 */
export const getUserData = (userId?: string): ProfileData => {
  const currentUserId = getCurrentUserId()
  const targetUserId = userId || currentUserId
  return userData.users[targetUserId] || userData.users[currentUserId]
}

/**
 * Gets the current user ID from authentication store or fallback to data file
 * @returns Current user ID string
 */
export const getCurrentUserId = (): string => {
  // For now, fallback to data file until authentication is fully implemented
  // In a real app, this would come from the authentication store
  return userData.currentUserId
}

/**
 * Checks if the given user ID is the current user
 * @param userId - User ID to check
 * @returns True if user is current user
 */
export const isCurrentUser = (userId?: string): boolean => {
  if (!userId) return true // No userId means viewing own profile
  return userId === getCurrentUserId()
}

/**
 * Placeholder function to set current user (for testing)
 * In production, this would be handled by the authentication flow
 * @param userId - User ID to set as current user
 */
export const setCurrentUserForTesting = (userId: string) => {
  // This is a placeholder - in real app this would update the auth store
  console.log(`Setting current user to: ${userId}`)
  // You can uncomment the line below to temporarily modify the data for testing
  // (userData as any).currentUserId = userId
}

/**
 * Generates share content for a user profile
 * @param profileData - User's profile data
 * @returns Share configuration object
 */
export const generateProfileShareContent = (profileData: ProfileData) => ({
  message: `Check out ${profileData.profile.name}'s padel profile!\n\nStats:\n• ${profileData.stats.totalMatches} Total Matches\n• ${profileData.stats.socialWins} Social Wins\n• ${profileData.stats.tournamentWins} Tournament Wins\n\nJoin our padel community!`,
  title: `${profileData.profile.name}'s Padel Profile`,
})

/**
 * Handles profile sharing with error handling
 * @param profileData - User's profile data
 */
export const handleProfileShare = async (profileData: ProfileData) => {
  await safeAsync(async () => {
    const shareContent = generateProfileShareContent(profileData)
    return await Share.share(shareContent)
  })
}
