import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, EventCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { typography } from "@/theme"
import type { ThemedStyle } from "@/theme"
import type { ProfileEvent } from "../types"

export interface MatchesProps {
  upcomingEvents: ProfileEvent[]
  pastEvents: ProfileEvent[]
  onEventPress: (event: ProfileEvent) => void
  onEventDetailPress?: (event: ProfileEvent) => void
}

export const Matches: FC<MatchesProps> = ({
  upcomingEvents,
  pastEvents,
  onEventPress,
  onEventDetailPress,
}) => {
  const { themed } = useAppTheme()

  const renderEventCard = (event: ProfileEvent) => (
    <EventCard
      key={event.id}
      event={event as any}
      onJoin={() => onEventPress(event)}
      onPress={() => onEventDetailPress?.(event)}
    />
  )

  return (
    <View>
      {upcomingEvents.length > 0 && (
        <View style={themed($section)}>
          <Text text="Upcoming Events" style={themed($sectionTitle)} />
          <View style={themed($sectionContent)}>
            {upcomingEvents.map((event) => renderEventCard(event))}
          </View>
        </View>
      )}

      {pastEvents.length > 0 && (
        <View style={themed($section)}>
          <Text text="Past Events" style={themed($sectionTitle)} />
          <View style={themed($sectionContent)}>
            {pastEvents.map((event) => renderEventCard(event))}
          </View>
        </View>
      )}
    </View>
  )
}

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  marginBottom: spacing.md,
  fontSize: 20,
  lineHeight: 32,
  fontFamily:
    typography.primary?.semiBold || typography.primary?.medium || typography.primary?.normal,
  fontWeight: "600",
})

const $sectionContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xxs,
})
