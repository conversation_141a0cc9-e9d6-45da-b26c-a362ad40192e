import { FC, Fragment } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon, Avatar } from "@/components"
import { ProfileHeaderProps, ProfileTab } from "../types"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const ProfileHeader: FC<ProfileHeaderProps> = ({
  profile,
  stats,
  activeTab,
  onTabChange,
  isCurrentUser = true,
  onMessagePress,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const handleMessagePress = () => {
    // Placeholder action - you can replace this with actual messaging logic
    console.log("Message icon pressed for user:", profile.name)
    onMessagePress?.()
  }

  const statItems = [
    { label: "Total Matches", value: stats.totalMatches.toString() },
    { label: "Social Won", value: stats.socialWins.toString() },
    { label: "Tournament Won", value: stats.tournamentWins.toString() },
  ]

  const tabs: { key: ProfileTab; label: string }[] = [
    { key: "matches", label: "Matches" },
    { key: "communities", label: "Communities" },
    { key: "trophies", label: "Trophies" },
  ]

  return (
    <View style={themed($header)}>
      {/* Profile Info */}
      <View style={themed($profileInfo)}>
        <Avatar
          name={profile.name}
          size="lg"
          style={themed($avatar)}
          textStyle={themed($avatarText)}
          textSize="xl"
          textWeight="bold"
        />
        <View style={themed($profileDetails)}>
          <Text text={profile.name} style={themed($profileName)} size="lg" weight="bold" />
          <Text text={profile.description} style={themed($profileDescription)} size="sm" />
        </View>
        {!isCurrentUser && (
          <TouchableOpacity style={themed($messageIcon)} onPress={handleMessagePress}>
            <Icon icon="message" size={24} color={colors.palette.primary500} />
          </TouchableOpacity>
        )}
      </View>

      {/* Stats */}
      <View style={themed($statsContainer)}>
        {statItems.map((stat, index) => (
          <Fragment key={index}>
            <View style={themed($statItem)}>
              <Text text={stat.value} style={themed($statValue)} size="xxl" weight="bold" />
              <Text text={stat.label} style={themed($statLabel)} size="xs" />
            </View>
            {index < statItems.length - 1 && (
              <View key={`divider-${index}`} style={themed($divider)} />
            )}
          </Fragment>
        ))}
      </View>

      {/* Tab Selector */}
      <View style={themed($tabContainer)}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.key}
            style={[themed($tab), activeTab === tab.key && themed($activeTab)]}
            onPress={() => onTabChange(tab.key)}
          >
            <Text
              text={tab.label}
              style={[themed($tabText), activeTab === tab.key && themed($activeTabText)]}
              size="xs"
              weight="medium"
              numberOfLines={1}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral200, // Lighter background color
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.md,
  paddingBottom: spacing.md,
})

const $profileInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
  marginBottom: spacing.lg,
})

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 52,
  height: 52,
  borderRadius: 32,
  backgroundColor: "#6366f1",
  justifyContent: "center",
  alignItems: "center",
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $profileDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $profileName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
})

const $profileDescription: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $messageIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  alignSelf: "center",
})

const $statsContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-around",
  marginBottom: spacing.lg,
})

const $statItem: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
})

const $statValue: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
})

const $statLabel: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginTop: spacing.xs,
  textAlign: "center",
})

const $divider: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 1,
  height: "100%",
  backgroundColor: colors.palette.neutral400,
})

const $tabContainer: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flexDirection: "row",
  backgroundColor: colors.palette.neutral300,
  borderRadius: spacing.sm,
  padding: spacing.xs,
})

const $tab: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
  borderRadius: spacing.xs,
  alignItems: "center",
  justifyContent: "center",
})

const $activeTab: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral100,
})

const $tabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  textAlign: "center",
  fontSize: 14,
})

const $activeTabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  textAlign: "center",
  fontSize: 14,
})
