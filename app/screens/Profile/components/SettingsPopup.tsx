import { FC } from "react"
import { Popup, PopupMenuItem, PopupPosition } from "@/components"

export interface SettingsPopupProps {
  visible: boolean
  onClose: () => void
  onEditProfile?: () => void
  onChangePassword?: () => void
  onHelp?: () => void
  lightMode?: boolean
  onToggleLightMode?: (enabled: boolean) => void
  position?: PopupPosition // Add position prop
}

export const SettingsPopup: FC<SettingsPopupProps> = ({
  visible,
  onClose,
  onEditProfile,
  onChangePassword,
  onHelp,
  lightMode = false,
  onToggleLightMode,
  position, // Destructure position prop
}) => {
  const menuItems: PopupMenuItem[] = [
    {
      icon: "view",
      title: "Edit Profile",
      onPress: () => {
        onEditProfile?.()
        onClose()
      },
    },
    {
      icon: "lock",
      title: "Change Password",
      onPress: () => {
        onChangePassword?.()
        onClose()
      },
    },
    {
      icon: "more",
      title: "Help",
      onPress: () => {
        onHelp?.()
        onClose()
      },
    },
    {
      icon: "settings",
      title: "Light Mode",
      onPress: () => {}, // Not used for toggles
      isToggle: true,
      toggleValue: lightMode,
      onToggleChange: onToggleLightMode,
    },
  ]

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      items={menuItems}
      iconSize={16}
      textSize="xs"
      paddingVertical={12}
      paddingHorizontal={16}
      borderRadius={12}
      position={position} // Use the passed position prop
    />
  )
}
