import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { CommunityCardLight } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { UserCommunity } from "../types"

export interface CommunitiesProps {
  communities: UserCommunity[]
  onCommunityPress: (community: UserCommunity) => void
}

export const Communities: FC<CommunitiesProps> = ({ communities, onCommunityPress }) => {
  const { themed } = useAppTheme()

  const renderCommunityCard = (community: UserCommunity) => (
    <CommunityCardLight key={community.id} community={community} onPress={onCommunityPress} />
  )

  return (
    <View style={themed($section)}>
      <View style={themed($sectionContent)}>
        {communities.map((community) => renderCommunityCard(community))}
      </View>
    </View>
  )
}

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl,
})

const $sectionContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm, // Add proper spacing between community cards
})
