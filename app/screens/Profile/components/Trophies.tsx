import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { TrophyCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { Trophy } from "../types"

export interface TrophiesProps {
  trophies: Trophy[]
}

export const Trophies: FC<TrophiesProps> = ({ trophies }) => {
  const { themed } = useAppTheme()

  const renderTrophyCard = (trophy: Trophy) => <TrophyCard key={trophy.id} trophy={trophy} />

  return (
    <View style={themed($section)}>
      <View style={themed($sectionContent)}>
        {trophies.map((trophy) => renderTrophyCard(trophy))}
      </View>
    </View>
  )
}

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl,
})

const $sectionContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm, // Add proper spacing between trophy cards
})
