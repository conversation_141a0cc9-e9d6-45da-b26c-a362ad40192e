import { CommunityData } from "@/types/communities"
import { EventLight } from "@/components/EventCardLight"
import { UserCommunity } from "@/components/CommunityCardLight"
import communitiesDataRaw from "@/data/communities.json"
import eventsDataRaw from "@/data/events.json"

const typedCommunitiesData: { communities: CommunityData[] } = communitiesDataRaw as any
const typedEventsData: EventLight[] = eventsDataRaw as any

// Scroll manager instance for the MainScreen
class MainScreenScrollManager {
  private scrollToTopFunction: (() => void) | null = null

  registerScrollToTop(fn: () => void) {
    this.scrollToTopFunction = fn
  }

  unregisterScrollToTop() {
    this.scrollToTopFunction = null
  }

  triggerScrollToTop() {
    if (this.scrollToTopFunction) {
      this.scrollToTopFunction()
    }
  }
}

export const scrollManager = new MainScreenScrollManager()

/**
 * Get random communities for trending section
 */
export const getRandomCommunities = (count: number = 2): UserCommunity[] => {
  const allCommunities = typedCommunitiesData.communities
  const shuffled = [...allCommunities].sort(() => 0.5 - Math.random())

  return shuffled.slice(0, count).map((community) => ({
    id: community.id,
    name: community.name,
    members: community.memberCount,
    role: community.isJoined ? "Member" : "Admin",
    color: community.backgroundImage, // Using the background gradient as color
  }))
}

/**
 * Get random events for featured section
 */
export const getRandomEvents = (count: number = 2): EventLight[] => {
  const shuffled = [...typedEventsData].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

/**
 * Mock user data - in a real app this would come from authentication store
 */
export const getCurrentUser = () => ({
  id: "current-user-123",
  name: "John Padel",
  initials: "JP",
  avatar: null, // Using initials instead
})


