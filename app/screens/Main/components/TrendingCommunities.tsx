import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Text, SectionCarousel, CommunityCardLight } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { UserCommunity } from "@/components/CommunityCardLight"
import type { ThemedStyle } from "@/theme"

interface TrendingCommunitiesProps {
  trendingCommunities: UserCommunity[]
  onCommunityPress: (community: UserCommunity) => void
}

export const TrendingCommunities: FC<TrendingCommunitiesProps> = ({
  trendingCommunities,
  onCommunityPress,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($trendingCommunitiesSection)}>
      <View style={themed($trendingCommunitiesTitleContainer)}>
        <Text text="Trending Communities" style={themed($trendingCommunitiesTitle)} />
      </View>
      <SectionCarousel
        data={trendingCommunities}
        renderItem={(community) => (
          <CommunityCardLight
            community={community}
            onPress={onCommunityPress}
            showStatusBadge={false}
          />
        )}
        keyExtractor={(community) => community.id}
        itemsPerRow={2}
        itemWidth={280}
        itemSpacing={10}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  )
}

const $trendingCommunitiesSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $trendingCommunitiesTitleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  marginBottom: spacing.sm,
})

const $trendingCommunitiesTitle: ThemedStyle<any> = ({ colors }) => ({
  color: colors.text,
  fontSize: 18,
  fontWeight: "600",
}) 