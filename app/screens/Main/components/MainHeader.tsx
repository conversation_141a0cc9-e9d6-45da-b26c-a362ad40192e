import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle, StatusBar } from "react-native"
import { Text, Avatar, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { observer } from "mobx-react-lite"
import { useStores } from "@/models"

interface MainHeaderProps {
  onAvatarPress: () => void
  onNotificationPress: () => void
  onInboxPress: () => void
  onLogoutPress: () => void
}

const MainHeaderComponent: FC<MainHeaderProps> = ({ onAvatarPress, onNotificationPress, onInboxPress, onLogoutPress }) => {
  const { themed } = useAppTheme()
  const { userProfileStore } = useStores()
  const fullName = userProfileStore.displayName || "Player"
  const firstName = fullName.split(" ")[0]
  const $topSafeArea = useSafeAreaInsetsStyle(["top"])

  return (
    <View style={[themed($header), $topSafeArea]}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <View style={themed($headerContent)}>
        <Avatar
          name={fullName}
          size={48}
          style={themed($avatar)}
          textStyle={themed($avatarText)}
          textWeight="medium"
          onPress={onAvatarPress}
        />
        <View style={themed($greetingContainer)}>
          <Text text="Hello," style={themed($helloText)} size="sm" />
          <Text text={firstName} style={themed($nameText)} size="lg" weight="semiBold" />
        </View>
        <View style={themed($iconButtonsContainer)}>
          <TouchableOpacity onPress={onNotificationPress} style={themed($iconButton)}>
            <Icon icon="bell" size={24} />
          </TouchableOpacity>
          <TouchableOpacity onPress={onInboxPress} style={themed($iconButton)}>
            <Icon icon="inbox" size={24} />
          </TouchableOpacity>
          <TouchableOpacity onPress={onLogoutPress} style={themed($iconButton)}>
            <Icon icon="logout" size={24} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

export const MainHeader = observer(MainHeaderComponent)

const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.sm,
})

const $headerContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
})

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#6366f1",
  justifyContent: "center",
  alignItems: "center",
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $greetingContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $helloText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  lineHeight: 16,
  marginBottom: 2,
})

const $nameText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  lineHeight: 20,
  marginTop: 4,
})

const $iconButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  minWidth: 44,
  minHeight: 44,
  justifyContent: "center",
  alignItems: "center",
})

const $iconButtonsContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xxs,
})
