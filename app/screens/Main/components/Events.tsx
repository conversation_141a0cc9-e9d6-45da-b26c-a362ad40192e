import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { SectionCard, EventCardLight } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { EventLight } from "@/components/EventCardLight"
import type { ThemedStyle } from "@/theme"

interface EventsProps {
  featuredEvents: EventLight[]
  onEventPress: (event: EventLight) => void
}

export const Events: FC<EventsProps> = ({
  featuredEvents,
  onEventPress,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($content)}>
      <SectionCard title="Events from Your Communities">
        <View style={themed($eventsGrid)}>
          {featuredEvents.map((event) => (
            <EventCardLight
              key={event.id}
              event={event}
              onPress={onEventPress}
              onJoin={(eventId) => console.log("Join event:", eventId)}
            />
          ))}
        </View>
      </SectionCard>
    </View>
  )
}

const $content: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.xl,
  gap: spacing.lg,
})

const $eventsGrid: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm,
}) 