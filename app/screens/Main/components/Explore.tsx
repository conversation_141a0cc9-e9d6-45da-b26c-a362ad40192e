import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Text, Carousel } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface ExploreProps {
  onExploreCommunities: () => void
  onExploreEvents: () => void
  onExploreFeed: () => void
}

export const Explore: FC<ExploreProps> = ({
  onExploreCommunities,
  onExploreEvents,
  onExploreFeed,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($carouselSection)}>
      <View style={themed($carouselTitleContainer)}>
        <Text text="Explore" style={themed($carouselTitle)} />
      </View>
      <Carousel
        onExploreCommunities={onExploreCommunities}
        onExploreEvents={onExploreEvents}
        onExploreFeed={onExploreFeed}
      />
    </View>
  )
}

const $carouselSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $carouselTitleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  marginBottom: spacing.sm,
})

const $carouselTitle: ThemedStyle<any> = ({ colors }) => ({
  color: colors.text,
  fontSize: 18,
  fontWeight: "600",
}) 