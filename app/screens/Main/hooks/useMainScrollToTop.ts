import { useRef, useCallback, useEffect } from "react"
import { ScrollView } from "react-native"
import { useFocusEffect } from "@react-navigation/native"
import { scrollManager } from "../utils"

export const useMainScrollToTop = () => {
  const scrollViewRef = useRef<ScrollView>(null)

  const scrollToTop = useCallback(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: 0, animated: true })
    }
  }, [])

  // Register/unregister scroll function with the manager
  useFocusEffect(
    useCallback(() => {
      scrollManager.registerScrollToTop(scrollToTop)

      return () => {
        scrollManager.unregisterScrollToTop()
      }
    }, [scrollToTop]),
  )

  return {
    scrollViewRef,
  }
}
