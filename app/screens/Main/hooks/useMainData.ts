import { useState, useEffect } from "react"
import { MainScreenData } from "../types"
import { getRandomCommunities, getRandomEvents, getCurrentUser } from "../utils"

export const useMainData = (): MainScreenData & { user: ReturnType<typeof getCurrentUser> } => {
  const [trendingCommunities, setTrendingCommunities] = useState(getRandomCommunities(8))
  const [featuredEvents, setFeaturedEvents] = useState(getRandomEvents(2))
  const user = getCurrentUser()

  // Refresh data when component mounts
  useEffect(() => {
    setTrendingCommunities(getRandomCommunities(8))
    setFeaturedEvents(getRandomEvents(2))
  }, [])

  return {
    trendingCommunities,
    featuredEvents,
    user,
  }
}
