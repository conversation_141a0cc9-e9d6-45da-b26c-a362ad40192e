import { useCallback } from "react"
import { UserCommunity } from "@/components/CommunityCardLight"
import { EventLight } from "@/components/EventCardLight"
import { useStores } from "@/models"
import { MainScreenActions } from "../types"

interface UseMainNavigationProps {
  navigation: any
}

export const useMainNavigation = ({ navigation }: UseMainNavigationProps): MainScreenActions => {
  const { authenticationStore, userProfileStore } = useStores()
  
  const handleAvatarPress = useCallback(() => {
    navigation.navigate("Profile")
  }, [navigation])

  const handleNotificationPress = useCallback(() => {
    navigation.navigate("Notifications")
  }, [navigation])

  const handleInboxPress = useCallback(() => {
    navigation.navigate("Inbox")
  }, [navigation])

  const handleLogoutPress = useCallback(() => {
    // Clear authentication state - this will automatically redirect to Login
    // due to conditional rendering in AppNavigator
    authenticationStore.logout()
    userProfileStore.clear()
  }, [authenticationStore])

  const handleExploreCommunities = useCallback(() => {
    navigation.navigate("Communities")
  }, [navigation])

  const handleExploreEvents = useCallback(() => {
    navigation.navigate("Events")
  }, [navigation])

  const handleExploreFeed = useCallback(() => {
    // TODO: Navigate to feed screen when implemented
    console.log("Explore Feed - TODO: Implement feed screen navigation")
  }, [])

  const handleCommunityPress = useCallback(
    (community: UserCommunity) => {
      navigation.navigate("CommunityDetail", { communityId: community.id })
    },
    [navigation],
  )

  const handleEventPress = useCallback(
    (event: EventLight) => {
      navigation.navigate("EventDetail", { eventId: event.id })
    },
    [navigation],
  )

  return {
    handleAvatarPress,
    handleNotificationPress,
    handleInboxPress,
    handleLogoutPress,
    handleExploreCommunities,
    handleExploreEvents,
    handleExploreFeed,
    handleCommunityPress,
    handleEventPress,
  }
} 