import { UserCommunity } from "@/components/CommunityCardLight"
import { EventLight } from "@/components/EventCardLight"

/**
 * Main screen data interface
 */
export interface MainScreenData {
  trendingCommunities: UserCommunity[]
  featuredEvents: EventLight[]
}

/**
 * User profile information for header
 */
export interface MainUserProfile {
  id: string
  name: string
  initials: string
  avatar?: string | null
}

/**
 * Main screen actions interface
 */
export interface MainScreenActions {
  handleAvatarPress: () => void
  handleNotificationPress: () => void
  handleInboxPress: () => void
  handleLogoutPress: () => void
  handleExploreCommunities: () => void
  handleExploreEvents: () => void
  handleExploreFeed: () => void
  handleCommunityPress: (community: UserCommunity) => void
  handleEventPress: (event: EventLight) => void
}
