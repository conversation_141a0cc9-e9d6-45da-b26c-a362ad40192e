import { useRef, useState } from "react"
import { TextInput } from "react-native"
import { useStores } from "@/models"
import { SupabaseAuthService } from "@/services/supabase/auth-service"

export const useSignUpActions = () => {
  const authPasswordInput = useRef<TextInput>(null)

  const [authPassword, setAuthPassword] = useState("")
  const [isAuthPasswordHidden, setIsAuthPasswordHidden] = useState(true)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [attemptsCount, setAttemptsCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const {
    authenticationStore: {
      authEmail,
      setAuthEmail,
      setAuthToken,
      validationError,
      setCurrentUserId,
    },
  } = useStores()

  const error = isSubmitted ? validationError : ""

  async function signUp(): Promise<boolean> {
    if (isLoading) return false

    setIsSubmitted(true)
    setAttemptsCount(attemptsCount + 1)

    if (validationError) {
      return false
    }

    try {
      setIsLoading(true)
      // Send OTP email which will create user if not exists
      await SupabaseAuthService.requestEmailOtp(authEmail, true)

      // Keep password for verification screen; don't clear here

      setIsSubmitted(false)

      return true
    } catch (e) {
      console.error("Sign-up failed", e)
      return false
    } finally {
      setIsLoading(false)
    }
    return false
  }

  return {
    authPasswordInput,
    authPassword,
    setAuthPassword,
    isAuthPasswordHidden,
    setIsAuthPasswordHidden,
    attemptsCount,
    authEmail,
    setAuthEmail,
    error,
    signUp,
    isLoading,
  }
} 