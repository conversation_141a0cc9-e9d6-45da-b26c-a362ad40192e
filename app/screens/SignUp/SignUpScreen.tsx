import { observer } from "mobx-react-lite"
import { FC, ComponentType, useState } from "react"
import { ViewStyle, TextStyle, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from "react-native"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { Button, Screen, Text, TextField, TextFieldAccessoryProps } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { PasswordRightAccessory } from "@/screens/Login/components/PasswordRightAccessory"
import { useSignUpActions } from "./hooks/useSignUpActions"
import { useCallback } from "react"
import type { SignUpScreenProps } from "@/screens/SignUp/types"

export const SignUpScreen: FC<SignUpScreenProps> = observer(function SignUpScreen(_props) {
  const { navigation } = _props
  const {
    authPasswordInput,
    authPassword,
    setAuthPassword,
    isAuthPassword<PERSON>idden,
    setIsAuthPasswordHidden,
    attemptsCount,
    authEmail,
    setAuthEmail,
    error,
    signUp,
    isLoading,
  } = useSignUpActions()

  const [displayName, setDisplayName] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [passwordError, setPasswordError] = useState<string | undefined>()
  const [displayNameError, setDisplayNameError] = useState<string | undefined>()

  const validatePasswords = () => {
    if (authPassword !== confirmPassword) {
      setPasswordError("Passwords do not match")
      return false
    }
    setPasswordError(undefined)
    return true
  }

  const validateDisplayName = () => {
    if (displayName.trim().length === 0) {
      setDisplayNameError("Display name required")
      return false
    }
    setDisplayNameError(undefined)
    return true
  }

  const handleSignUp = useCallback(async () => {
    const validName = validateDisplayName()
    const validPw = validatePasswords()
    if (!validName || !validPw) return

    const needsVerification = await signUp()
    if (needsVerification) {
      navigation.navigate("EmailVerification", { email: authEmail, password: authPassword, displayName })
      // clear password locally
      setAuthPassword("")
      setConfirmPassword("")
    }
  }, [signUp, authEmail, navigation, authPassword, setAuthPassword, confirmPassword, displayName])

  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  const RightAccessory: ComponentType<TextFieldAccessoryProps> = (props) => (
    <PasswordRightAccessory
      {...props}
      isAuthPasswordHidden={isAuthPasswordHidden}
      setIsAuthPasswordHidden={setIsAuthPasswordHidden}
    />
  )

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["top", "bottom"]}
      contentContainerStyle={themed($container)}
    >
      <KeyboardAvoidingView
        style={themed($keyboardContainer)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={themed($scrollView)}
          contentContainerStyle={[themed($screenContentContainer), $bottomInset]}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Text text="Create Account" preset="heading" style={themed($heading)} />
          <Text text="Enter your email and password" preset="subheading" style={themed($subheading)} />
          {attemptsCount > 2 && (
            <Text text="Please check your details and try again." size="sm" weight="light" style={themed($hint)} />
          )}

          <TextField
            value={authEmail}
            onChangeText={setAuthEmail}
            containerStyle={themed($textField)}
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect={false}
            keyboardType="email-address"
            label="Email"
            placeholder="<EMAIL>"
            helper={error}
            status={error ? "error" : undefined}
            onSubmitEditing={() => authPasswordInput.current?.focus()}
          />

          <TextField
            value={displayName}
            onChangeText={setDisplayName}
            containerStyle={themed($textField)}
            autoCapitalize="words"
            label="Display Name"
            placeholder="John Doe"
            helper={displayNameError}
            status={displayNameError ? "error" : undefined}
          />

          <TextField
            ref={authPasswordInput}
            value={authPassword}
            onChangeText={setAuthPassword}
            containerStyle={themed($textField)}
            autoCapitalize="none"
            autoComplete="password"
            autoCorrect={false}
            secureTextEntry={isAuthPasswordHidden}
            label="Password"
            placeholder="••••••••"
            onSubmitEditing={handleSignUp}
            RightAccessory={RightAccessory}
          />

          <TextField
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            containerStyle={themed($textField)}
            autoCapitalize="none"
            autoComplete="password"
            autoCorrect={false}
            secureTextEntry={isAuthPasswordHidden}
            label="Confirm Password"
            placeholder="••••••••"
            helper={passwordError}
            status={passwordError ? "error" : undefined}
            onSubmitEditing={handleSignUp}
            RightAccessory={RightAccessory}
          />

          <Button
            text={isLoading ? undefined : "Sign Up"}
            style={themed($tapButton)}
            preset="reversed"
            onPress={handleSignUp}
            disabled={isLoading || !!displayNameError || !!passwordError || authPassword.length === 0}
            RightAccessory={isLoading ? undefined : undefined}
          >
            {isLoading && <ActivityIndicator color="#fff" />}
          </Button>

          {/* Back to Login */}
          <Text
            text="Back to Login"
            size="sm"
            weight="light"
            style={{ marginTop: 16, textAlign: "center" }}
            onPress={() => _props.navigation?.navigate?.("Login")}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  )
})

// -----------------------------------------------------------------------------
// Styles
// -----------------------------------------------------------------------------
const $screenContentContainer: ViewStyle = {
  paddingHorizontal: 20,
  paddingTop: 40,
}

const $heading: TextStyle = {
  marginBottom: 4,
}

const $subheading: TextStyle = {
  marginBottom: 16,
}

const $hint: TextStyle = {
  color: "red",
  marginBottom: 16,
}

const $textField: ViewStyle = {
  marginBottom: 12,
}

const $tapButton: ViewStyle = {
  marginTop: 16,
}

const $container: ViewStyle = {
  flex: 1,
}

const $keyboardContainer: ViewStyle = {
  flex: 1,
}

const $scrollView: ViewStyle = {
  flex: 1,
} 