import { FC } from "react"
import { View, ViewStyle, StatusBar } from "react-native"
import { Screen, Text, EmptyState } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  CommunityHeader,
  CommunityTabs,
  HomeTab,
  EventsTab,
  ChatTab,
  MembersTab,
} from "./components"
import { 
  useCommunityData, 
  useCommunityActions, 
  useCommunityNavigation,
  useCommunityUIActions 
} from "./hooks"
import { getSafeAreaConfig } from "@/utils/safeAreaHelpers"
import { useHeader } from "@/utils/useHeader"
import { observer } from "mobx-react-lite"
import type { ThemedStyle } from "@/theme"
import type { CommunityDetailScreenProps } from "./types"

export const CommunityDetailScreen: FC<CommunityDetailScreenProps> = observer(function CommunityDetailScreen({ route, navigation }) {
  const { communityId } = route.params

  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Data hook - community data only
  const { community, loading, error } = useCommunityData({ communityId })

  // UI state hook - tab management
  const { activeTab, setActiveTab } = useCommunityUIActions()

  // Business actions hook - business logic only
  const {
    handleSharePress,
    handleSettingsPress,
    handleJoinEvent,
    handleFollowMember,
  } = useCommunityActions({ community, navigation })

  // Navigation hook - navigation actions only
  const {
    handleBack,
    handleEventPress,
    handleMessageMember,
    handleAdminPress,
    handleMemberPress,
  } = useCommunityNavigation({ navigation, community })

  // Configure native header using useHeader hook
  useHeader(
    {
      showBackButton: true,
      onBackPress: handleBack,
      rightButtons: [
        {
          icon: "share",
          onPress: handleSharePress,
        },
        {
          icon: "settings",
          onPress: handleSettingsPress,
        },
      ],
      style: themed($reducedPaddingHeader),
    },
    [community, handleSharePress, handleSettingsPress],
  )

  // Loading state
  if (loading) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top", "bottom"]}
        contentContainerStyle={themed($loadingContainer)}
      >
        <Text tx="communityDetailScreen:loadingCommunity" />
      </Screen>
    )
  }

  // Error state
  if (error || !community) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top", "bottom"]}
        contentContainerStyle={themed($loadingContainer)}
      >
        <EmptyState
          headingTx="communityDetailScreen:errorHeading"
          contentTx={error ? "communityDetailScreen:communityError" : "communityDetailScreen:communityNotFound"}
          buttonTx="common:goBack"
          buttonOnPress={handleBack}
        />
      </Screen>
    )
  }

  const renderActiveTabContent = () => {
    switch (activeTab) {
      case "home":
        return <HomeTab community={community} onAdminPress={handleAdminPress} />
      case "events":
        return (
          <EventsTab
            community={community}
            onJoinEvent={handleJoinEvent}
            onEventPress={handleEventPress}
          />
        )
      case "chat":
        return <ChatTab community={community} />
      case "members":
        return (
          <MembersTab
            community={community}
            onFollowMember={handleFollowMember}
            onMessageMember={handleMessageMember}
            onMemberPress={handleMemberPress}
          />
        )
      default:
        return <HomeTab community={community} onAdminPress={handleAdminPress} />
    }
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={getSafeAreaConfig("CONTENT_ONLY")}
      contentContainerStyle={themed($container)}
      statusBarStyle="light"
      backgroundColor={colors.palette.neutral100}
    >
      <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />

      {/* Community Header (avatar, title, member info) */}
      <CommunityHeader community={community} />

      <CommunityTabs activeTab={activeTab} onTabChange={setActiveTab} />

      <View style={themed(activeTab === "chat" ? $chatContentContainer : $contentContainer)}>
        {renderActiveTabContent()}
      </View>
    </Screen>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.palette.neutral100,
})

const $loadingContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: colors.background,
  padding: spacing.lg,
})

const $reducedPaddingHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: spacing.xs, // Reduced from default spacing.md
})

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.lg,
})

const $chatContentContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})
