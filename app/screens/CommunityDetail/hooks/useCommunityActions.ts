import { handleCommunityShare } from "../utils"
import { CommunityEditService } from "@/services/CommunityEditService"
import type { UseCommunityActionsProps } from "../types"

/**
 * Custom hook for managing community business actions
 */
export const useCommunityActions = ({
  community,
  navigation,
}: Pick<UseCommunityActionsProps, 'community' | 'navigation'>) => {
  const handleSharePress = async () => {
    await handleCommunityShare(community)
  }

  /**
   * Navigate to the CreateCommunity flow with the current community pre-filled
   */
  const handleSettingsPress = () => {
    if (!community) return

    const initialData = CommunityEditService.toFormData(community)

    navigation.navigate("CreateCommunity", { mode: "edit", initialData })
  }

  const handleJoinEvent = (event: any) => {
    console.log("Join event:", event.title)
  }

  const handleFollowMember = (memberId: number) => {
    console.log("Follow member:", memberId)
  }

  return {
    // Business actions only
    handleSharePress,
    handleSettingsPress,
    handleJoinEvent,
    handleFollowMember,
  }
}
