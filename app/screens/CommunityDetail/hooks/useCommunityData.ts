import { useState, useEffect } from "react"
import { getCommunityById } from "../utils"
import type { UseCommunityDataProps } from "../types"
import { translate } from "@/i18n"

/**
 * Custom hook for managing community data only
 */
export const useCommunityData = ({
  communityId,
}: UseCommunityDataProps) => {
  const [community, setCommunity] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCommunity = async () => {
      try {
        setLoading(true)
        setError(null)

        const foundCommunity = getCommunityById(communityId)

        if (foundCommunity) {
          setCommunity(foundCommunity)
        } else {
          setError(translate("communityDataHook:communityNotFound"))
        }
      } catch (err) {
        setError(translate("communityDataHook:failedToLoadCommunity"))
      } finally {
        setLoading(false)
      }
    }

    fetchCommunity()
  }, [communityId])

  return {
    // Data only
    community,
    loading,
    error,
  }
}
