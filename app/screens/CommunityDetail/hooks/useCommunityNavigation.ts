import { nameToUserId } from "../utils"
import userData from "@/data/user_data.json"
import { MessageService } from "@/services/MessageService"
import type { ConversationSummary } from "@/types/messages"
import { translate } from "@/i18n"

interface UseCommunityNavigationProps {
  navigation: any
  community: any
}

export const useCommunityNavigation = ({
  navigation,
  community,
}: UseCommunityNavigationProps) => {
  const handleBack = () => {
    navigation.goBack()
  }

  const handleEventPress = (event: any) => {
    navigation.navigate("EventDetail", { eventId: event.id })
  }

  const handleMessageMember = (memberId: number) => {
    // Find the member by ID to get their name
    const member = community?.members.find((m: any) => m.id === memberId)
    if (!member) {
      console.log(translate("communityNavigationHook:memberNotFoundForID", { memberId }))
      return
    }

    // Current logged-in user id from mock data
    const currentUserId: string = (userData as any).currentUserId

    // Look for an existing conversation with this member via MessageService
    let participantId = ""
    try {
      const summaries: ConversationSummary[] = MessageService.getConversationSummaries(
        currentUserId,
      )
      const existing = summaries.find(
        (s) => s.otherUserName.toLowerCase() === member.name.toLowerCase(),
      )
      participantId = existing?.otherUserId ?? ""
    } catch (e) {
      console.warn("MessageService lookup failed", e)
    }

    // Fallback: derive user id from name if not found
    if (!participantId) {
      participantId = nameToUserId(member.name)
    }

    navigation.navigate("PrivateChat", {
      participantId,
      participantName: member.name,
    })
  }

  const handleAdminPress = (admin: any) => {
    const userId = nameToUserId(admin.name)
    navigation.navigate("MemberProfile", { userId })
  }

  const handleMemberPress = (member: any) => {
    const userId = nameToUserId(member.name)
    navigation.navigate("MemberProfile", { userId })
  }

  return {
    // Navigation actions
    handleBack,
    handleEventPress,
    handleMessageMember,
    handleAdminPress,
    handleMemberPress,
  }
}
