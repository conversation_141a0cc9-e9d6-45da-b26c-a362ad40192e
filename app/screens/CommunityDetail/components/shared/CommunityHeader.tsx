import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"

interface CommunityHeaderProps {
  community: CommunityData
}

export const CommunityHeader: FC<CommunityHeaderProps> = ({ community }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($header)}>
      {/* Community Info */}
      <View style={themed($communityInfo)}>
        <View style={themed($avatar)}>
          <Icon icon="community" size={18} color={colors.palette.neutral100} />
        </View>
        <View style={themed($communityDetails)}>
          <Text text={community.name} style={themed($communityName)} size="lg" weight="bold" />
          <View style={themed($memberInfo)}>
            <Icon icon="people" size={12} color={colors.palette.neutral100} />
            <Text
              tx="communityHeader:membersCount"
              txOptions={{ count: community.memberCount }}
              style={themed($memberCount)}
              size="sm"
            />
          </View>
        </View>
      </View>
    </View>
  )
}

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral800,
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.md,
})

const $communityInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
})

const $avatar: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#4a9d8e",
  justifyContent: "center",
  alignItems: "center",
})

const $communityDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $communityName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontWeight: "700",
})

const $memberInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  marginTop: 2,
})

const $memberCount: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})
