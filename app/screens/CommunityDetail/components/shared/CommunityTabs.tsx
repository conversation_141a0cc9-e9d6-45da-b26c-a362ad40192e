import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable, Keyboard } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface CommunityTabsProps {
  activeTab: "home" | "events" | "chat" | "members"
  onTabChange: (tab: "home" | "events" | "chat" | "members") => void
}

export const CommunityTabs: FC<CommunityTabsProps> = ({ activeTab, onTabChange }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const tabs = [
    { key: "home" as const, title: translate("communityTabs:homeTabTitle") },
    { key: "events" as const, title: translate("communityTabs:eventsTabTitle") },
    { key: "chat" as const, title: translate("communityTabs:chatTabTitle") },
    { key: "members" as const, title: translate("communityTabs:membersTabTitle") },
  ]

  return (
    <View style={themed($tabsContainer)}>
      {tabs.map((tab) => (
        <Pressable
          key={tab.key}
          style={themed([$tab, activeTab === tab.key && $activeTab])}
          onPress={() => {
            Keyboard.dismiss()
            onTabChange(tab.key)
          }}
        >
          <Text
            text={tab.title}
            style={themed([$tabText, activeTab === tab.key && $activeTabText])}
            size="sm"
          />
          {activeTab === tab.key && <View style={themed($activeIndicator)} />}
        </Pressable>
      ))}
    </View>
  )
}

const $tabsContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.border,
})

const $tab: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flex: 1,
  alignItems: "center",
  paddingVertical: spacing.xs,
  position: "relative",
  backgroundColor: colors.palette.neutral100,
})

const $activeTab: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral100,
})

const $tabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $activeTabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontWeight: "bold",
})

const $activeIndicator: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: colors.text,
})
