import { FC, useRef, useState, useEffect } from "react"
import {
  View,
  ViewStyle,
  ScrollView,
  Keyboard,
  TouchableWithoutFeedback,
  Platform,
  Alert,
} from "react-native"
import { ChatInputContainer } from "@/components"
import { ChatItem } from "./ChatItem"
import { EventLight } from "@/components/EventCardLight"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { CommunityData } from "@/types/communities"
import { useNavigation } from "@react-navigation/native"
import { useStores } from "@/models"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface ChatTabProps {
  community: CommunityData
}

export const ChatTab: FC<ChatTabProps> = ({ community }) => {
  const scrollViewRef = useRef<ScrollView>(null)
  const {
    themed,
    theme: { colors, spacing },
  } = useAppTheme()
  const navigation = useNavigation<any>()
  const $bottomInsets = useSafeAreaInsetsStyle(["bottom"])
  const { authenticationStore } = useStores()

  // 🔧 TEMPORARY: Set current user as admin for testing delete functionality
  // In production, this would be handled by proper authentication flow
  if (!authenticationStore.currentUserId) {
    authenticationStore.setCurrentUserId("1") // John Doe - Community Admin
  }

  // Keyboard handling state - following PrivateChatScreen pattern
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)

  // Keyboard event listeners - following PrivateChatScreen pattern
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      (e) => {
        setKeyboardHeight(e.endCoordinates.height)
        setIsKeyboardVisible(true)
      },
    )

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      () => {
        setKeyboardHeight(0)
        setIsKeyboardVisible(false)
      },
    )

    return () => {
      keyboardDidShowListener?.remove()
      keyboardDidHideListener?.remove()
    }
  }, [])

  const scrollToBottom = () => {
    scrollViewRef.current?.scrollToEnd({ animated: true })
  }

  const handleScrollViewPress = () => {
    Keyboard.dismiss()
  }

  const handleSendMessage = (message: string) => {
    console.log("Send message:", message)
    // Scroll to bottom after sending message
    setTimeout(scrollToBottom, 100)
  }

  const handleEventJoin = (eventId: number) => {
    console.log("Join event from chat:", eventId)
  }

  const handleAuthorPress = (authorName: string) => {
    const userId = authorName.toLowerCase().replace(/\s+/g, "_")
    navigation.navigate("MemberProfile", { userId })
  }

  const handleEventPress = (event: EventLight) => {
    navigation.navigate("EventDetail", { eventId: event.id })
  }

  // Helper function to check if current user can delete messages
  const canDeleteMessage = (messageAuthor: string): boolean => {
    const currentUserId = authenticationStore.currentUserId
    if (!currentUserId) return false

    // Check if user is admin or moderator in community
    const isAdminOrModerator = community.adminsAndModerators.some(
      (admin) => admin.id.toString() === currentUserId,
    )

    // Check if user is admin or moderator in members list
    const isMemberWithAdminRole = community.members.some(
      (member) =>
        member.id.toString() === currentUserId &&
        (member.role === "Admin" || member.role === "Moderator"),
    )

    // Check if user is message author (allow users to delete their own messages)
    const isMessageAuthor =
      messageAuthor === currentUserId || messageAuthor.toLowerCase() === "john doe" // Temporary: match admin name

    // User can delete if they are admin/moderator OR if they authored the message
    return isAdminOrModerator || isMemberWithAdminRole || isMessageAuthor
  }

  const handleChatItemLongPress = (message: any) => {
    if (!canDeleteMessage(message.author)) {
      return // User doesn't have permission to delete
    }

    Alert.alert(
      translate("chatTab:deleteMessageTitle"),
      translate("chatTab:deleteMessageContent"),
      [
        {
          text: translate("chatTab:cancelButton"),
          style: "cancel",
        },
        {
          text: translate("chatTab:deleteButton"),
          style: "destructive",
          onPress: () => {
            // Here you would implement the actual delete functionality
            // For now, just logging
            console.log("Delete message:", message.id)
          },
        },
      ],
    )
  }

  return (
    <View
      style={[
        themed($container),
        // Apply safe area bottom insets when keyboard is NOT visible
        // Apply small padding when keyboard IS visible
        isKeyboardVisible ? { paddingBottom: spacing.sm } : $bottomInsets,
      ]}
    >
      <TouchableWithoutFeedback onPress={handleScrollViewPress}>
        {/* Chat Messages Area - This should take up the remaining space */}
        <View style={themed($chatContainer)}>
          <ScrollView
            ref={scrollViewRef}
            style={themed($messagesContainer)}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={scrollToBottom}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="interactive"
            // Better gesture handling properties
            directionalLockEnabled={true}
            nestedScrollEnabled={true}
            scrollEventThrottle={16}
          >
            {community.chatMessages.map((chatMessage) => (
              <ChatItem
                key={chatMessage.id}
                message={chatMessage}
                onEventJoin={handleEventJoin}
                onAuthorPress={handleAuthorPress}
                onEventPress={handleEventPress}
                onLongPress={handleChatItemLongPress}
              />
            ))}
          </ScrollView>
        </View>
      </TouchableWithoutFeedback>

      {/* Chat Input - This should be at the bottom */}
      <ChatInputContainer
        onSendMessage={handleSendMessage}
        placeholder={translate("chatTab:typeYourMessagePlaceholder")}
      />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $chatContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $messagesContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.md,
})
