import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text, Avatar } from "@/components"
import { EventCardLight, EventLight } from "@/components/EventCardLight"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface ChatMessage {
  id: number
  author: string
  content: string
  timestamp: string
  type: "message" | "event"
  eventId?: number
  eventType?: string
  eventDetails?: {
    title?: string
    date: string
    participants: string
    price: string
    location: string
  }
}

interface ChatItemProps {
  message: ChatMessage
  onEventJoin?: (eventId: number) => void
  onAuthorPress?: (authorName: string) => void
  onEventPress?: (event: EventLight) => void
  onLongPress?: (message: ChatMessage) => void
}

export const ChatItem: FC<ChatItemProps> = ({
  message,
  onEventJoin,
  onAuthorPress,
  onEventPress,
  onLongPress,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const handleAuthorPress = () => {
    if (onAuthorPress) {
      onAuthorPress(message.author)
    }
  }

  const handleEventPress = (event: EventLight) => {
    if (onEventPress) {
      onEventPress(event)
    }
  }

  const handleEventJoin = (eventId: number) => {
    if (onEventJoin) {
      onEventJoin(eventId)
    }
  }

  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress(message)
    }
  }

  // Convert chat message event details to EventLight format
  const getEventFromMessage = (): EventLight | null => {
    if (message.type !== "event" || !message.eventDetails || !message.eventId) {
      return null
    }

    return {
      id: message.eventId,
      type: message.eventType || "Social",
      title: message.eventDetails.title || message.content,
      date: message.eventDetails.date,
      location: message.eventDetails.location,
      price: message.eventDetails.price,
      participants: parseInt(message.eventDetails.participants.split("/")[0]) || 0,
      maxParticipants: parseInt(message.eventDetails.participants.split("/")[1]) || 0,
    }
  }

  const event = getEventFromMessage()

  return (
    <View style={themed($chatItem)}>
      <Pressable
        onLongPress={handleLongPress}
        delayLongPress={500}
        android_ripple={{ color: colors.palette.neutral300, borderless: false }}
        style={({ pressed }) => [
          themed($messageContainer),
          pressed && { backgroundColor: colors.palette.neutral100 },
        ]}
      >
        {/* Avatar */}
        <Avatar
          name={message.author}
          size="md"
          style={themed($avatar)}
          textStyle={themed($avatarText)}
          onPress={handleAuthorPress}
        />

        {/* Message Content */}
        <View style={themed($messageContent)}>
          {/* Header with Author Name and Timestamp */}
          <View style={themed($messageHeader)}>
            <Pressable onPress={handleAuthorPress}>
              <Text text={message.author} style={themed($authorName)} weight="bold" size="sm" />
            </Pressable>
            <Text text={message.timestamp} style={themed($timestamp)} size="xs" />
          </View>

          {/* Message Text */}
          {message.content && (
            <Text text={message.content} style={themed($messageText)} size="sm" />
          )}

          {/* Event Card (if any) */}
          {event && (
            <EventCardLight event={event} onJoin={handleEventJoin} onPress={handleEventPress} />
          )}
        </View>
      </Pressable>
    </View>
  )
}

const $chatItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({})

const $messageContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.xxs,
})

const $avatar: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#6366f1",
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
  flexShrink: 0,
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 16,
  fontWeight: "600",
})

const $messageContent: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  minWidth: 0, // Prevents overflow
})

const $messageHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: spacing.xs,
})

const $authorName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $timestamp: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginLeft: spacing.xs,
})

const $messageText: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  lineHeight: 20,
})
