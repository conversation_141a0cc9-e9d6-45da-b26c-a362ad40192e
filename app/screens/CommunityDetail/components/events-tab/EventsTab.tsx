import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { type ContentStyle } from "@shopify/flash-list"
import { Text } from "@/components"
import { EventCard } from "@/components/EventCard"
import { ListView } from "@/components/ListView"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"

interface EventsTabProps {
  community: CommunityData
  onJoinEvent: (event: any) => void
  onEventPress?: (event: any) => void
}

export const EventsTab: FC<EventsTabProps> = ({ community, onJoinEvent, onEventPress }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($container)}>
      <ListView
        contentContainerStyle={themed($listContentContainer)}
        data={community.upcomingEvents.slice()}
        extraData={community.upcomingEvents.length}
        estimatedItemSize={177}
        ListHeaderComponent={
          <View style={themed($header)}>
            <Text tx="eventsTab:upcomingEventsTitle" style={themed($headerTitle)} weight="semiBold" size="lg" />
          </View>
        }
        ListEmptyComponent={
          <View style={themed($emptyState)}>
            <Text tx="eventsTab:noUpcomingEvents" style={themed($emptyText)} />
          </View>
        }
        renderItem={({ item }) => (
          <EventCard key={item.id} event={item} onJoin={onJoinEvent} onPress={onEventPress} />
        )}
        showsVerticalScrollIndicator={false}
        // Better gesture handling properties
        directionalLockEnabled={true}
        nestedScrollEnabled={true}
        scrollEventThrottle={16}
      />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  backgroundColor: colors.palette.neutral100,
})

const $listContentContainer: ThemedStyle<ContentStyle> = ({ spacing }) => ({
  paddingBottom: spacing.sm,
})

const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
  marginTop: spacing.sm,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({}) => ({
  flex: 1,
})

const $emptyState: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.xl,
})

const $emptyText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})
