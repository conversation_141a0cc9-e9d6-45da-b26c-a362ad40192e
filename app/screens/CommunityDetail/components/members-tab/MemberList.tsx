import { FC } from "react"
import { View, ViewStyle, ScrollView } from "react-native"
import { UserCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"

interface MemberListProps {
  community: CommunityData
  filteredMembers: CommunityData['members']
  onFollowMember: (memberId: number) => void
  onMessageMember: (memberId: number) => void
  onMemberPress: (member: any) => void
}

export const MemberList: FC<MemberListProps> = ({
  community,
  filteredMembers,
  onFollowMember,
  onMessageMember,
  onMemberPress,
}) => {
  const { themed } = useAppTheme()

  const handleFollowToggle = (memberId: string) => {
    onFollowMember(Number(memberId))
  }

  const handleMessage = (memberId: string) => {
    onMessageMember(Number(memberId))
  }

  const handleMemberPress = (memberId: string) => {
    const member = filteredMembers.find(m => m.id.toString() === memberId)
    if (member) {
      onMemberPress(member)
    }
  }

  return (
    <ScrollView
      style={themed($container)}
      showsVerticalScrollIndicator={false}
      directionalLockEnabled={true}
      nestedScrollEnabled={true}
      scrollEventThrottle={16}
    >
      <View style={themed($membersList)}>
        {filteredMembers.map((member) => (
          <UserCard
            key={member.id}
            user={{
              id: member.id.toString(),
              name: member.name,
              role: member.role,
            }}
            onUserPress={handleMemberPress}
            onMessagePress={handleMessage}
            onFollowPress={handleFollowToggle}
            showMessageButton={true}
            showFollowButton={true}
            followState={member.isFollowing ? 'following' : 'follow'}
            avatarColor="#6366f1"
            containerStyle={themed($memberCard)}
          />
        ))}
      </View>
    </ScrollView>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $membersList: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs,
})

const $memberCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
}) 