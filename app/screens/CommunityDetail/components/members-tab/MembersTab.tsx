import { FC, useState } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon, TextField } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import { MemberList } from "./MemberList"
import type { ThemedStyle } from "@/theme"

interface MembersTabProps {
  community: CommunityData
  onFollowMember: (memberId: number) => void
  onMessageMember: (memberId: number) => void
  onMemberPress: (member: any) => void
}

export const MembersTab: FC<MembersTabProps> = ({
  community,
  onFollowMember,
  onMessageMember,
  onMemberPress,
}) => {
  const [searchTerm, setSearchTerm] = useState("")
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const filteredMembers = community.members.filter(
    (member) =>
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.role.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <View style={themed($container)}>
      <View style={themed($searchContainer)}>
        <View style={themed($searchInputContainer)}>
          <Icon icon="view" size={20} color={colors.textDim} />
          <TextField
            value={searchTerm}
            onChangeText={setSearchTerm}
            placeholderTx="membersTab:searchMembersPlaceholder"
            placeholderTextColor={colors.textDim}
            style={themed($searchInput)}
            containerStyle={themed($searchFieldContainer)}
            inputWrapperStyle={themed($searchFieldWrapper)}
          />
        </View>
      </View>

      <MemberList
        community={community}
        filteredMembers={filteredMembers}
        onFollowMember={onFollowMember}
        onMessageMember={onMessageMember}
        onMemberPress={onMemberPress}
      />
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.sm,
  paddingHorizontal: spacing.xxs,
})

const $searchContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  marginBottom: spacing.sm,
})

const $searchInputContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.md,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  height: 44,
})

const $searchFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  marginLeft: 8,
})

const $searchFieldWrapper: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "transparent",
  borderWidth: 0,
  paddingHorizontal: 0,
  paddingVertical: 0,
})

const $searchInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontSize: 16,
  color: colors.text,
  paddingVertical: 0,
})
