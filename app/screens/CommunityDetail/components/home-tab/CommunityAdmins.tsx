import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { SectionCard, UserCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface CommunityAdminsProps {
  community: CommunityData
  onAdminPress: (admin: any) => void
}

export const CommunityAdmins: FC<CommunityAdminsProps> = ({ community, onAdminPress }) => {
  const { themed } = useAppTheme()

  if (!community.adminsAndModerators || community.adminsAndModerators.length === 0) {
    return null
  }

  const handleAdminPress = (adminId: string) => {
    const admin = community.adminsAndModerators?.find(a => a.id.toString() === adminId)
    if (admin) {
      onAdminPress(admin)
    }
  }

  return (
    <SectionCard title={translate("communityAdmins:communityAdminsTitle")}>
      <View style={themed($adminsList)}>
        {community.adminsAndModerators.map((admin) => (
          <UserCard
            key={admin.id}
            user={{
              id: admin.id.toString(),
              name: admin.name || translate("communityAdmins:unknownAdmin"),
              role: admin.role || translate("communityAdmins:adminRole"),
            }}
            onUserPress={handleAdminPress}
            statusBadge={{
              status: admin.status || "offline",
              variant: "indicator"
            }}
            containerStyle={themed($adminCard)}
          />
        ))}
      </View>
    </SectionCard>
  )
}

const $adminsList: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm,
})

const $adminCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
})
