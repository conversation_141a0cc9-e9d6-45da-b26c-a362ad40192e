import { FC } from "react"
import { View, ViewStyle, ScrollView } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import { CommunityAbout } from "./CommunityAbout"
import { CommunityStats } from "./CommunityStats"
import { CommunityRules } from "./CommunityRules"
import { CommunityAdmins } from "./CommunityAdmins"
import type { ThemedStyle } from "@/theme"

interface HomeTabProps {
  community: CommunityData
  onAdminPress: (admin: any) => void
}

export const HomeTab: FC<HomeTabProps> = ({ community, onAdminPress }) => {
  const { themed } = useAppTheme()

  if (!community) {
    return (
      <View style={themed($container)}>
        <Text tx="homeTab:loadingCommunityData" />
      </View>
    )
  }

  return (
    <ScrollView
      style={themed($container)}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={themed($contentContainer)}
      nestedScrollEnabled={true}
      bounces={true}
      scrollEventThrottle={16}
      directionalLockEnabled={true}
    >
      <CommunityAbout community={community} />
      <CommunityStats community={community} />
      <CommunityRules community={community} />
      <CommunityAdmins community={community} onAdminPress={onAdminPress} />
    </ScrollView>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
})

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.xxs,
})
