import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, SectionCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface CommunityRulesProps {
  community: CommunityData
}

export const CommunityRules: FC<CommunityRulesProps> = ({ community }) => {
  const { themed } = useAppTheme()

  if (!community.rules || community.rules.length === 0) {
    return null
  }

  return (
    <SectionCard title={translate("communityRules:communityRulesTitle")}>
      <View style={themed($rulesList)}>
        {community.rules.map((rule, index) => (
          <View key={index} style={themed($ruleItem)}>
            <Text text="•" style={themed($ruleBullet)} />
            <Text text={rule} style={themed($ruleText)} />
          </View>
        ))}
      </View>
    </SectionCard>
  )
}

const $rulesList: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs,
})

const $ruleItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
})

const $ruleBullet: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.secondary500,
  lineHeight: 18,
})

const $ruleText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  lineHeight: 18,
})
