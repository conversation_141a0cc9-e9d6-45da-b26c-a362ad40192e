import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, SectionCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import { getStatStyles, getSafeStatValue } from "../../utils"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

interface CommunityStatsProps {
  community: CommunityData
}

export const CommunityStats: FC<CommunityStatsProps> = ({ community }) => {
  const { themed } = useAppTheme()
  const statStyles = getStatStyles()

  const statsData = [
    {
      value: getSafeStatValue(community.stats?.totalMembers || community.memberCount),
      label: translate("communityStats:totalMembers"),
      style: statStyles[0],
    },
    {
      value: getSafeStatValue(community.stats?.eventsThisMonth),
      label: translate("communityStats:eventsThisMonth"),
      style: statStyles[1],
    },
    {
      value: getSafeStatValue(community.stats?.gamesPlayed),
      label: translate("communityStats:gamesPlayed"),
      style: statStyles[2],
    },
    {
      value: getSafeStatValue(community.stats?.communityRating),
      label: translate("communityStats:communityRating"),
      style: statStyles[3],
    },
  ]

  return (
    <SectionCard title={translate("communityStats:communityStatsTitle")}>
      <View style={themed($statsGrid)}>
        {statsData.map((stat, index) => (
          <View
            key={index}
            style={[themed($statItem), { backgroundColor: stat.style.backgroundColor }]}
          >
            <Text
              text={stat.value}
              style={[themed($statNumber), { color: stat.style.textColor }]}
              weight="bold"
            />
            <Text text={stat.label} style={themed($statLabel)} size="sm" />
          </View>
        ))}
      </View>
    </SectionCard>
  )
}

const $statsGrid: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "space-between",
})

const $statItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: "48%",
  alignItems: "center",
  borderRadius: spacing.sm,
  justifyContent: "center",
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.xxs,
  marginBottom: spacing.sm,
})

const $statNumber: ThemedStyle<TextStyle> = () => ({
  fontSize: 24,
  fontWeight: "bold",
  marginBottom: 4,
})

const $statLabel: ThemedStyle<TextStyle> = ({ spacing }) => ({
  textAlign: "center",
  marginTop: spacing.xxs,
  lineHeight: 16,
})
