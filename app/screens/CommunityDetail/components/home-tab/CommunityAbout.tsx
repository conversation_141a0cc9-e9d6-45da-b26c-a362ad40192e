import { FC } from "react"
import { TextStyle } from "react-native"
import { Text, SectionCard } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CommunityData } from "@/types/communities"
import type { ThemedStyle } from "@/theme"

interface CommunityAboutProps {
  community: CommunityData
}

import { translate } from "@/i18n"

export const CommunityAbout: FC<CommunityAboutProps> = ({ community }) => {
  const { themed } = useAppTheme()

  return (
    <SectionCard title={translate("communityAbout:aboutCommunityTitle")}>
      <Text
        text={community.description || community.short_description || translate("communityAbout:noDescriptionAvailable")}
        style={themed($aboutText)}
        size="sm"
      />
    </SectionCard>
  )
}

const $aboutText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  lineHeight: 20,
})
