import { CommunityData } from "@/types/communities"
import { AppStackScreenProps } from "@/navigators"

// Main screen props
export type CommunityDetailScreenProps = AppStackScreenProps<"CommunityDetail">

// Tab types
export type CommunityTab = "home" | "events" | "chat" | "members"

// Hook interfaces
export interface UseCommunityDataProps {
  communityId: string
}

export interface UseCommunityDataReturn {
  community: CommunityData | null
  loading: boolean
  error: string | null
  activeTab: CommunityTab
  setActiveTab: (tab: CommunityTab) => void
}

export interface UseCommunityActionsProps {
  navigation: CommunityDetailScreenProps["navigation"]
  community: CommunityData | null
}

export interface UseCommunityActionsReturn {
  handleBack: () => void
  handleSharePress: () => Promise<void>
  handleSettingsPress: () => void
  handleJoinEvent: (event: any) => void
  handleEventPress: (event: any) => void
  handleFollowMember: (memberId: number) => void
  handleMessageMember: (memberId: number) => void
  handleAdminPress: (admin: any) => void
  handleMemberPress: (member: any) => void
}

// Community stats interfaces
export interface CommunityStats {
  totalMembers: number
  eventsThisMonth: number
  gamesPlayed: number
  communityRating: number
}

// Admin/Member interfaces
export interface CommunityAdmin {
  id: string | number
  name: string
  role: string
  initials?: string
  status?: string
}

export interface CommunityMember {
  id: number
  name: string
  role: string
  isFollowing: boolean
}
