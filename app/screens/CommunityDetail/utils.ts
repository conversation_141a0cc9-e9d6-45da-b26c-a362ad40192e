import { Share } from "react-native"
import { CommunityData } from "@/types/communities"
import { safeAsync } from "@/utils/safeAsync"
import communitiesDataRaw from "@/data/communities.json"

const typedCommunitiesData: { communities: CommunityData[] } = communitiesDataRaw as any

/**
 * Fetch community data by ID
 */
export const getCommunityById = (communityId: string): CommunityData | null => {
  return typedCommunitiesData.communities.find((c) => c.id === communityId) || null
}

/**
 * Generate share content for a community
 */
import { translate } from "@/i18n"

export const generateCommunityShareContent = (community: CommunityData) => {
  const memberCountText = community.memberCount > 1
    ? translate("communityUtils:memberPlural")
    : translate("communityUtils:memberSingular")
  const eventsText = community.upcomingEvents?.length
    ? `\n${translate("communityUtils:upcomingEventsText", { count: community.upcomingEvents.length })}`
    : ""
  const venueText = community.address ? `\n${community.address}` : ""

  return {
    message: `${translate("common:checkOut")} ${community.name}!${venueText}\n\n${community.memberCount} ${memberCountText}${eventsText}\n\n${community.description || community.short_description || translate("communityUtils:joinPadelCommunity")}\n\n${translate("communityUtils:joinUsForMatches")}`,
    title: translate("communityUtils:shareTitle", { communityName: community.name }),
  }
}

/**
 * Handle community sharing with error handling
 */
export const handleCommunityShare = async (community: CommunityData | null): Promise<void> => {
  if (!community) return

  await safeAsync(async () => {
    const shareContent = generateCommunityShareContent(community)
    return await Share.share(shareContent)
  })
}

/**
 * Convert user/admin name to userId format for navigation
 */
export const nameToUserId = (name: string): string => {
  return name?.toLowerCase().replace(/\s+/g, "_") || "unknown_user"
}

/**
 * Generate initials from name
 */
export { generateInitials } from "@/utils/avatar"

/**
 * Get stat colors for community stats display
 */
export const getStatStyles = () => [
  {
    backgroundColor: "#EFF6FF", // blue-50
    textColor: "#2563EB", // blue-600
  },
  {
    backgroundColor: "#F0FDF4", // green-50
    textColor: "#16A34A", // green-600
  },
  {
    backgroundColor: "#FAF5FF", // purple-50
    textColor: "#9333EA", // purple-600
  },
  {
    backgroundColor: "#FFF7ED", // orange-50
    textColor: "#EA580C", // orange-600
  },
]

/**
 * Get safe stat value with fallback
 */
export const getSafeStatValue = (value: any, fallback: string = "0"): string => {
  return value?.toString() || fallback
}
