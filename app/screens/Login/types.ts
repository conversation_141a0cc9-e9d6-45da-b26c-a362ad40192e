import { AppStackScreenProps } from "app/navigators"
import { ComponentType } from "react"
import { TextFieldAccessoryProps } from "app/components"
import { ThemedStyle } from "@/theme"
import { TextStyle, ViewStyle } from "react-native"

export interface LoginScreenProps extends AppStackScreenProps<"Login"> {}

export interface LoginContentProps {
  authEmail: string
  setAuthEmail: (email: string) => void
  authPassword: string
  setAuthPassword: (password: string) => void
  isAuthPasswordHidden: boolean
  setIsAuthPasswordHidden: (hidden: boolean) => void
  login: () => void
  error: string
  attemptsCount: number
  PasswordRightAccessory: ComponentType<TextFieldAccessoryProps>
}

export interface LoginScreenStyles {
  $screenContentContainer: ThemedStyle<ViewStyle>
  $logIn: ThemedStyle<TextStyle>
  $enterDetails: ThemedStyle<TextStyle>
  $hint: ThemedStyle<TextStyle>
  $textField: ThemedStyle<ViewStyle>
  $tapButton: ThemedStyle<ViewStyle>
}
