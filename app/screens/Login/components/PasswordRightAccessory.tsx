import { ComponentType, FC } from "react"
import { PressableIcon, TextFieldAccessoryProps } from "app/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface PasswordRightAccessoryProps extends TextFieldAccessoryProps {
  isAuthPasswordHidden: boolean
  setIsAuthPasswordHidden: (hidden: boolean) => void
}

export const PasswordRightAccessory: FC<PasswordRightAccessoryProps> = ({
  style,
  isAuthPasswordHidden,
  setIsAuthPasswordHidden,
}) => {
  const { theme: { colors } } = useAppTheme()

  return (
    <PressableIcon
      icon={isAuthPasswordHidden ? "view" : "hidden"}
      color={colors.palette.neutral800}
      containerStyle={style}
      size={20}
      onPress={() => setIsAuthPasswordHidden(!isAuthPasswordHidden)}
    />
  )
}
