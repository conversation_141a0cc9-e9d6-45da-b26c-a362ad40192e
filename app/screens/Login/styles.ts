import { ThemedStyle } from "@/theme"
import { TextStyle, ViewStyle } from "react-native"
import { LoginScreenStyles } from "./types"

export const $screenContentContainer: LoginScreenStyles["$screenContentContainer"] = ({ spacing }) => ({
  paddingVertical: spacing.xxl,
  paddingHorizontal: spacing.lg,
})

export const $logIn: LoginScreenStyles["$logIn"] = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

export const $enterDetails: LoginScreenStyles["$enterDetails"] = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

export const $hint: LoginScreenStyles["$hint"] = ({ colors, spacing }) => ({
  color: colors.tint,
  marginBottom: spacing.md,
})

export const $textField: LoginScreenStyles["$textField"] = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

export const $tapButton: LoginScreenStyles["$tapButton"] = ({ spacing }) => ({
  marginTop: spacing.xs,
})
