// Utilities for CreateEvent feature will go here 

// ────────────────────────────────────────────────────────────────────────────────
// Step configuration used across CreateEvent flow

/** Total number of steps in the CreateEvent flow */
export const TOTAL_STEPS = 3

/**
 * Calculates the progress percentage for a multi-step flow
 * @param currentStep - The current step number (1-based)
 * @param totalSteps - The total number of steps (defaults to TOTAL_STEPS)
 * @returns Progress percentage rounded to nearest integer (0-100)
 * 
 * @example
 * calculateProgressPercentage(1, 3) // returns 33
 * calculateProgressPercentage(2, 3) // returns 67
 * calculateProgressPercentage(3, 3) // returns 100
 */
export const calculateProgressPercentage = (currentStep: number, totalSteps: number = TOTAL_STEPS): number => {
  return Math.round((currentStep / totalSteps) * 100)
}

export const STEP_CONFIG = {
  1: {
    titleKey: "createEventScreen:step1Title" as const,
    progress: calculateProgressPercentage(1),
  },
  2: {
    titleKey: "createEventScreen:step2Title" as const,
    progress: calculateProgressPercentage(2),
  },
  3: {
    titleKey: "createEventScreen:step3Title" as const,
    progress: calculateProgressPercentage(3),
  },
} as const

export type StepNumber = keyof typeof STEP_CONFIG 