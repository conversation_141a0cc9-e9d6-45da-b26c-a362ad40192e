import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

/**
 * Blank placeholder for Members page.
 */
export const Members: FC = observer(function Members() {
  const { themed } = useAppTheme()
  return <View style={themed($container)} />
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
}) 