import { FC, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { Text, SegmentedSwitch } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle, TextStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const PairField: FC = observer(function PairField() {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { eventSettings, setEventSettings } = useCreateEventData()

  const options = useMemo(
    () => [
      { label: "Random", value: "Random" },
      { label: "First&Last", value: "First&Last" },
      { label: "Ladder", value: "Ladder" },
    ],
    [],
  )

  function update(val: string) {
    setEventSettings({ ...eventSettings, pairMethod: val as any })
  }

  return (
    <>
      <Text text="Pairing Method" style={themed($label)} size="sm" weight="medium" />
      <SegmentedSwitch
        options={options}
        value={eventSettings.pairMethod}
        onChange={update}
        selectionColor={colors.palette.primary500}
        style={themed($segmented)}
      />
    </>
  )
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
})

const $segmented: ThemedStyle<ViewStyle> = () => ({
  marginTop: 8,
  marginBottom: 16,
}) 