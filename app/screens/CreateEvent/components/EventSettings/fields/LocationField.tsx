import { FC } from "react"
import { observer } from "mobx-react-lite"
import { TextField } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const LocationField: FC = observer(function LocationField() {
  const { themed } = useAppTheme()
  const { eventSettings, setEventSettings } = useCreateEventData()

  function update(text: string) {
    setEventSettings({ ...eventSettings, location: text })
  }

  return (
    <TextField
      labelTx="eventSettings:locationLabel"
      placeholderTx="eventSettings:locationPlaceholder"
      value={eventSettings.location}
      onChangeText={update}
      containerStyle={themed($fieldContainer)}
    />
  )
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
}) 