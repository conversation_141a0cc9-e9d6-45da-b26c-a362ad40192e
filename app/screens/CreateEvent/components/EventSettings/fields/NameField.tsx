import { FC } from "react"
import { observer } from "mobx-react-lite"
import { TextField } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const NameField: FC = observer(function NameField() {
  const { themed } = useAppTheme()
  const { eventSettings, setEventSettings } = useCreateEventData()

  function update(text: string) {
    setEventSettings({ ...eventSettings, eventName: text })
  }

  return (
    <TextField
      labelTx="eventSettings:eventNameLabel"
      placeholderTx="eventSettings:eventNamePlaceholder"
      value={eventSettings.eventName}
      onChangeText={update}
      containerStyle={themed($fieldContainer)}
    />
  )
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
}) 