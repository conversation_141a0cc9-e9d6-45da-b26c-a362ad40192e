import { FC } from "react"
import { observer } from "mobx-react-lite"
import { TextField } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const NumCourtsField: FC = observer(function NumCourtsField() {
  const { themed } = useAppTheme()
  const { eventSettings, setEventSettings } = useCreateEventData()

  function update(text: string) {
    setEventSettings({ ...eventSettings, numCourts: text })
  }

  return (
    <TextField
      labelTx="eventSettings:numCourtsLabel"
      placeholderTx="eventSettings:numCourtsPlaceholder"
      keyboardType="numeric"
      value={eventSettings.numCourts}
      onChangeText={update}
      containerStyle={themed($fieldContainer)}
    />
  )
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
}) 