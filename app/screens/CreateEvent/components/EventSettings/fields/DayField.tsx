import { FC, useState, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"
import { DatePickerSheet } from "@/screens/CreateEvent/shared"
import { format } from "date-fns"
import { translate } from "@/i18n"

export const DayField: FC = observer(function DayField() {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { eventSettings, setEventSettings } = useCreateEventData()

  const [showPicker, setShowPicker] = useState(false)

  const displayDate = useMemo(() => {
    return eventSettings.day
      ? format(eventSettings.day, "dd/MM/yyyy")
      : translate("eventSettings:selectDayPrompt")
  }, [eventSettings.day])

  // ------------------------------------------------------------------------
  function update(date: Date) {
    setEventSettings({ ...eventSettings, day: date })
  }

  function handlePress() {
    setShowPicker(true)
  }

  return (
    <>
      <Text tx="eventSettings:dayLabel" style={themed($label)} size="sm" weight="medium" />
      <TouchableOpacity style={themed($selector)} onPress={handlePress} accessibilityRole="button">
        <Icon icon="calendar" size={20} color={colors.textDim} />
        <Text text={displayDate} style={themed($selectorText)} size="md" />
        <Icon icon="caretRight" size={16} color={colors.textDim} />
      </TouchableOpacity>

      {showPicker && (
        <DatePickerSheet
          visible={true}
          onClose={() => setShowPicker(false)}
          onSelect={update}
          value={eventSettings.day || new Date()}
          title={translate("eventSettings:selectDayPrompt")}
          mode="date"
        />
      )}
    </>
  )
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
})

const $selector: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderRadius: 8,
  gap: spacing.sm,
  marginBottom: spacing.lg,
})

const $selectorText: ThemedStyle<TextStyle> = ({ colors }) => ({
  flex: 1,
  color: colors.text,
}) 