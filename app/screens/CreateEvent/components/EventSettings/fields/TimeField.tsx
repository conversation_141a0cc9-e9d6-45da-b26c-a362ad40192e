import { FC, useState, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"
import { DatePickerSheet } from "@/screens/CreateEvent/shared"
import { format } from "date-fns"
import { translate, type TxKeyPath } from "@/i18n"

interface TimeFieldProps {
  field: "startTime" | "endTime"
  label?: string
  labelTx?: TxKeyPath
}

export const TimeField: FC<TimeFieldProps> = observer(function TimeField({ field, label, labelTx }) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { eventSettings, setEventSettings } = useCreateEventData()

  const [showPicker, setShowPicker] = useState(false)

  const displayTime = useMemo(() => {
    const val = eventSettings[field]
    return val ? format(val as Date, "HH:mm") : "--:--"
  }, [eventSettings[field], field])

  function update(date: Date) {
    setEventSettings({ ...eventSettings, [field]: date } as any)
  }

  function handlePress() {
    setShowPicker(true)
  }

  const displayLabel =
    labelTx
      ? translate(labelTx)
      : label ||
        translate(
          field === "startTime"
            ? "eventSettings:startTimeLabel"
            : "eventSettings:endTimeLabel",
        )

  return (
    <>
      <Text text={displayLabel} style={themed($label)} size="sm" weight="medium" />
      <TouchableOpacity
        style={themed($selector)}
        onPress={handlePress}
        accessibilityRole="button"
      >
        <Icon icon="clock" size={20} color={colors.textDim} />
        <Text text={displayTime} style={themed($selectorText)} size="md" />
        <Icon icon="caretRight" size={16} color={colors.textDim} />
      </TouchableOpacity>

      {showPicker && (
        <DatePickerSheet
          visible={true}
          onClose={() => setShowPicker(false)}
          onSelect={update}
          value={(eventSettings[field] as Date) || new Date()}
          title={translate("eventSettings:selectTimePrompt")}
          mode="time"
        />
      )}
    </>
  )
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
})

const $selector: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderRadius: 8,
  gap: spacing.sm,
  marginBottom: spacing.lg,
})

const $selectorText: ThemedStyle<TextStyle> = ({ colors }) => ({
  flex: 1,
  color: colors.text,
}) 