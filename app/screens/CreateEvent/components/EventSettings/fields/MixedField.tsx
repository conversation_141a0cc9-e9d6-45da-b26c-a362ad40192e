import { FC, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { Text, SegmentedSwitch } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle, TextStyle, View } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const MixedField: FC = observer(function MixedField() {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { eventSettings, setEventSettings } = useCreateEventData()

  const options = useMemo(
    () => [
      { label: "Yes", value: true },
      { label: "No", value: false },
    ],
    [],
  )

  function update(val: boolean) {
    setEventSettings({ ...eventSettings, isMixed: val })
  }

  return (
    <View style={themed($row)}>
      <Text text="Mixed?" style={themed($label)} size="sm" weight="medium" />
      <SegmentedSwitch
        options={options}
        value={eventSettings.isMixed}
        onChange={update}
        selectionColor={colors.palette.primary500}
        style={themed($segmented)}
      />
    </View>
  )
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
  flex: 1,
})

const $segmented: ThemedStyle<ViewStyle> = () => ({
  marginLeft: 8,
  width: 160,
})

const $row: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  marginTop: spacing.md,
  marginBottom: spacing.md,
}) 