import { FC, useMemo } from "react"
import { observer } from "mobx-react-lite"
import { Text, SegmentedSwitch } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle, TextStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const PointsField: FC = observer(function PointsField() {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const { eventSettings, setEventSettings } = useCreateEventData()

  /* -------------------------- Segmented Switch Options ------------------------- */
  const pointOptions = useMemo(() => [8, 16, 24, 32, 40].map((p) => ({ label: String(p), value: p })), [])

  function update(val: number) {
    setEventSettings({ ...eventSettings, points: val })
  }

  return (
    <>
      <Text tx="eventSettings:numberOfPointsLabel" style={themed($label)} size="sm" weight="medium" />
      <SegmentedSwitch
        options={pointOptions}
        value={eventSettings.points}
        onChange={update}
        selectionColor={colors.palette.primary500}
        style={themed($segmented)}
      />
    </>
  )
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginBottom: spacing.xs,
})

const $segmented: ThemedStyle<ViewStyle> = () => ({
  marginTop: 8,
  marginBottom: 16,
}) 