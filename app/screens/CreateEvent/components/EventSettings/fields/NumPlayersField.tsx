import { FC } from "react"
import { observer } from "mobx-react-lite"
import { TextField } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { ViewStyle } from "react-native"
import { useCreateEventData } from "@/screens/CreateEvent/hooks"

export const NumPlayersField: FC = observer(function NumPlayersField() {
  const { themed } = useAppTheme()
  const { eventSettings, setEventSettings } = useCreateEventData()

  function update(text: string) {
    setEventSettings({ ...eventSettings, numPlayers: text })
  }

  return (
    <TextField
      labelTx="eventSettings:numPlayersLabel"
      placeholderTx="eventSettings:numPlayersPlaceholder"
      keyboardType="numeric"
      value={eventSettings.numPlayers}
      onChangeText={update}
      containerStyle={themed($fieldContainer)}
    />
  )
})

const $fieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
}) 