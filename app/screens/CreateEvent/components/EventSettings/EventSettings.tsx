import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "@/components"
import { useCreateEventData, EventFormatKey } from "@/screens/CreateEvent/hooks"
import {
  AmericanoSettings,
  MexicanoSettings,
  LeagueSettings,
  KnockoutSettings,
  GroupsKnockoutSettings,
  LeagueKnockoutSettings,
} from "./formatSpecific"

/**
 * Blank placeholder for Settings page.
 */
export const EventSettings: FC = observer(function EventSettings() {
  const { themed } = useAppTheme()
  const { selectedFormat } = useCreateEventData()

  const renderFormatSpecific = () => {
    switch (selectedFormat as EventFormatKey | null) {
      case "americano":
        return <AmericanoSettings />
      case "mexicano":
        return <MexicanoSettings />
      case "league":
        return <LeagueSettings />
      case "knockout":
        return <KnockoutSettings />
      case "groupsKnockout":
        return <GroupsKnockoutSettings />
      case "leagueKnockout":
        return <LeagueKnockoutSettings />
      default:
        return (
          <Text
            tx="eventSettings:selectFormatPrompt"
            preset="formHelper"
          />
        )
    }
  }

  return (
    <View style={themed($container)}>
      {/* Format-specific settings */}
      {renderFormatSpecific()}
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
  paddingHorizontal: spacing.xxs,
})
