import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle } from "react-native"

// Field components
import {
  NameField,
  LocationField,
  DayField,
  TimeField,
  NumPlayersField,
  NumCourtsField,
  PointsField,
  MixedField,
  TeamField,
  PairField,
} from "../fields"

import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const AmericanoSettings: FC = observer(function AmericanoSettings() {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <NameField />
      <LocationField />
      <DayField />
      {/* Start & End time fields */}
      <View style={themed($timeRow)}>
        <View style={$flexItem}>
          <TimeField field="startTime" labelTx="eventSettings:startTimeLabel" />
        </View>
        <View style={$flexItem}>
          <TimeField field="endTime" labelTx="eventSettings:endTimeLabel" />
        </View>
      </View>
      {/* Players & Courts side-by-side */}
      <View style={themed($fieldRow)}>
        <View style={$flexItem}>
          <NumPlayersField />
        </View>
        <View style={$flexItem}>
          <NumCourtsField />
        </View>
      </View>
      <PointsField />
      <MixedField />
      <TeamField />
      <PairField />
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({}) => ({
  flex: 1,
})

const $fieldRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.md,
})

// Alias for legacy name
const $timeRow = $fieldRow

const $flexItem: ViewStyle = { flex: 1 } 