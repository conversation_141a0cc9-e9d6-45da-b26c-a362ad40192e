import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { BottomSheetScrollView } from "@gorhom/bottom-sheet"

import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { BaseBottomSheet, Text } from "@/components"

interface InfoSheetProps {
  visible: boolean
  onClose: () => void
  title: string
  items: string[]
  snapPoints?: Array<string | number>
}

/**
 * Read-only rule list displayed in Create Event flow.
 */
export const InfoSheet: FC<InfoSheetProps> = observer(function InfoSheet({
  visible,
  onClose,
  title,
  items,
  snapPoints = ["75%"], // bigger default for long content
}) {
  const { themed } = useAppTheme()

  return (
    <BaseBottomSheet
      visible={visible}
      onClose={onClose}
      title={title}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
    >
      <BottomSheetScrollView style={themed($scrollView)} showsVerticalScrollIndicator={false}>
        {items.map((rule, idx) => (
          <View key={idx} style={themed($ruleItem)}>
            <Text text={`${idx + 1}. ${rule}`} size="md" style={themed($ruleText)} />
          </View>
        ))}
      </BottomSheetScrollView>
    </BaseBottomSheet>
  )
})

/* ────────────────────────────────────────────────────────── */
const $scrollView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $ruleItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
})

const $ruleText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
}) 