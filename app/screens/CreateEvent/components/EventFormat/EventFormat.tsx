import { FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TextStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { FormatCard, Text } from "@/components"
import { InfoSheet } from "../InfoSheet"
import { useCreateEventData, EventFormatKey } from "@/screens/CreateEvent/hooks"
import { formatRules } from "./Rules"

/**
 * EventFormat component shows available format options for creating an event.
 */
export const EventFormat: FC = observer(function EventFormat() {
  const { themed, theme } = useAppTheme()
  const { spacing } = theme

  const { selectedFormat: selectedKey, setSelectedFormat: setSelectedKey } = useCreateEventData()

  // State to manage info sheet visibility and content
  const [infoSheetFormat, setInfoSheetFormat] = useState<EventFormatKey | null>(null)

  const isInfoSheetVisible = infoSheetFormat !== null

  // Rules loaded from JSON (via local Rules utility)
  const formatInfo = formatRules

  const handleInfoPress = (format: EventFormatKey) => {
    setInfoSheetFormat(format)
  }

  const closeInfoSheet = () => setInfoSheetFormat(null)

  // Common props for all cards to keep code DRY
  const commonCardProps = {
    size: 120,
    rounded: 12,
  } as const

  return (
    <View style={themed($container)}>
      {/* Info Sheet */}
      {isInfoSheetVisible && (
        <InfoSheet
          visible={isInfoSheetVisible}
          onClose={closeInfoSheet}
          items={formatInfo[infoSheetFormat!].rules}
          title={formatInfo[infoSheetFormat!].title}
        />
      )}
      {/* Social Section */}
      <Text
        preset="subheading"
        weight="bold"
        style={themed($sectionTitle)}
        text="Social"
      />

      <View style={themed($row)}>
        <FormatCard
          {...commonCardProps}
          icon="sports"
          title="Americano"
          selected={selectedKey === "americano"}
          onPress={() => setSelectedKey("americano" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("americano")}
          containerStyle={{ width: "48%" }}
        />
        <FormatCard
          {...commonCardProps}
          icon="trophy"
          title="Mexicano"
          selected={selectedKey === "mexicano"}
          onPress={() => setSelectedKey("mexicano" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("mexicano")}
          containerStyle={{ width: "48%" }}
        />
      </View>

      {/* Tournament Section */}
      <Text
        preset="subheading"
        weight="bold"
        style={themed($sectionTitle)}
        text="Tournament"
      />

      <View style={themed($grid)}>
        <FormatCard
          {...commonCardProps}
          icon="calendar"
          title="League"
          selected={selectedKey === "league"}
          onPress={() => setSelectedKey("league" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("league")}
          containerStyle={{ width: "48%", marginBottom: spacing.md }}
        />
        <FormatCard
          {...commonCardProps}
          icon="clap"
          title="Knockout"
          selected={selectedKey === "knockout"}
          onPress={() => setSelectedKey("knockout" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("knockout")}
          containerStyle={{ width: "48%", marginBottom: spacing.md }}
        />
        <FormatCard
          {...commonCardProps}
          icon="people"
          title="Groups + Knockout"
          selected={selectedKey === "groupsKnockout"}
          onPress={() => setSelectedKey("groupsKnockout" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("groupsKnockout")}
          containerStyle={{ width: "48%", marginBottom: spacing.md }}
        />
        <FormatCard
          {...commonCardProps}
          icon="award"
          title="League + Knockout"
          selected={selectedKey === "leagueKnockout"}
          onPress={() => setSelectedKey("leagueKnockout" as EventFormatKey)}
          onInfoPress={() => handleInfoPress("leagueKnockout")}
          containerStyle={{ width: "48%", marginBottom: spacing.md }}
        />
      </View>
    </View>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingTop: spacing.md,
  paddingHorizontal: spacing.md,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

const $row: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  marginBottom: spacing.lg,
})

const $grid: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  flexWrap: "wrap",
  justifyContent: "space-between",
}) 