import { FC, useState } from "react"
// 2. React Native imports
import {
  View,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from "react-native"
// 3. Third-party imports
import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker"
import { observer } from "mobx-react-lite"
import { Calendar } from "react-native-calendars"
import { format } from "date-fns"

// 4. Local component imports
import { BaseBottomSheet } from "@/components"
import { Text } from "@/components"
// 5. Local utility imports
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

export interface DatePickerSheetProps {
  /** Controls whether the sheet is visible */
  visible: boolean
  /** Called when the sheet is fully dismissed (including swipe-down) */
  onClose: () => void
  /** Called when the user confirms a date */
  onSelect: (date: Date) => void
  /** Currently selected date */
  value: Date
  /** Title shown at the top of the sheet */
  title?: string
  /** Max selectable date */
  maximumDate?: Date
  /** Min selectable date */
  minimumDate?: Date
  /** Picker mode – defaults to "date" */
  mode?: "date" | "time"
  /** Optional custom snap points ("40%", 400, etc.) */
  snapPoints?: Array<string | number>
}

/**
 * Date picker displayed inside a bottom sheet. Mimics the API of `DatePickerModal` but
 * re-uses our `BaseBottomSheet` for consistent styling & behaviour.
 */
export const DatePickerSheet: FC<DatePickerSheetProps> = observer(function DatePickerSheet({
  visible,
  onClose,
  onSelect,
  value,
  title = translate("editProfileScreen:selectDateTitle"),
  maximumDate,
  minimumDate,
  mode = "date",
  snapPoints = ["60%"],
}) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()
  const [selectedDate, setSelectedDate] = useState(value)

  const selectedDateString = format(selectedDate, "yyyy-MM-dd")

  const handleDateChange = (_event: DateTimePickerEvent, date?: Date) => {
    if (date) {
      setSelectedDate(date)
    }
  }

  const handleConfirm = () => {
    onSelect(selectedDate)
    onClose()
  }

  const handleCancel = () => {
    setSelectedDate(value)
    onClose()
  }

  return (
    <BaseBottomSheet
      visible={visible}
      onClose={handleCancel}
      /* Hide default header – we render a custom one with Cancel/Done */
      title={undefined}
      snapPoints={snapPoints}
      enableDynamicSizing={false}
    >
      {/* Header */}
      <View style={themed($header)}>
        <TouchableOpacity onPress={handleCancel}>
          <Text tx="common:cancel" style={themed($cancelText)} size="md" />
        </TouchableOpacity>

        <Text
          text={title}
          style={themed($headerTitle)}
          size="lg"
          weight="semiBold"
        />

        <TouchableOpacity onPress={handleConfirm}>
          <Text tx="common:done" style={themed($doneText)} size="md" weight="medium" />
        </TouchableOpacity>
      </View>

      {/* Date / Time picker */}
      <View style={themed($pickerContainer)}>
        {mode === "date" ? (
          <Calendar
            current={selectedDateString}
            onDayPress={(day) => setSelectedDate(new Date(day.timestamp))}
            markedDates={{
              [selectedDateString]: {
                selected: true,
                selectedColor: colors.palette.primary500,
              },
            }}
            minDate={minimumDate ? format(minimumDate, "yyyy-MM-dd") : undefined}
            maxDate={maximumDate ? format(maximumDate, "yyyy-MM-dd") : undefined}
            style={themed($calendar)}
            theme={{
              calendarBackground: "transparent",
              backgroundColor: "transparent",
              textDayFontWeight: "bold",
              textMonthFontWeight: "bold",
              textDayHeaderFontWeight: "bold",
            }}
          />
        ) : (
          <DateTimePicker
            value={selectedDate}
            mode="time"
            display="spinner"
            onChange={handleDateChange}
            style={themed($picker)}
          />
        )}
      </View>
    </BaseBottomSheet>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $header: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
})

const $headerTitle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

const $cancelText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral500,
})

const $doneText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.secondary500,
})

const $pickerContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingBottom: spacing.xl,
})

const $picker: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "transparent",
})

const $calendar: ThemedStyle<ViewStyle> = () => ({
  alignSelf: "stretch",
  backgroundColor: "transparent",
}) 