import { observer } from "mobx-react-lite"
import React, { FC, useState } from "react"
import { View, Platform, Modal, ScrollView, KeyboardAvoidingView } from "react-native"
import { Screen, Button, Icon, ProgressBar, Text } from "@/components"
import { AppStackScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { STEP_CONFIG, TOTAL_STEPS } from "./utils"
import { translate, TxKeyPath } from "@/i18n"
import {
  $container,
  $buttonContainer,
  $nextButton,
  $nextButtonText,
  $progressContainer,
  $progressLabel,
  $progressBarWrapper,
  NEXT_ICON_COLOR,
  $keyboardContainer,
  $scrollView,
  $scrollContent,
} from "./styles"
import { ModalHeader } from "./shared"
import { EventFormat } from "./components/EventFormat"
import { EventSettings } from "./components/EventSettings"
import { Members } from "./components/Add Player"
import { CreateEventDataProvider } from "./hooks"
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet"

interface CreateEventScreenProps extends AppStackScreenProps<"CreateEvent"> {}

export const CreateEventScreen: FC<CreateEventScreenProps> = observer(function CreateEventScreen({ navigation }) {
  const isIOS = Platform.OS === "ios"
  const [visible, setVisible] = useState(!isIOS)

  const { themed } = useAppTheme()
  const $bottomInset = useSafeAreaInsetsStyle(["bottom"])

  const [step, setStep] = useState<1 | 2 | 3>(1)

  const handleClose = () => {
    if (!isIOS) setVisible(false)
    navigation.goBack()
  }

  const handleNext = () => {
    if (step < TOTAL_STEPS) {
      setStep((prev) => (prev + 1) as 1 | 2 | 3)
    } else {
      // TODO: Integrate CreateEventService here
      handleClose()
    }
  }

  const handleBack = () => {
    if (step > 1) setStep((prev) => (prev - 1) as 1 | 2 | 3)
  }

  const title = translate(STEP_CONFIG[step].titleKey as TxKeyPath)

  // Determine which component to render for each step
  const StepComponent = ((): JSX.Element | null => {
    switch (step) {
      case 1:
        return <EventFormat />
      case 2:
        return <EventSettings />
      case 3:
        return <Members />
      default:
        return null
    }
  })()

  const screenContent = (
    <Screen preset="fixed" safeAreaEdges={[]} contentContainerStyle={themed($container)}>
      {/* Header */}
      <ModalHeader title={title} onClose={handleClose} onBack={step > 1 ? handleBack : undefined} />

      {/* Progress Bar */}
      <View style={themed($progressContainer)}>
        <View style={$progressBarWrapper}>
          <ProgressBar segments={TOTAL_STEPS} filledSegments={step} height={6} />
        </View>
        <Text text={`${step}/${TOTAL_STEPS}`} style={themed($progressLabel)} size="sm" weight="medium" />
      </View>

      {/* Content */}
      <KeyboardAvoidingView
        style={themed($keyboardContainer)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={themed($scrollView)}
          contentContainerStyle={[themed($scrollContent), $bottomInset]}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {StepComponent}
        </ScrollView>

        {/* Bottom Next button */}
        <View style={[themed($buttonContainer), $bottomInset]}>
          <Button
            text={
              step < TOTAL_STEPS
                ? translate("createEventScreen:nextButton" as TxKeyPath)
                : translate("createEventScreen:createButton" as TxKeyPath)
            }
            preset="filled"
            RightAccessory={(props) => (
              <Icon icon="caretRight" size={20} color={NEXT_ICON_COLOR} {...props} />
            )}
            style={themed($nextButton)}
            textStyle={themed($nextButtonText)}
            onPress={handleNext}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  )

  const content = (
    <BottomSheetModalProvider>
      <CreateEventDataProvider>{screenContent}</CreateEventDataProvider>
    </BottomSheetModalProvider>
  )

  if (isIOS) return content

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      {content}
    </Modal>
  )
})
