import { createContext, useContext, useState, ReactNode, FC } from "react"

export type EventFormatKey =
  | "americano"
  | "mexicano"
  | "league"
  | "knockout"
  | "groupsKnockout"
  | "leagueKnockout"

interface CreateEventDataContextValue {
  selectedFormat: EventFormatKey | null
  setSelectedFormat: (key: EventFormatKey) => void
  eventSettings: EventSettings
  setEventSettings: (settings: EventSettings) => void
}

const CreateEventDataContext = createContext<CreateEventDataContextValue | undefined>(
  undefined,
)

export interface EventSettings {
  eventName: string
  location: string
  day: Date | null
  startTime: Date | null
  endTime: Date | null
  numPlayers: string
  numCourts: string
  points: number
  isMixed: boolean
  isTeam: boolean
  pairMethod: "Random" | "First&Last" | "Ladder"
}

export const CreateEventDataProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedFormat, setSelectedFormat] = useState<EventFormatKey | null>(null)
  const [eventSettings, setEventSettings] = useState<EventSettings>({
    eventName: "",
    location: "",
    day: null,
    startTime: null,
    endTime: null,
    numPlayers: "",
    numCourts: "",
    points: 8,
    isMixed: false,
    isTeam: false,
    pairMethod: "Random",
  })

  return (
    <CreateEventDataContext.Provider
      value={{ selectedFormat, setSelectedFormat, eventSettings, setEventSettings }}
    >
      {children}
    </CreateEventDataContext.Provider>
  )
}

export function useCreateEventData() {
  const ctx = useContext(CreateEventDataContext)
  if (!ctx) throw new Error("useCreateEventData must be used inside CreateEventDataProvider")
  return ctx
} 