import { useCallback } from "react"
import { Alert } from "react-native"
import type { UseCreateEventActionsProps, UseCreateEventActionsReturn } from "../types"
import { translate, TxKeyPath } from "@/i18n"

export function useCreateEventActions({
  step,
  setStep,
  onClose,
  navigation,
}: UseCreateEventActionsProps): UseCreateEventActionsReturn {
  /* ------------------------------------------------------------------ */
  // Close / discard flow

  const handleClose = useCallback(() => {
    Alert.alert(
      translate("createEventScreen:discardTitle" as TxKeyPath),
      translate("createEventScreen:discardMessage" as TxKeyPath),
      [
        { text: translate("common:stay" as TxKeyPath), style: "cancel" },
        {
          text: translate("createEventScreen:discardButton" as Tx<PERSON>eyPath),
          style: "destructive",
          onPress: () => {
            if (onClose) onClose()
            else navigation?.goBack()
          },
        },
      ]
    )
  }, [navigation, onClose])

  /* ------------------------------------------------------------------ */
  // Step navigation & placeholder creation

  const handleNext = useCallback(() => {
    // Step 1 → 2
    if (step === 1) return setStep(2)
    // Step 2 → 3
    if (step === 2) return setStep(3)

    // Final step – simply close for now
    handleClose()
  }, [step, setStep, handleClose])

  const handleBack = useCallback(() => {
    if (step > 1) setStep((step - 1) as 1 | 2 | 3)
  }, [step, setStep])

  return {
    handleNext,
    handleBack,
    handleClose,
  }
} 