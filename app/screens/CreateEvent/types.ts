export interface UseCreateEventActionsProps {
  /** Current creation step */
  step: 1 | 2 | 3
  setStep: (step: 1 | 2 | 3) => void
  onClose: () => void
  /** React Navigation object for performing app-wide navigation */
  navigation?: import("@/navigators").AppStackScreenProps<"CreateEvent">["navigation"]
}

export interface UseCreateEventActionsReturn {
  handleNext: () => void
  handleBack: () => void
  handleClose: () => void
} 