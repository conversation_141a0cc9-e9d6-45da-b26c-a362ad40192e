import { ChatMessage, ChatParticipant, ChatConversation } from "./types"

/**
 * Generate fake chat messages for development/demo purposes
 */
export const generateFakeMessages = (participantName: string): ChatMessage[] => {
  return [
    {
      id: 1,
      text: "Hey! 👋 How's your padel game coming along?",
      sender: "other",
      timestamp: "10:30 AM",
      isRead: true,
    },
    {
      id: 2,
      text: "Getting better! Been practicing my backhand volley. You?",
      sender: "self",
      timestamp: "10:32 AM",
      isRead: true,
    },
    {
      id: 3,
      text: "Nice! I've been working on my serve consistency. Want to play this weekend?",
      sender: "other",
      timestamp: "10:35 AM",
      isRead: true,
    },
    {
      id: 4,
      text: "Absolutely! Saturday afternoon works for me 🎾",
      sender: "self",
      timestamp: "10:36 AM",
      isRead: true,
    },
    {
      id: 5,
      text: "Perfect! I know a great court at Elite Padel Club. They have excellent lighting.",
      sender: "other",
      timestamp: "10:40 AM",
      isRead: true,
    },
    {
      id: 6,
      text: "That sounds ideal. What time should we meet? I prefer afternoon sessions.",
      sender: "self",
      timestamp: "10:42 AM",
      isRead: true,
    },
    {
      id: 7,
      text: "How about 2:00 PM? We can warm up together and then play for about 2 hours.",
      sender: "other",
      timestamp: "10:45 AM",
      isRead: true,
    },
    {
      id: 8,
      text: "Sounds perfect! Should I bring extra balls?",
      sender: "self",
      timestamp: "10:46 AM",
      isRead: true,
    },
    {
      id: 9,
      text: "Good thinking! I'll bring some too, just in case. Better to have extras than run out mid-game 😄",
      sender: "other",
      timestamp: "10:50 AM",
      isRead: true,
    },
    {
      id: 10,
      text: "Great planning! What's your current skill level? I'm intermediate, working towards advanced.",
      sender: "self",
      timestamp: "10:52 AM",
      isRead: true,
    },
    {
      id: 11,
      text: "Same here! Been playing for about 2 years now. Really loving the strategy aspect of padel.",
      sender: "other",
      timestamp: "10:55 AM",
      isRead: true,
    },
    {
      id: 12,
      text: "That's what I love about it too! The glass walls add such an interesting dynamic.",
      sender: "self",
      timestamp: "10:56 AM",
      isRead: true,
    },
    {
      id: 13,
      text: "Exactly! Using the walls for strategic shots is what makes padel unique. Can't wait for our match!",
      sender: "other",
      timestamp: "11:00 AM",
      isRead: true,
    },
    {
      id: 14,
      text: "Me too! See you Saturday at 2 PM at Elite Padel Club 👍",
      sender: "self",
      timestamp: "11:01 AM",
      isRead: true,
    },
    {
      id: 15,
      text: "Looking forward to it! Have a great rest of your week! 🎾",
      sender: "other",
      timestamp: "11:02 AM",
      isRead: false,
    },
  ]
}

/**
 * Generate fake participant data
 */
export const generateFakeParticipant = (
  participantId: string,
  participantName: string,
): ChatParticipant => {
  return {
    id: participantId,
    name: participantName,
    isOnline: true,
    lastSeen: new Date().toISOString(),
  }
}

/**
 * Format timestamp for display
 */
export const formatMessageTime = (timestamp: string): string => {
  const now = new Date()
  const messageTime = new Date(timestamp)

  // If timestamp is already in display format (like "9:00 AM"), return as is
  if (timestamp.includes("AM") || timestamp.includes("PM")) {
    return timestamp
  }

  const isToday = now.toDateString() === messageTime.toDateString()

  if (isToday) {
    return messageTime.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  } else {
    return messageTime.toLocaleDateString()
  }
}

/**
 * Generate initials from name for avatar display
 */
export { generateInitials as getInitials } from "@/utils/avatar"

/**
 * Generate a unique message ID
 */
export const generateMessageId = (existingMessages: ChatMessage[]): number => {
  const maxId = Math.max(...existingMessages.map((m) => m.id), 0)
  return maxId + 1
}

/**
 * Create a new message object
 */
export const createMessage = (
  text: string,
  sender: "self" | "other",
  existingMessages: ChatMessage[],
): ChatMessage => {
  return {
    id: generateMessageId(existingMessages),
    text: text.trim(),
    sender,
    timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    isRead: false,
  }
}
