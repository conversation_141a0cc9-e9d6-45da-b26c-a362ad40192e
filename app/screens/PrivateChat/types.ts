export interface ChatMessage {
  id: number
  text: string
  sender: "self" | "other"
  timestamp: string
  isRead?: boolean
}

export interface ChatParticipant {
  id: string
  name: string
  avatar?: string
  isOnline?: boolean
  lastSeen?: string
}

export interface ChatConversation {
  id: string
  participantId: string
  participant: ChatParticipant
  messages: ChatMessage[]
  lastMessage?: ChatMessage
  unreadCount?: number
}

export interface UsePrivateChatDataProps {
  participantId: string
}

export interface UsePrivateChatActionsProps {
  navigation: any
  participantId: string
  onSendMessage: (message: string) => void
}
