import { useCallback } from "react"

interface UsePrivateChatNavigationProps {
  navigation: any
  participantId: string
}

export const usePrivateChatNavigation = ({
  navigation,
  participantId,
}: UsePrivateChatNavigationProps) => {
  const handleBackPress = useCallback(() => {
    navigation.goBack()
  }, [navigation])

  const navigateToProfile = useCallback(() => {
    navigation.navigate("MemberProfile", { userId: participantId })
  }, [navigation, participantId])

  return {
    // Navigation actions
    handleBackPress,
    navigateToProfile,
  }
} 