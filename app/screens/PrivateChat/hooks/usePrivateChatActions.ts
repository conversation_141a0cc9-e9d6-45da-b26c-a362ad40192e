import { useCallback } from "react"
import { UsePrivateChatActionsProps } from "../types"

interface UsePrivateChatActionsReturn {
  // Chat actions
  handleSendMessage: (message: string) => void

  // UI actions
  handleMessageLongPress: (messageId: number) => void
}

export const usePrivateChatActions = ({
  onSendMessage,
}: Omit<UsePrivateChatActionsProps, 'navigation' | 'participantId'>): UsePrivateChatActionsReturn => {

  // Chat actions
  const handleSendMessage = useCallback(
    (message: string) => {
      onSendMessage(message)
    },
    [onSendMessage],
  )

  // UI actions
  const handleMessageLongPress = useCallback((messageId: number) => {
    // For future implementation: message options like copy, delete, etc.
    console.log("Message long pressed:", messageId)
  }, [])

  return {
    // Business actions only
    handleSendMessage,
    handleMessageLongPress,
  }
}
