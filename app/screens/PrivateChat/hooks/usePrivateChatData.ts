import { useState, useEffect } from "react"
import { ChatMessage, ChatParticipant, UsePrivateChatDataProps } from "../types"
import { createMessage } from "../utils"
import userData from "@/data/user_data.json"
import { MessageDetailService } from "@/services/MessageDetailService"

interface UsePrivateChatDataReturn {
  // Data
  messages: ChatMessage[]
  participant: ChatParticipant | null

  // Loading states
  loading: boolean

  // Error states
  error: string | null

  // Actions
  sendMessage: (messageText: string) => void
  markMessagesAsRead: () => void
}

export const usePrivateChatData = ({
  participantId,
}: UsePrivateChatDataProps): UsePrivateChatDataReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [participant, setParticipant] = useState<ChatParticipant | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Initialize chat data
  useEffect(() => {
    const initializeChatData = async () => {
      try {
        console.log("🔄 Initializing chat data for participantId:", participantId)
        setLoading(true)
        setError(null)

        // Try to find the participant in user data first
        let participantName = "Unknown User"
        const userDataTyped = userData as any

        if (userDataTyped.users && userDataTyped.users[participantId]) {
          // Found user in data, use their actual name
          participantName = userDataTyped.users[participantId].profile.name
          console.log("✅ Found user in data:", participantName)
        } else if (participantId.includes("_")) {
          // Convert underscore format back to proper name (e.g., "john_doe" -> "John Doe")
          participantName = participantId
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ")
          console.log("🔄 Converted underscore format:", participantName)
        } else {
          // Try to convert camelCase to proper name (e.g., "johnDoe" -> "John Doe")
          participantName = participantId.replace(/([A-Z])/g, " $1").trim()
          if (participantName) {
            participantName = participantName.charAt(0).toUpperCase() + participantName.slice(1)
          }
          console.log("🔄 Converted camelCase format:", participantName)
        }

        const fetchedMessages = MessageDetailService.getMessagesForConversation(
          `conv_${participantId}`,
          (userData as any).currentUserId,
        )

        const participantObj: ChatParticipant = {
          id: participantId,
          name: participantName,
          avatar: (userData as any).users[participantId]?.profile?.avatar ?? "",
          isOnline: true,
          lastSeen: new Date().toISOString(),
        }

        setParticipant(participantObj)
        setMessages(fetchedMessages)

        console.log("✅ Chat data initialized successfully")
      } catch (err) {
        console.error("❌ Error initializing chat data:", err)
        setError(err instanceof Error ? err.message : "Failed to load chat data")
      } finally {
        setLoading(false)
      }
    }

    if (participantId) {
      initializeChatData()
    } else {
      console.warn("⚠️ No participantId provided")
    }
  }, [participantId])

  // Send a new message
  const sendMessage = (messageText: string) => {
    if (!messageText.trim()) return

    console.log("📤 Sending message:", messageText)
    const newMessage = createMessage(messageText, "self", messages)
    console.log("📤 Created new message:", newMessage)
    setMessages((prevMessages) => [...prevMessages, newMessage])
  }

  // Mark messages as read
  const markMessagesAsRead = () => {
    setMessages((prevMessages) =>
      prevMessages.map((message) => ({
        ...message,
        isRead: true,
      })),
    )
  }

  // Debug current state
  console.log("🔍 Current hook state:", {
    messagesCount: messages.length,
    participantName: participant?.name,
    loading,
    error,
  })

  return {
    // Data
    messages,
    participant,

    // Loading states
    loading,

    // Error states
    error,

    // Actions
    sendMessage,
    markMessagesAsRead,
  }
}
