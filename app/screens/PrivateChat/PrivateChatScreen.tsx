import { FC, useEffect, useState } from "react"
import { View, ViewStyle, TextStyle, StatusBar, Keyboard, Platform } from "react-native"
import { Screen, Text, ChatInputContainer, EmptyState, Icon } from "@/components"
import { useHeader } from "@/utils/useHeader"
import { useAppTheme } from "@/utils/useAppTheme"
import { AppStackScreenProps } from "@/navigators"
import { usePrivateChatData, usePrivateChatActions, usePrivateChatNavigation } from "./hooks"
import { ChatMessagesList } from "./components"
import type { ThemedStyle } from "@/theme"

type PrivateChatScreenProps = AppStackScreenProps<"PrivateChat">

export const PrivateChatScreen: FC<PrivateChatScreenProps> = ({ route, navigation }) => {
  const { participantId, participantName } = route.params
  const {
    themed,
    theme: { colors, spacing },
  } = useAppTheme()

  // Keyboard handling state
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)

  // Data hook - chat data only
  const { messages, participant, loading, error, sendMessage } = usePrivateChatData({
    participantId,
  })

  // Business actions hook - business logic only
  const { handleSendMessage, handleMessageLongPress } = usePrivateChatActions({
    onSendMessage: sendMessage,
  })

  // Navigation hook - navigation actions only
  const { handleBackPress, navigateToProfile } = usePrivateChatNavigation({
    navigation,
    participantId,
  })

  useHeader({
    title: participantName,
    showBackButton: true,
    onBackPress: handleBackPress,
    rightButtons: [
      {
        icon: "more" as const,
        onPress: navigateToProfile,
      },
    ],
  })

  // Keyboard event listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      (e) => {
        setKeyboardHeight(e.endCoordinates.height)
        setIsKeyboardVisible(true)
      },
    )

    const keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      () => {
        setKeyboardHeight(0)
        setIsKeyboardVisible(false)
      },
    )

    return () => {
      keyboardDidShowListener?.remove()
      keyboardDidHideListener?.remove()
    }
  }, [])

  // Loading state
  if (loading) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["bottom"]}
        contentContainerStyle={[
          themed($container),
          isKeyboardVisible && { paddingBottom: spacing.md },
        ]}
      >
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />
        <View style={themed($loadingContainer)}>
          <Text text="Loading chat..." style={themed($loadingText)} />
        </View>
      </Screen>
    )
  }

  // Error state
  if (error) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["bottom"]}
        contentContainerStyle={[
          themed($container),
          isKeyboardVisible && { paddingBottom: spacing.sm },
        ]}
      >
        <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />
        <EmptyState
          heading="Oops! Something went wrong"
          content={error}
          button="Try Again"
          buttonOnPress={() => {}}
        />
      </Screen>
    )
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["bottom"]}
      contentContainerStyle={[
        themed($container),
        isKeyboardVisible && { paddingBottom: spacing.sm },
      ]}
    >
      <StatusBar barStyle="light-content" backgroundColor={colors.palette.neutral800} />

      {/* Chat Messages Area - This should take up the remaining space */}
      <View style={themed($chatContainer)}>
        <ChatMessagesList messages={messages} onMessageLongPress={handleMessageLongPress} />
      </View>

      {/* Chat Input - This should be at the bottom */}
      <ChatInputContainer
        onSendMessage={handleSendMessage}
        placeholder={`Message ${participantName}...`}
      />
    </Screen>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $loadingContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.lg,
})

const $loadingText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $chatContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})
