import { FC } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components/Text"
import { useAppTheme } from "@/utils/useAppTheme"
import { ChatMessage } from "../types"
import { formatMessageTime } from "../utils"
import type { ThemedStyle } from "@/theme"

interface ChatMessageItemProps {
  message: ChatMessage
  onLongPress?: (messageId: number) => void
}

export const ChatMessageItem: FC<ChatMessageItemProps> = ({ message, onLongPress }) => {
  const { themed } = useAppTheme()

  const handleLongPress = () => {
    onLongPress?.(message.id)
  }

  const isOwnMessage = message.sender === "self"

  return (
    <View
      style={[
        themed($messageContainer),
        isOwnMessage ? themed($ownMessageContainer) : themed($otherMessageContainer),
      ]}
    >
      <TouchableOpacity
        onLongPress={handleLongPress}
        style={[
          themed($messageBubble),
          isOwnMessage ? themed($ownMessageBubble) : themed($otherMessageBubble),
        ]}
        activeOpacity={0.8}
      >
        <Text
          text={message.text}
          style={[
            themed($messageText),
            isOwnMessage ? themed($ownMessageText) : themed($otherMessageText),
          ]}
          size="sm"
        />
      </TouchableOpacity>
      <Text text={formatMessageTime(message.timestamp)} style={themed($timestamp)} size="xs" />
    </View>
  )
}

const $messageContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginVertical: spacing.xs,
  maxWidth: "80%",
})

const $ownMessageContainer: ThemedStyle<ViewStyle> = () => ({
  alignSelf: "flex-end",
  alignItems: "flex-end",
})

const $otherMessageContainer: ThemedStyle<ViewStyle> = () => ({
  alignSelf: "flex-start",
  alignItems: "flex-start",
})

const $messageBubble: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  borderRadius: 20,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  maxWidth: "100%",
})

const $ownMessageBubble: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.primary500,
  borderBottomRightRadius: 6,
})

const $otherMessageBubble: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.primary500,
  borderBottomLeftRadius: 6,
})

const $messageText: ThemedStyle<TextStyle> = () => ({
  lineHeight: 20,
})

const $ownMessageText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $otherMessageText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $timestamp: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginTop: spacing.xxs,
  marginHorizontal: spacing.xs,
})
