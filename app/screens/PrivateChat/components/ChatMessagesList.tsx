import { FC, useRef, useEffect } from "react"
import { FlatList, ViewStyle, ListRenderItem } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { ChatMessage } from "../types"
import { ChatMessageItem } from "./ChatMessageItem"
import type { ThemedStyle } from "@/theme"

interface ChatMessagesListProps {
  messages: ChatMessage[]
  onMessageLongPress?: (messageId: number) => void
}

export const ChatMessagesList: FC<ChatMessagesListProps> = ({ messages, onMessageLongPress }) => {
  const { themed } = useAppTheme()
  const flatListRef = useRef<FlatList>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true })
      }, 100)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [messages])

  const renderMessage: ListRenderItem<ChatMessage> = ({ item }) => {
    return <ChatMessageItem message={item} onLongPress={onMessageLongPress} />
  }

  const keyExtractor = (item: ChatMessage) => item.id.toString()

  return (
    <FlatList
      ref={flatListRef}
      data={messages}
      renderItem={renderMessage}
      keyExtractor={keyExtractor}
      style={themed($messagesList)}
      contentContainerStyle={themed($messagesContent)}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
      maintainVisibleContentPosition={{
        minIndexForVisible: 0,
        autoscrollToTopThreshold: 10,
      }}
    />
  )
}

const $messagesList: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $messagesContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  flexGrow: 1,
})
