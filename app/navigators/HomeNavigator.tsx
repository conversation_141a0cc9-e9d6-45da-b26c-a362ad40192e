/**
 * This file previously contained the home tab navigator but has been refactored.
 * All screens are now directly in the AppNavigator stack.
 *
 * This file is kept for backwards compatibility with any remaining references.
 */

// Legacy type exports (keeping for any remaining references)
export type HomeTabParamList = {
  Main: undefined
  Home: undefined
  Communities: undefined
  DemoDebug: undefined
  Profile: undefined
}

/**
 * @deprecated - Use AppStackScreenProps from AppNavigator instead
 */
export type HomeTabScreenProps<T extends keyof HomeTabParamList> = {
  navigation: any
  route: any
}

/**
 * @deprecated - All screens are now in AppNavigator.tsx
 * This component is no longer used but kept for backwards compatibility.
 */
export function HomeNavigator() {
  console.warn("HomeNavigator is deprecated. All screens are now in AppNavigator.")
  return null
}
