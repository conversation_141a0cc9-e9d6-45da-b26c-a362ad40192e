import "react-native-url-polyfill/auto"
import { createClient, SupabaseClient } from "@supabase/supabase-js"

// -----------------------------------------------------------------------------
// Environment Variables (Expo .env or process.env)
// -----------------------------------------------------------------------------
// These should be defined in your .env or app.config.* files.
//
// EXPO_PUBLIC_SUPABASE_URL          – e.g. https://xyzcompany.supabase.co
// EXPO_PUBLIC_SUPABASE_ANON_KEY     – your Supabase anon/public key
// -----------------------------------------------------------------------------

const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL ?? ""
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ?? ""

// Fail fast in development so we know env vars are missing
if (__DEV__) {
  if (!SUPABASE_URL) console.warn("[Supabase] EXPO_PUBLIC_SUPABASE_URL is not set.")
  if (!SUPABASE_ANON_KEY) console.warn("[Supabase] EXPO_PUBLIC_SUPABASE_ANON_KEY is not set.")
}

/**
 * Shared Supabase client used throughout the app.
 *
 * Import with:
 * ```ts
 * import { supabase } from "@/supabase"
 * ```
 */
export const supabase: SupabaseClient = createClient(
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  },
)
