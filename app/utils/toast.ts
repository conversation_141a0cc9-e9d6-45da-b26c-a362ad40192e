import { Toast } from "toastify-react-native"

/**
 * Global toast helpers used across the app. Ensure `ToastManager` is mounted in
 * `app/app.tsx` before calling any of these functions.
 * All toasts are displayed at the top position to avoid keyboard overlap.
 */

export const showSuccessToast = (
  message: string,
  description?: string,
) => {
  Toast.success(message, "top")
}

export const showErrorToast = (
  message: string,
  description?: string,
) => {
  Toast.error(message, "top")
}

export const showInfoToast = (
  message: string,
  description?: string,
) => {
  Toast.info(message, "top")
}

export const showWarningToast = (
  message: string,
  description?: string,
) => {
  Toast.warn(message, "top")
}

// Generic show, for advanced use cases
export const showToast = Toast.show 