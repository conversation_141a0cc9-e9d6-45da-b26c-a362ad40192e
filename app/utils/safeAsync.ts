/**
 * Utility for safely executing async operations with consistent error handling
 */

export interface SafeAsyncResult<T> {
  success: boolean
  data?: T
  error?: Error
}

export interface SafeAsyncOptions {
  /** Custom error handler function */
  onError?: (error: Error) => void
  /** Whether to log errors to console (default: true) */
  logErrors?: boolean
  /** Default value to return on error */
  defaultValue?: any
}

/**
 * Safely executes an async function with consistent error handling
 *
 * @param asyncFn - The async function to execute
 * @param options - Configuration options for error handling
 * @returns Promise<SafeAsyncResult<T>> - Result object with success/data/error
 *
 * @example
 * ```typescript
 * const result = await safeAsync(async () => {
 *   return await fetch('/api/data')
 * })
 *
 * if (result.success) {
 *   console.log(result.data)
 * } else {
 *   console.error(result.error)
 * }
 * ```
 */
export async function safeAsync<T>(
  asyncFn: () => Promise<T>,
  options: SafeAsyncOptions = {},
): Promise<SafeAsyncResult<T>> {
  const { onError, logErrors = true, defaultValue } = options

  try {
    const data = await asyncFn()
    return {
      success: true,
      data,
    }
  } catch (error) {
    const safeError = error instanceof Error ? error : new Error(String(error))

    // Log error if enabled
    if (logErrors) {
      console.error("SafeAsync error:", safeError.message, safeError)
    }

    // Call custom error handler if provided
    if (onError) {
      onError(safeError)
    }

    return {
      success: false,
      error: safeError,
      data: defaultValue,
    }
  }
}

/**
 * Simplified version that returns the data directly or undefined on error
 * Useful when you don't need detailed error handling
 *
 * @param asyncFn - The async function to execute
 * @param defaultValue - Value to return on error (default: undefined)
 * @returns Promise<T | undefined> - The result data or default value
 */
export async function safeAsyncSimple<T>(
  asyncFn: () => Promise<T>,
  defaultValue?: T,
): Promise<T | undefined> {
  const result = await safeAsync(asyncFn, { logErrors: true, defaultValue })
  return result.data
}

/**
 * Version that throws a user-friendly error instead of returning error in result
 * Useful for operations where you want to handle errors with user notifications
 *
 * @param asyncFn - The async function to execute
 * @param userMessage - User-friendly error message
 * @returns Promise<T> - The result data or throws user-friendly error
 */
export async function safeAsyncThrow<T>(
  asyncFn: () => Promise<T>,
  userMessage: string = "Something went wrong. Please try again.",
): Promise<T> {
  const result = await safeAsync(asyncFn, { logErrors: true })

  if (result.success) {
    return result.data!
  } else {
    throw new Error(userMessage)
  }
}
