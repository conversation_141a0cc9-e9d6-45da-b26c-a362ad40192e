/**
 * Avatar utility functions for consistent avatar handling across the app
 */

/**
 * Generates initials from a full name
 * Handles edge cases like empty strings, null values, and multi-word names
 * @param name - Full name string
 * @returns Initials string (max 2 characters, uppercase)
 */
export const generateInitials = (name: string): string => {
  // Handle null, undefined, or empty string cases
  if (!name || name.trim() === "") {
    return "??"
  }

  return name
    .trim()
    .split(" ")
    .filter((word) => word.length > 0) // Filter out empty strings from multiple spaces
    .map((word) => word.charAt(0))
    .join("")
    .substring(0, 2)
    .toUpperCase()
}

/**
 * Avatar size presets for consistent sizing across the app
 */
export const AVATAR_SIZES = {
  xs: 24,
  sm: 32,
  md: 40,
  lg: 52,
  xl: 64,
} as const

export type AvatarSize = keyof typeof AVATAR_SIZES

/**
 * Get avatar dimensions for a given size preset
 * @param size - Avatar size preset
 * @returns Object with width, height, and borderRadius
 */
export const getAvatarDimensions = (size: AvatarSize | number) => {
  const dimension = typeof size === "number" ? size : AVATAR_SIZES[size]

  return {
    width: dimension,
    height: dimension,
    borderRadius: dimension / 2,
  }
}

/**
 * Default avatar colors for consistent theming
 */
export const AVATAR_COLORS = {
  primary: "#6366f1",
  secondary: "#8b5cf6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
  neutral: "#6b7280",
} as const

export type AvatarColor = keyof typeof AVATAR_COLORS
