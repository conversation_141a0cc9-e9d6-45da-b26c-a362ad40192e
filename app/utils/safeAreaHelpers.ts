import { Edge, useSafeAreaInsets } from "react-native-safe-area-context"
import { Platform, StatusBar } from "react-native"

/**
 * Safe area utilities for consistent handling across devices
 */

export type SafeAreaEdge = Edge | "start" | "end"

/**
 * Common safe area edge configurations for different screen types
 */
export const SAFE_AREA_CONFIGS = {
  // Full screen with all edges protected
  FULL_SCREEN: ["top", "bottom", "left", "right"] as SafeAreaEdge[],

  // Screen with tab navigation (exclude bottom for tab bar)
  WITH_TABS: ["top", "left", "right"] as SafeAreaEdge[],

  // Modal screens (usually need all edges)
  MODAL: ["top", "bottom", "left", "right"] as SafeAreaEdge[],

  // Header only (for screens where content can extend to bottom)
  HEADER_ONLY: ["top", "left", "right"] as SafeAreaEdge[],

  // Content only (for screens with custom headers)
  CONTENT_ONLY: ["bottom", "left", "right"] as SafeAreaEdge[],

  // No safe area (use sparingly, only when manually handling)
  NONE: [] as SafeAreaEdge[],
} as const

/**
 * Hook to get safe area insets with additional helpers
 */
export function useEnhancedSafeArea() {
  const insets = useSafeAreaInsets()

  return {
    ...insets,
    // Helper to check if device has notch/dynamic island
    hasNotch: insets.top > 20,

    // Helper to check if device has home indicator
    hasHomeIndicator: insets.bottom > 0,

    // Status bar height helper
    statusBarHeight: Platform.OS === "ios" ? insets.top : StatusBar.currentHeight || 0,

    // Common padding values
    headerPaddingTop: insets.top,
    contentPaddingBottom: insets.bottom,

    // Minimum safe values (for older devices)
    minSafeTop: Math.max(insets.top, Platform.OS === "ios" ? 20 : StatusBar.currentHeight || 0),
    minSafeBottom: Math.max(insets.bottom, 0),
  }
}

/**
 * Helper to determine appropriate safe area config based on screen type
 */
export function getSafeAreaConfig(screenType: keyof typeof SAFE_AREA_CONFIGS): SafeAreaEdge[] {
  return SAFE_AREA_CONFIGS[screenType]
}

/**
 * Hook for dynamic safe area styling
 */
export function useDynamicSafeAreaStyle() {
  const safeArea = useEnhancedSafeArea()

  return {
    // Common container styles
    screenContainer: {
      paddingTop: safeArea.top,
      paddingBottom: safeArea.bottom,
      paddingLeft: safeArea.left,
      paddingRight: safeArea.right,
    },

    // Header container (with top safe area)
    headerContainer: {
      paddingTop: safeArea.top,
      paddingLeft: safeArea.left,
      paddingRight: safeArea.right,
    },

    // Content container (with bottom safe area)
    contentContainer: {
      paddingBottom: safeArea.bottom,
      paddingLeft: safeArea.left,
      paddingRight: safeArea.right,
    },

    // Tab bar container
    tabBarContainer: {
      paddingBottom: Math.max(safeArea.bottom - 8, 4), // Reduce bottom padding by 8px, minimum 4px
      height: 62 + Math.max(safeArea.bottom - 8, 4), // Reduce base height and adjusted bottom padding
    },
  }
}
