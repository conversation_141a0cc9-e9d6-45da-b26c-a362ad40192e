const en = {
  common: {
    ok: "OK!",
    cancel: "Cancel",
    back: "Back",
    goBack: "Go back",
    logOut: "Log Out",
    checkOut: "Check out",
    save: "Save",
    done: "Done",
    stay: "Stay",
  },
  // Keep the "demo" sections for now as they are used in DemoDebugScreen, will clean up later if needed
  demoDebugScreen: {
    howTo: "HOW TO",
    title: "Debug",
    tagLine:
      "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!",
    reactotron: "Send to Reactotron",
    reportBugs: "Report Bugs",
    demoList: "Demo List",
    demoPodcastList: "Demo Podcast List",
    currentSystemTheme: "Current system theme: {{theme}}",
    currentAppTheme: "Current app theme: {{theme}}",
    reset: "Reset",
    toggleTheme: "Toggle Theme: {{theme}}",
    appId: "App Id",
    appName: "App Name",
    appVersion: "App Version",
    appBuildVersion: "App Build Version",
    hermesEnabled: "Hermes Enabled",
    fabricEnabled: "Fabric Enabled",
    androidReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.",
    iosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    macosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    webReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    windowsReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
  },
  loginScreen: {
    logIn: "Log In",
    enterDetails:
      "Enter your details below.",
    emailFieldLabel: "Email",
    passwordFieldLabel: "Password",
    emailFieldPlaceholder: "Enter your email address",
    passwordFieldPlaceholder: "Super secret password here",
    tapToLogIn: "Tap to log in!",
    hint: "Hint: you can use any email address and your favorite password :)",
  },
  changePasswordScreen: {
    title: "Change password",
    description: "Enter the password you want to set for your account.",
    passwordPlaceholder: "Password",
    ruleMin8Chars: "Minimum 8 characters",
    ruleMinDigit: "Minimum 1 digit",
    ruleMinUppercase: "Minimum 1 uppercase letter",
    changeButton: "Change password",
    passwordUpdated: "Password updated",
    passwordInvalid: "Your password doesn't meet the criteria.",
  },
  communitiesScreen: {
    searchCommunitiesPlaceholder: "Search communities...",
    noResultsFoundHeading: "No Results Found",
    noResultsFoundContent: "No communities found for \"{{query}}\". Try searching with different keywords.",
    noCommunitiesAvailableHeading: "No Communities Available",
    noCommunitiesAvailableContent: "Check back later for new communities or create your own!",
    title: "Communities",
  },
  profileScreen: {
    title: "Profile",
  },
  inboxScreen: {
    title: "Inbox",
  },
  eventDetailScreen: {
    title: "Event Details",
    loadingEvent: "Loading event...",
    eventNotFound: "Event not found",
    eventError: "Error loading event",
    errorHeading: "Something went wrong",
    eventDetailsTitle: "Event Details",
    dateTimeLabel: "Date & Time",
    durationLabel: "Duration",
    locationLabel: "Location",
    priceLabel: "Price",
    participantsSectionTitle: "Participants",
    participantsCount: "{{current}}/{{max}} participants",
    organizerSectionTitle: "Organizer",
    aboutEventTitle: "About This Event",
    locationSectionTitle: "Location",
    eventRulesTitle: "Event Rules",
    organizedBy: "Organized by {{organizer}}",
  },
  communityDetailScreen: {
    loadingCommunity: "Loading community...",
    communityNotFound: "Community not found",
    communityError: "Error loading community",
    errorHeading: "Something went wrong",
  },
  communityDataHook: {
    communityNotFound: "Community not found",
    failedToLoadCommunity: "Failed to load community",
  },
  chatTab: {
    deleteMessageTitle: "Delete Message",
    deleteMessageContent: "Are you sure you want to delete this message?",
    cancelButton: "Cancel",
    deleteButton: "Delete",
    typeYourMessagePlaceholder: "Type your message...",
  },
  eventsTab: {
    upcomingEventsTitle: "Upcoming Events",
    noUpcomingEvents: "No upcoming events",
  },
  communityAbout: {
    aboutCommunityTitle: "About Community",
    noDescriptionAvailable: "No description available",
  },
  communityAdmins: {
    communityAdminsTitle: "Community Admins & Moderators",
    unknownAdmin: "Unknown Admin",
    adminRole: "Admin",
  },
  communityRules: {
    communityRulesTitle: "Community Rules",
  },
  communityStats: {
    communityStatsTitle: "Community Stats",
    totalMembers: "Total Members",
    eventsThisMonth: "Events This Month",
    gamesPlayed: "Games Played",
    communityRating: "Community Rating",
  },
  homeTab: {
    loadingCommunityData: "Loading community data...",
  },
  membersTab: {
    searchMembersPlaceholder: "Search members...",
  },
  communityHeader: {
    membersCount: "{{count}} members",
  },
  communityTabs: {
    homeTabTitle: "Home",
    eventsTabTitle: "Events",
    chatTabTitle: "Chat",
    membersTabTitle: "Members",
  },
  communityNavigationHook: {
    memberNotFoundForID: "Member not found for ID:{{memberId}}",
  },
  communityUtils: {
    memberSingular: "member",
    memberPlural: "members",
    upcomingEventsText: "{{count}} upcoming events",
    joinPadelCommunity: "Join our padel community!",
    shareTitle: "{{communityName}} - Padel Community",
    joinUsForMatches: "Join us for some amazing padel matches!",
  },
  createCommunityScreen: {
    viewButton: "View",
    nextButton: "Next",
    editCommunityTitle: "Edit Community",
  },
  communityAvatar: {
    selectCommunityAvatar: "Select community avatar",
    changeCommunityAvatar: "Change community avatar",
    changeCommunityPicture: "Change community picture",
  },
  detailsScreen: {
    changeCommunityPicturePrompt: "Change Community Picture",
  },
  detailsAbout: {
    communityDetailsTitle: "Community Details",
    communityNameLabel: "Community Name",
    communityNamePlaceholder: "Enter community name",
    shortDescriptionLabel: "Short Description",
    shortDescriptionPlaceholder: "Write a short description",
    descriptionLabel: "About Community",
    descriptionPlaceholder: "Describe your community",
    locationLabel: "Location",
    locationPlaceholder: "Where do you play?",
    selectLocationTitle: "Select Location",
    searchPlaceholder: "Search location...",
    rulesLabel: "Rules",
    rulesPlaceholder: "Write the rules for the community",
  },
  membersScreen: {
    membersTitle: "Members",
  },
  basicSettings: {
    communityType: "Community Type",
    public: "Public",
    private: "Private",
    maxUsersLimit: "Max Users Limit",
    enterUserLimit: "Enter user limit",
    valuePlaceholder: "Value",
    moderation: "Moderation",
    enableChat: "Enable Chat",
    yes: "Yes",
    no: "No",
  },
  settingsScreen: {
    moderatorsTitle: "Moderators",
  },
  // Edit Profile Screen
  editProfileScreen: {
    editProfileTitle: "Edit Profile",
    personalInformationTitle: "Personal Information",
    nameAndSurnameLabel: "Name and Surname",
    enterYourNamePlaceholder: "Enter your name",
    emailLabel: "Email",
    enterYourEmailPlaceholder: "Enter your email",
    phoneLabel: "Phone",
    phonePlaceholder: "Phone number",
    genderLabel: "Gender",
    dateOfBirthLabel: "Date of Birth",
    descriptionLabel: "Description",
    descriptionPlaceholder: "Right side, right-handed",
    locationLabel: "Where do you play?",
    locationPlaceholder: "Enter your playing location",
    changeProfilePicture: "Change profile picture",
    selectCountryTitle: "Select Country",
    selectGenderTitle: "Select Gender",
    selectDateTitle: "Select Date",
    characterCount: "{{count}} characters",
  },
  editProfileErrors: {
    emailRequired: "Email is required",
    emailInvalid: "Please enter a valid email address",
    nameRequired: "Name is required",
    nameTooShort: "Name must be at least 2 characters",
    phoneRequired: "Phone number is required",
    phoneInvalid: "Please enter a valid phone number",
    descriptionTooLong: "Description must be less than 160 characters",
    locationRequired: "Location is required",
    minimumAge: "You must be at least 13 years old",
    dateOfBirthInvalid: "Please enter a valid date of birth",
  },
  eventsScreen: {
    title: "Events",
  },
  eventsCard: {
    fullLabel: "Full",
    completedLabel: "Completed",
    cancelledLabel: "Cancelled",
    joinLabel: "Join",
    resultsLabel: "Results",
    unavailableLabel: "Unavailable",
  },
  inboxTabs: {
    messagesTabTitle: "Messages",
    invitesTabTitle: "Invites",
  },
  inboxActions: {
    markRead: "Mark Read",
    markUnread: "Mark Unread",
    delete: "Delete",
  },
  
  // Event Settings translations
  eventSettings: {
    selectFormatPrompt: "Please select a format in the previous step to configure its settings.",
    eventNameLabel: "Event Name",
    eventNamePlaceholder: "Enter event name",
    locationLabel: "Location",
    locationPlaceholder: "Which padel facility?",
    dayLabel: "Day",
    selectDayPrompt: "Select Day",
    startTimeLabel: "Start Time",
    endTimeLabel: "End Time",
    selectTimePrompt: "Select Time",
    numPlayersLabel: "Number of Players",
    numPlayersPlaceholder: "e.g. 16",
    numCourtsLabel: "Number of Courts",
    numCourtsPlaceholder: "e.g. 4",
    numberOfPointsLabel: "Number of Points",
    mexicanoSettingsPlaceholder: "Mexicano format specific settings will be displayed here.",
    leagueSettingsPlaceholder: "League format specific settings will be displayed here.",
    knockoutSettingsPlaceholder: "Knockout format specific settings will be displayed here.",
    groupsKnockoutSettingsPlaceholder: "Groups + Knockout format specific settings will be displayed here.",
    leagueKnockoutSettingsPlaceholder: "League + Knockout format specific settings will be displayed here.",
  },

  createEventScreen: {
    step1Title: "Create Event",
    step2Title: "Event Settings",
    step3Title: "Add Players",
    nextButton: "Next",
    createButton: "Create Event",
    discardTitle: "Discard Event Creation?",
    discardMessage: "Are you sure you want to exit? Your progress will be lost.",
    discardButton: "Discard",
  },
}

export default en
export type Translations = typeof en
