export interface CommunityData {
  id: string
  name: string
  description: string
  short_description: string
  memberCount: number
  backgroundImage: string
  isPrivate: boolean
  chatEnabled?: boolean
  location?: string
  isJoined: boolean
  stats: {
    totalMembers: number
    eventsThisMonth: number
    gamesPlayed: number
    communityRating: number
  }
  address: string
  adminsAndModerators: Array<{
    id: number
    name: string
    role: string
    initials: string
    bgColor: string
    status: string
  }>
  achievements: Array<{
    title: string
    description: string
    icon: string
    bgColor: string
  }>
  rules: string[]
  upcomingEvents: Array<{
    id: number
    type: "Tournament" | "Social" | "Americano" | "Mexicano" | "League"
    title: string
    date: string
    day: string
    location: string
    price: string
    participants: number
    maxParticipants: number
    status: "joinable" | "full" | "completed"
  }>
  pastEvents: Array<{
    id: number
    type: "Tournament" | "Social" | "Americano" | "Mexicano" | "League"
    title: string
    date: string
    day: string
    location: string
    price: string
    participants: number
    maxParticipants: number
    status: "joinable" | "full" | "completed"
    winner?: string
  }>
  members: Array<{
    id: number
    name: string
    role: "Moderator" | "Admin" | "Member"
    isFollowing: boolean
  }>
  chatMessages: Array<{
    id: number
    author: string
    content: string
    timestamp: string
    type: "message" | "event"
    eventId?: number
    eventType?: string
    eventDetails?: {
      date: string
      participants: string
      price: string
      location: string
    }
  }>
}
