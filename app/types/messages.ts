// Preview-level data used in Inbox list
export interface ConversationPreview {
  id: string
  participants: string[]
  lastSenderId: string
  preview: string
  timestamp: string
  unread: boolean
}

// Detailed message stored in messages_detail.json
export interface MessageDetail {
  message_id: string
  conversation_id: string
  sender_id: string
  receiver_id: string
  content: string
  timestamp: string
}

export interface ConversationSummary {
  id: string
  otherUserId: string
  otherUserName: string
  otherUserAvatar: string
  lastMessageText: string
  lastMessageTimestamp: string
  unread: boolean
} 