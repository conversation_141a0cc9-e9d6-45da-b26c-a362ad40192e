export enum EventType {
  AMERICANO = "Americano",
  MEXICANO = "Mexicano",
  TOURNAMENT = "Tournament",
  LEAGUE = "League",
  SOCIAL = "Social",
}

export enum EventStatus {
  JOINABLE = "joinable",
  FULL = "full",
  COMPLETED = "completed",
  FINISHED = "finished",
  CANCELLED = "cancelled",
}

export interface Event {
  id: number
  type: EventType | string
  title: string
  date: string
  day: string
  location: string
  price: string
  participants: number
  maxParticipants: number
  status: EventStatus | string
}

export interface EventFilters {
  selectedLocations: string[]
  selectedTypes: string[]
}

export interface EventCardProps {
  event: Event
  onJoin?: (event: Event) => void
  onPress?: (event: Event) => void
}

export interface EventsListProps {
  events: Event[]
  onJoinEvent: (event: Event) => void
  onEventPress?: (event: Event) => void
}
