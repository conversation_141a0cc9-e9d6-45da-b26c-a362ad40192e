export interface Notification {
  id: string
  type: "follow" | "invite" | "join" | "game-post"
  user: {
    name: string
    username: string
    avatar?: string
    initials: string
  }
  content: {
    title: string
    communityName?: string
    eventName?: string
  }
  timestamp: string
  isFollowing?: boolean
  isRead: boolean
}

export interface NotificationGroup {
  "Last 7 days": Notification[]
  "Last 30 days": Notification[]
  "Older": Notification[]
}
