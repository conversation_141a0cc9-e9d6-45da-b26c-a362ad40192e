import { EventType } from "@/types/events"

/**
 * StatusBadgeService - Handles all status badge configuration and styling logic
 * Separates business logic from UI components for better maintainability
 */
export class StatusBadgeService {
  /**
   * Get badge color based on variant and status
   */
  static getBadgeColor(
    status: string, 
    variant: 'type' | 'status' | 'role' | 'indicator',
    colors: any
  ): string {
    switch (variant) {
      case 'type':
        return this.getEventTypeColor(status, colors)
      
      case 'status':
        return this.getStatusColor(status, colors)
      
      case 'role':
        return this.getRoleColor(status, colors)
      
      case 'indicator':
        return this.getIndicatorColor(status, colors)
      
      default:
        return colors.palette.neutral500
    }
  }

  /**
   * Get color for event types
   */
  private static getEventTypeColor(status: string, colors: any): string {
    switch (status) {
      case EventType.AMERICANO:
        return colors.palette.secondary500
      case EventType.MEXICANO:
        return colors.palette.accent500
      case EventType.TOURNAMENT:
        return colors.palette.primary500
      case EventType.LEAGUE:
        return colors.palette.angry500
      case EventType.SOCIAL:
        return colors.palette.accent400
      default:
        return colors.palette.neutral500
    }
  }

  /**
   * Get color for general statuses
   */
  private static getStatusColor(status: string, colors: any): string {
    switch (status.toLowerCase()) {
      case 'joinable':
      case 'join':
      case 'joined':
      case 'active':
        return colors.palette.success500
      case 'full':
      case 'inactive':
        return colors.palette.neutral400
      case 'completed':
      case 'finished':
        return colors.palette.neutral600
      case 'cancelled':
      case 'canceled':
      case 'closed':
        return colors.palette.angry500
      case 'request':
      case 'pending':
        return colors.palette.accent500
      default:
        return colors.palette.neutral500
    }
  }

  /**
   * Get color for user roles
   */
  private static getRoleColor(status: string, colors: any): string {
    switch (status.toLowerCase()) {
      case 'admin':
      case 'administrator':
        return colors.palette.primary500
      case 'moderator':
      case 'mod':
        return colors.palette.secondary500
      case 'member':
      case 'user':
        return colors.palette.neutral400
      default:
        return colors.palette.neutral500
    }
  }

  /**
   * Get color for indicators
   */
  private static getIndicatorColor(status: string, colors: any): string {
    switch (status.toLowerCase()) {
      case 'online':
      case 'active':
        return colors.palette.success500
      case 'offline':
      case 'inactive':
        return colors.palette.neutral400
      case 'away':
      case 'busy':
        return colors.palette.accent500
      default:
        return colors.palette.neutral400
    }
  }

  /**
   * Get size configuration for badge
   */
  static getSizeConfig(size: 'xs' | 'sm' | 'md', spacing: any): {
    containerStyle: Record<string, any>;
    textStyle: Record<string, any>;
  } {
    switch (size) {
      case 'xs':
        return {
          containerStyle: {
            paddingHorizontal: spacing.xs,
            paddingVertical: spacing.xxs,
            borderRadius: spacing.xxs,
            minHeight: 16,
          },
          textStyle: {
            fontSize: 10,
            lineHeight: 12,
          },
        }
      case 'sm':
        return {
          containerStyle: {
            paddingHorizontal: spacing.sm,
            paddingVertical: spacing.xs,
            borderRadius: spacing.xs,
            minHeight: 20,
          },
          textStyle: {
            fontSize: 11,
            lineHeight: 14,
          },
        }
      case 'md':
        return {
          containerStyle: {
            paddingHorizontal: spacing.md,
            paddingVertical: spacing.xs,
            borderRadius: spacing.sm,
            minHeight: 24,
          },
          textStyle: {
            fontSize: 12,
            lineHeight: 16,
          },
        }
      default:
        return this.getSizeConfig('sm', spacing)
    }
  }

  /**
   * Get complete badge configuration
   */
  static getBadgeConfiguration(
    status: string,
    variant: 'type' | 'status' | 'role' | 'indicator',
    size: 'xs' | 'sm' | 'md',
    theme: any
  ) {
    const { colors, spacing } = theme
    const backgroundColor = this.getBadgeColor(status, variant, colors)
    const sizeConfig = this.getSizeConfig(size, spacing)

    return {
      backgroundColor,
      containerStyle: {
        ...sizeConfig.containerStyle,
        backgroundColor,
      },
      textStyle: {
        ...sizeConfig.textStyle,
        color: colors.palette.neutral100, // Always white text
        fontWeight: '600',
        textAlign: 'center' as const,
      },
    }
  }

  /**
   * Validate badge variant and status combination
   */
  static validateBadgeConfig(
    status: string,
    variant: 'type' | 'status' | 'role' | 'indicator'
  ): { isValid: boolean; reason?: string } {
    if (!status || status.trim() === '') {
      return { isValid: false, reason: 'Status cannot be empty' }
    }

    // Could add more specific validations based on variant
    switch (variant) {
      case 'type':
        if (!Object.values(EventType).includes(status as EventType)) {
          // Allow non-enum values but warn
          console.warn(`Status '${status}' is not a recognized EventType`)
        }
        break
    }

    return { isValid: true }
  }

  /**
   * Get status display text (can be different from internal status)
   */
  static getDisplayText(status: string, variant: 'type' | 'status' | 'role' | 'indicator'): string {
    // For most cases, return as-is, but this allows for custom display text
    switch (variant) {
      case 'status':
        switch (status.toLowerCase()) {
          case 'joinable':
            return 'Available'
          case 'canceled':
            return 'Cancelled' // Normalize spelling
          default:
            return status
        }
      default:
        return status
    }
  }
} 