import { supabase } from "@/supabase"
import * as Linking from "expo-linking"

/**
 * A thin wrapper around Supabase Auth helpers. Keeps all authentication-related
 * calls in one place so they can be mocked or replaced easily during tests.
 */
export class SupabaseAuthService {
  /**
   * Sign the user in with an email / password combination.
   *
   * Returns the Supabase session & user on success or throws on error.
   */
  static async signInWithEmailPassword(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({ email, password })

    if (error) throw error
    return data
  }

  /**
   * Create a new user using email / password credentials.
   *
   * Returns the Supabase session & user on success or throws on error.
   */
  static async signUpWithEmailPassword(email: string, password: string, displayName: string) {
    const redirectTo = process.env.EXPO_PUBLIC_SUPABASE_EMAIL_REDIRECT ?? undefined

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
        data: displayName ? { display_name: displayName } : undefined,
      },
    })

    if (error) throw error
    return data
  }

  /**
   * Completely sign the currently authenticated user out.
   */
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    if (error) throw error
  }

  /**
   * Request an email-based one-time password (OTP).
   * If the user was just created, make sure `shouldCreateUser` is `false` so Supabase will
   * treat this as a verification code rather than trying to create a new user again.
   */
  static async requestEmailOtp(email: string, shouldCreateUser = false) {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: { shouldCreateUser },
    })
    if (error) throw error
  }

  /**
   * Verify the OTP sent to the user's email address.
   * Returns the new session on success.
   */
  static async verifyEmailOtp(email: string, token: string) {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: "email",
    })
    if (error) throw error
    return data
  }

  /**
   * Update currently authenticated user's password. User must be signed in.
   */
  static async updateUserPassword(newPassword: string) {
    const { data, error } = await supabase.auth.updateUser({ password: newPassword })
    if (error) throw error
    return data
  }

  /**
   * Update user profile/user_metadata values.
   */
  static async updateUserProfile(data: Record<string, any>) {
    const { data: result, error } = await supabase.auth.updateUser({ data })
    if (error) throw error
    return result
  }

  /**
   * Request a password reset email for the given address.
   * The user will receive an email with a link to reset their password.
   */
  static async resetPasswordForEmail(email: string) {
    // Build a deep-link that will take the user directly to the in-app
    // screen where they can set a new password.
    // `createURL("PasswordResetConfirm")` generates "padelcommunityapp://PasswordResetConfirm"
    // (two slashes instead of three). Supabase will append the auth fragment after verification.
    const redirectTo = Linking.createURL("PasswordResetConfirm", { scheme: "padelcommunityapp" })

    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo,
    })

    if (error) throw error
    return data
  }
} 