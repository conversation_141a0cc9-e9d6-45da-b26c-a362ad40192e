import { CommunityRepository } from "../repositories/community-repository";
import { CommunityData } from "@/types";

export class CommunityRepositorySupabase implements CommunityRepository {
  getAll(): Promise<CommunityData[]> {
    throw new Error("Not implemented");
  }

  getById(id: string): Promise<CommunityData | undefined> {
    throw new Error("Not implemented");
  }

  save(entity: CommunityData): Promise<void> {
    throw new Error("Not implemented");
  }

  delete(id: string): Promise<void> {
    throw new Error("Not implemented");
  }
}
