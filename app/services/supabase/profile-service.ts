import { supabase } from "@/supabase"
import type { UserProfile } from "@/screens/EditProfile/types"
import type { Trophy, UserCommunity } from "@/screens/Profile/types"

export class SupabaseProfileService {
  /**
   * Fetch the authenticated user's profile data from public.users.
   */
  static async fetchCurrentUserProfile(): Promise<UserProfile | null> {
    // Ensure we have an authenticated user session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (__DEV__) {
      console.log("[ProfileService] current session user:", user)
    }
    if (!user) throw new Error("No authenticated user found.")

    const { data, error } = await supabase
      .from("users")
      .select(
        `display_name, email, country_code, phone, gender, dob, description, location`
      )
      .eq("user_id", user.id)
      .maybeSingle()

    if (error) throw error

    if (__DEV__) {
      console.log("[ProfileService] fetched user profile row:", data)
    }

    if (!data) return null

    return {
      name: data.display_name ?? "",
      email: data.email ?? "",
      countryCode: data.country_code ?? "",
      phone: data.phone ?? "",
      gender: data.gender ?? "",
      dateOfBirth: data.dob ? new Date(data.dob) : new Date(),
      description: data.description ?? "",
      location: data.location ?? "",
      // avatar intentionally omitted; generated on-device
    }
  }

  /**
   * Update the authenticated user's profile row.
   */
  static async updateCurrentUserProfile(profile: UserProfile): Promise<void> {
    // Ensure authenticated
    const {
      data: { session: updateSession },
      error: sessionError2,
    } = await supabase.auth.getSession()
    if (sessionError2) throw sessionError2

    const user = updateSession?.user
    if (!user) throw new Error("No authenticated user found.")

    const { error } = await supabase
      .from("users")
      .update({
        display_name: profile.name,
        email: profile.email,
        country_code: profile.countryCode,
        phone: profile.phone,
        gender: profile.gender,
        dob: profile.dateOfBirth.toISOString().split("T")[0], // YYYY-MM-DD
        description: profile.description,
        location: profile.location,
        last_updated: new Date().toISOString(),
      })
      .eq("user_id", user.id)

    if (error) throw error
  }

  /**
   * Fetch the authenticated user's statistics from public.user_stats.
   * Returns the stats object or null if not found.
   */
  static async fetchStats(): Promise<{
    totalMatches: number
    socialWins: number
    tournamentWins: number
  } | null> {
    // Ensure authenticated
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (!user) throw new Error("No authenticated user found.")

    const { data, error } = await supabase
      .from("user_stats")
      .select("total_matches, social_wins, tournament_wins")
      .eq("user_id", user.id)
      .maybeSingle()

    if (error) throw error

    if (__DEV__) {
      console.log("[ProfileService] fetched user stats row:", data)
    }

    if (!data) return null

    return {
      totalMatches: data.total_matches ?? 0,
      socialWins: data.social_wins ?? 0,
      tournamentWins: data.tournament_wins ?? 0,
    }
  }

  /**
   * Fetch the authenticated user's trophies from public.user_trophies.
   * Returns an array of trophies or an empty array if not found.
   */
  static async fetchTrophies(): Promise<Trophy[]> {
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (!user) throw new Error("No authenticated user found.")

    const { data, error } = await supabase
      .from("user_trophies")
      .select("id, type, description, created_at")
      .eq("user_id", user.id)

    if (error) throw error

    if (__DEV__) {
      console.log("[ProfileService] fetched user trophies rows:", data)
    }

    if (!data) return []

    return data.map((trophy) => ({
      id: trophy.id,
      title: trophy.type,
      description: trophy.description,
      date: new Date(trophy.created_at).toLocaleDateString(),
      icon: "🏆", // Default icon
    }))
  }

  /**
   * Fetch the authenticated user's communities.
   * Returns an array of UserCommunity or an empty array if not found.
   * This version joins with the `community` table to get the name.
   */
  static async fetchUserCommunities(): Promise<UserCommunity[]> {
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (!user) throw new Error("No authenticated user found.")

    // This query now joins community_members with community to get the name.
    const { data, error } = await supabase
      .from("community_members")
      .select(
        `
        role,
        community (
          community_id,
          name
        )
      `,
      )
      .eq("user_id", user.id)
      .eq("status", "active")
      .limit(10)

    if (error) {
      console.error("[ProfileService] Error fetching user communities with join:", error)
      throw error
    }

    if (__DEV__) {
      console.log("[ProfileService] fetched user communities with join:", data)
    }

    if (!data) return []

    return data
      .map((item) => {
        if (!item.community) return null

        const communityData = Array.isArray(item.community) ? item.community[0] : item.community
        if (!communityData) return null

        return {
          id: communityData.community_id,
          name: communityData.name,
          members: 0, // Placeholder for now
          role: item.role,
          color: "bg-blue-500", // Back to original color
        }
      })
      .filter((c): c is UserCommunity => c !== null)
  }
}
