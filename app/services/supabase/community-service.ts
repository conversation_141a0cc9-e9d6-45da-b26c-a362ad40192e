import { supabase } from "@/supabase"

/**
 * Data transfer object used when creating a Community row.
 * Only includes fields that can be set from the mobile app.
 */
export interface CreateCommunityDto {
  name: string
  shortDescription: string
  description: string
  rules: string[]
  /** Whether the community is private (mapped to `visibility` column) */
  isPrivate: boolean
  /** Maximum number of users allowed. Pass `null` for unlimited */
  maxUsers: number | null
  /** Whether chat is enabled for members */
  chatEnabled: boolean
  /** Whether post moderation is enabled */
  moderationEnabled: boolean
  /** Optional textual location description */
  location?: string | null
}

/**
 * SupabaseCommunityService – thin wrapper around `community` Postgres table.
 * Keeps all network calls in one place so they can be mocked or replaced easily.
 */
export class SupabaseCommunityService {
  /**
   * Insert a new community row, assigning the current authenticated user as admin.
   * Returns the newly created community_id.
   */
  static async createCommunity(data: CreateCommunityDto): Promise<string> {
    // Ensure we have an authenticated user session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) throw sessionError
    const user = session?.user
    if (!user) throw new Error("No authenticated user found.")

    const payload = {
      name: data.name,
      short_desc: data.shortDescription,
      long_desc: data.description,
      visibility: data.isPrivate ? "Private" : "Public",
      max_users: data.maxUsers,
      chat_enabled: data.chatEnabled,
      moderation: data.moderationEnabled,
      admin_id: user.id,
      location: data.location ?? null,
      rules: data.rules ?? [],
    }

    const { data: insertData, error } = await supabase
      .from("community")
      .insert(payload)
      .select("community_id")
      .maybeSingle()

    if (error) throw error
    if (!insertData) throw new Error("Community creation failed. No data returned.")

    return insertData.community_id as string
  }

  // ---------------------------------------------------------------------------
  // Placeholders for future CRUD operations

  /** Fetch a single community row by ID */
  static async fetchCommunityById(id: string) {
    throw new Error("fetchCommunityById not implemented yet")
  }

  /** Update an existing community row */
  static async updateCommunity(id: string, partial: Partial<CreateCommunityDto>) {
    throw new Error("updateCommunity not implemented yet")
  }

  /** Delete a community row */
  static async deleteCommunity(id: string) {
    throw new Error("deleteCommunity not implemented yet")
  }
} 