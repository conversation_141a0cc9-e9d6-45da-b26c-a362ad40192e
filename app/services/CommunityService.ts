import { CommunityData } from "@/types/communities"

/**
 * CommunityService - Handles all community-related business logic
 * Separates business logic from UI components for better maintainability
 */
export class CommunityService {
  /**
   * Check if user has already joined the community
   */
  static isUserJoined(community: CommunityData): boolean {
    return community.isJoined
  }

  /**
   * Check if community is private (requires request to join)
   */
  static isPrivate(community: CommunityData): boolean {
    return community.isPrivate
  }

  /**
   * Check if user can directly join the community
   */
  static canDirectJoin(community: CommunityData): boolean {
    return !this.isUserJoined(community) && !this.isPrivate(community)
  }

  /**
   * Check if user needs to request to join
   */
  static needsJoinRequest(community: CommunityData): boolean {
    return !this.isUserJoined(community) && this.isPrivate(community)
  }

  /**
   * Get the appropriate action type for the community
   */
  static getCommunityActionType(community: CommunityData): 'joined' | 'request' | 'join' {
    if (this.isUserJoined(community)) {
      return 'joined'
    } else if (this.isPrivate(community)) {
      return 'request'
    } else {
      return 'join'
    }
  }

  /**
   * Get join button configuration based on community state
   */
  static getJoinButtonConfig(community: CommunityData) {
    const actionType = this.getCommunityActionType(community)

    switch (actionType) {
      case 'joined':
        return {
          text: "Joined",
          disabled: true,
          variant: "joined" as const,
          action: null,
        }
      case 'request':
        return {
          text: "Request",
          disabled: false,
          variant: "request" as const,
          action: "request" as const,
        }
      case 'join':
        return {
          text: "Join",
          disabled: false,
          variant: "join" as const,
          action: "join" as const,
        }
      default:
        return {
          text: "Unavailable",
          disabled: true,
          variant: "disabled" as const,
          action: null,
        }
    }
  }

  /**
   * Get button style configuration based on variant
   */
  static getButtonStyleConfig(variant: 'joined' | 'request' | 'join' | 'disabled') {
    const styleMap = {
      joined: {
        backgroundColor: "#9ca3af", // neutral-400
        color: "#f9fafb", // neutral-100
      },
      request: {
        backgroundColor: "#f97316", // orange-500
        color: "#f9fafb", // neutral-100
      },
      join: {
        backgroundColor: "#22c55e", // green-500
        color: "#f9fafb", // neutral-100
      },
      disabled: {
        backgroundColor: "#9ca3af", // neutral-400
        color: "#f9fafb", // neutral-100
      },
    }

    return styleMap[variant] || styleMap.disabled
  }

  /**
   * Format member count for display
   */
  static formatMemberCount(community: CommunityData): string {
    const count = community.memberCount || community.stats?.totalMembers || 0
    
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`
    }
    
    return count.toString()
  }

  /**
   * Format member count text for simple display (e.g., "5 members")
   */
  static formatMemberCountText(memberCount: number): string {
    if (memberCount >= 1000) {
      return `${(memberCount / 1000).toFixed(1)}k members`
    }
    
    return `${memberCount} ${memberCount === 1 ? 'member' : 'members'}`
  }

  /**
   * Get formatted address for display
   */
  static formatAddress(community: CommunityData): string {
    return community.address || "Location not specified"
  }

  /**
   * Validate if action can be performed on community
   */
  static validateCommunityAction(
    community: CommunityData, 
    action: 'join' | 'request'
  ): { canPerform: boolean; reason?: string } {
    if (this.isUserJoined(community)) {
      return { canPerform: false, reason: "Already joined this community" }
    }

    if (action === 'join' && this.isPrivate(community)) {
      return { canPerform: false, reason: "Private community requires request" }
    }

    if (action === 'request' && !this.isPrivate(community)) {
      return { canPerform: false, reason: "Public community allows direct join" }
    }

    return { canPerform: true }
  }

  /**
   * Get community statistics summary
   */
  static getCommunityStats(community: CommunityData) {
    return {
      memberCount: this.formatMemberCount(community),
      eventsThisMonth: community.stats?.eventsThisMonth || 0,
      gamesPlayed: community.stats?.gamesPlayed || 0,
      rating: community.stats?.communityRating || 0,
    }
  }

  /**
   * Check if community has active events
   */
  static hasActiveEvents(community: CommunityData): boolean {
    return (community.upcomingEvents?.length || 0) > 0
  }

  /**
   * Get community access level description
   */
  static getAccessLevelDescription(community: CommunityData): string {
    if (this.isPrivate(community)) {
      return "Private community - Request required"
    }
    return "Public community - Open to join"
  }

  /**
   * Get community icon style configuration
   */
  static getCommunityIconStyle(communityColor?: string) {
    return {
      backgroundColor: communityColor || "#10b981", // Default green
      iconColor: "#ffffff",
    }
  }

  /**
   * Get role display configuration
   */
  static getRoleDisplayConfig(role: "Member" | "Admin") {
    return {
      displayText: role,
      variant: "role" as const,
      priority: role === "Admin" ? 1 : 2, // For sorting if needed
    }
  }

  /**
   * Mock implementation for creating a community.
   * In the future this will hit the backend API.
   */
  static async createCommunity(data: any): Promise<void> {
    // TODO: Replace with real API integration
    await new Promise((resolve) => setTimeout(resolve, 1000))
    // Log for debugging purposes
    // eslint-disable-next-line no-console
    console.log("[CommunityService] Community created (mock):", {
      id: `COMM_${Date.now()}`,
      ...data,
    })
  }
} 