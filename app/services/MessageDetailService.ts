import messagesDetailData from "@/data/messages_detail.json"
import userData from "@/data/user_data.json"
import { MessageDetail } from "@/types/messages"
import { ChatMessage } from "@/screens/PrivateChat/types"

export class MessageDetailService {
  static messages: MessageDetail[] = (messagesDetailData as any).messages

  static getMessagesForConversation = (
    conversationId: string,
    currentUserId: string,
  ): ChatMessage[] => {
    const msgs = this.messages
      .filter((m) => m.conversation_id === conversationId)
      .sort(
        (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      )

    return msgs.map((m) => ({
      id: parseInt(m.message_id.replace(/[^0-9]/g, ""), 10),
      text: m.content,
      sender: m.sender_id === currentUserId ? "self" : "other",
      timestamp: m.timestamp,
      isRead: true,
    }))
  }
} 