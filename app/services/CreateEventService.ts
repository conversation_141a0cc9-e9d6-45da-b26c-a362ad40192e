export interface CreateEventPayload {
  name: string
  description?: string
  // TODO: add other event fields
}

export class CreateEventService {
  /**
   * Placeholder method to create an event. Replace with real implementation when API is ready.
   */
  static async createEvent(payload: CreateEventPayload) {
    // eslint-disable-next-line no-console
    console.log("[CreateEventService] createEvent called with:", payload)
    // Simulate success response
    return { success: true }
  }
} 