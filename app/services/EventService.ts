import { Event, EventStatus, EventType } from "@/types/events"
import { translate } from "@/i18n"

/**
 * EventService - Handles all event-related business logic
 * Separates business logic from UI components for better maintainability
 */
export class EventService {
  /**
   * Check if an event can be joined by a user
   */
  static canJoinEvent(event: Event): boolean {
    return event.status === EventStatus.JOINABLE && event.participants < event.maxParticipants
  }

  /**
   * Check if an event is full
   */
  static isEventFull(event: Event): boolean {
    return event.status === EventStatus.FULL || event.participants >= event.maxParticipants
  }

  /**
   * Check if an event is completed
   */
  static isEventCompleted(event: Event): boolean {
    return event.status === EventStatus.COMPLETED || event.status === EventStatus.FINISHED
  }

  /**
   * Check if an event is finished (new separate status)
   */
  static isEventFinished(event: Event): boolean {
    return event.status === EventStatus.FINISHED
  }

  /**
   * Check if an event is cancelled
   */
  static isEventCancelled(event: Event): boolean {
    return event.status === EventStatus.CANCELLED
  }

  /**
   * Get join button configuration based on event status
   */
  static getJoinButtonConfig(event: Event) {
    if (this.isEventFull(event)) {
      return {
        text: translate("eventsCard:fullLabel"),
        disabled: true,
        variant: "disabled" as const,
      }
    }

    if (this.isEventFinished(event)) {
      return {
        text: translate("eventsCard:resultsLabel"),
        disabled: true,
        variant: "disabled" as const,
      }
    }

    if (this.isEventCompleted(event)) {
      return {
        text: translate("eventsCard:completedLabel"),
        disabled: true,
        variant: "disabled" as const,
      }
    }

    if (this.isEventCancelled(event)) {
      return {
        text: translate("eventsCard:cancelledLabel"),
        disabled: true,
        variant: "disabled" as const,
      }
    }

    if (this.canJoinEvent(event)) {
      return {
        text: translate("eventsCard:joinLabel"),
        disabled: false,
        variant: "enabled" as const,
      }
    }

    return {
      text: translate("eventsCard:unavailableLabel"),
      disabled: true,
      variant: "disabled" as const,
    }
  }

  /**
   * Get event type display configuration
   */
  static getEventTypeConfig(eventType: EventType | string) {
    const typeMap = {
      [EventType.AMERICANO]: { color: "success", label: "Americano" },
      [EventType.MEXICANO]: { color: "warning", label: "Mexicano" },
      [EventType.TOURNAMENT]: { color: "error", label: "Tournament" },
      [EventType.LEAGUE]: { color: "info", label: "League" },
      [EventType.SOCIAL]: { color: "neutral", label: "Social" },
    }

    return typeMap[eventType as EventType] || { color: "neutral", label: eventType }
  }

  /**
   * Format participants display text
   */
  static formatParticipantsText(event: Event): string {
    return `${event.participants}/${event.maxParticipants}`
  }

  /**
   * Format price display text
   */
  static formatPriceText(event: Event): string {
    return event.price
  }

  /**
   * Validate if join action should be performed
   */
  static validateJoinAction(event: Event): { canJoin: boolean; reason?: string } {
    if (this.isEventFull(event)) {
      return { canJoin: false, reason: "Event is full" }
    }

    if (this.isEventCompleted(event)) {
      return { canJoin: false, reason: "Event is completed" }
    }

    if (this.isEventFinished(event)) {
      return { canJoin: false, reason: "Event is finished" }
    }

    if (this.isEventCancelled(event)) {
      return { canJoin: false, reason: "Event is cancelled" }
    }

    if (!this.canJoinEvent(event)) {
      return { canJoin: false, reason: "Event is not available for joining" }
    }

    return { canJoin: true }
  }
} 