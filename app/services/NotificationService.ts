import { Notification } from "@/types/notifications"

type ButtonPreset = "default" | "filled" | "reversed"

/**
 * NotificationService - Handles all notification-related business logic
 * Separates business logic from UI components for better maintainability
 */
export class NotificationService {
  /**
   * Get notification text configuration based on notification type
   */
  static getNotificationTextConfig(notification: Notification) {
    const { type, user, content } = notification

    switch (type) {
      case "follow":
        return {
          segments: [
            { text: user.name, style: "clickable" },
            { text: ` ${content.title}`, style: "normal" },
          ],
          hasHighlight: false,
          highlightText: null,
        }

      case "invite":
        return {
          segments: [
            { text: user.name, style: "clickable" },
            { text: ` ${content.title} `, style: "normal" },
            { text: content.eventName || "", style: "highlight" },
          ],
          hasHighlight: true,
          highlightText: content.eventName,
        }

      case "join":
        return {
          segments: [
            { text: user.name, style: "clickable" },
            { text: ` ${content.title} `, style: "normal" },
            { text: content.eventName || "", style: "highlight" },
          ],
          hasHighlight: true,
          highlightText: content.eventName,
        }

      case "game-post":
        return {
          segments: [
            { text: user.name, style: "clickable" },
            { text: ` ${content.title} `, style: "normal" },
            { text: content.communityName || "", style: "highlight" },
          ],
          hasHighlight: true,
          highlightText: content.communityName,
        }

      default:
        return {
          segments: [{ text: content.title, style: "normal" }],
          hasHighlight: false,
          highlightText: null,
        }
    }
  }

  /**
   * Get action button configuration based on notification type
   */
  static getActionButtonConfig(notification: Notification) {
    switch (notification.type) {
      case "follow":
        return {
          text: notification.isFollowing ? "Following" : "Follow",
          preset: (notification.isFollowing ? "default" : "filled") as ButtonPreset,
          action: "follow" as const,
          disabled: false,
        }

      case "invite":
        return {
          text: "Join",
          preset: "filled" as ButtonPreset,
          action: "join" as const,
          disabled: false,
        }

      case "join":
        return {
          text: "View",
          preset: "default" as ButtonPreset,
          action: "view" as const,
          disabled: false,
        }

      case "game-post":
        return {
          text: "View",
          preset: "default" as ButtonPreset,
          action: "view" as const,
          disabled: false,
        }

      default:
        return null
    }
  }

  /**
   * Check if notification has an action button
   */
  static hasActionButton(notification: Notification): boolean {
    return this.getActionButtonConfig(notification) !== null
  }

  /**
   * Get clickable elements in notification text
   */
  static getClickableElements(notification: Notification) {
    const textConfig = this.getNotificationTextConfig(notification)
    
    return {
      userName: notification.user.name,
      userUsername: notification.user.username,
      highlightText: textConfig.highlightText,
      hasUserClick: true,
      hasHighlightClick: textConfig.hasHighlight,
    }
  }

  /**
   * Validate notification action
   */
  static validateNotificationAction(
    notification: Notification,
    action: "follow" | "join" | "view"
  ): { canPerform: boolean; reason?: string } {
    const buttonConfig = this.getActionButtonConfig(notification)
    
    if (!buttonConfig) {
      return { canPerform: false, reason: "No action available for this notification type" }
    }

    if (buttonConfig.action !== action) {
      return { canPerform: false, reason: `Invalid action '${action}' for notification type '${notification.type}'` }
    }

    if (buttonConfig.disabled) {
      return { canPerform: false, reason: "Action is currently disabled" }
    }

    return { canPerform: true }
  }
} 