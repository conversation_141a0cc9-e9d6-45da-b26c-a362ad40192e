import eventResultsData from "@/data/event_result.json"
import type { Game } from "@/screens/EventResult/types"

export interface LeaderboardRow {
  player: string
  wins: number
  ties: number
  losses: number
  diff: number
  points: number
}

interface RoundData {
  round: number
  games: Game[]
}

interface ResultsSchema {
  rounds: RoundData[]
}

const results = eventResultsData as unknown as ResultsSchema

/**
 * Calculates leaderboard statistics for all players across all rounds.
 */
export class LeaderboardService {
  /**
   * Generate leaderboard rows sorted by Points (desc), Diff (desc)
   */
  static calculateLeaderboard(): LeaderboardRow[] {
    const players: Record<string, LeaderboardRow> = {}

    // Iterate through all games in all rounds
    results.rounds.forEach((round) => {
      round.games.forEach((game) => {
        const [teamA, teamB] = game.teams

        // Helper to process a single team against its opponent
        const processTeam = (
          team: (typeof game.teams)[0],
          opponent: (typeof game.teams)[0],
        ) => {
          team.players.forEach((player) => {
            const stats: LeaderboardRow =
              players[player] || {
                player,
                wins: 0,
                ties: 0,
                losses: 0,
                diff: 0,
                points: 0,
              }

            // Add points scored in this game
            stats.points += team.score
            // Add diff (team score minus opponent score)
            stats.diff += team.score - opponent.score

            // Determine game outcome
            if (team.score > opponent.score) {
              stats.wins += 1
            } else if (team.score === opponent.score) {
              stats.ties += 1
            } else {
              stats.losses += 1
            }

            players[player] = stats
          })
        }

        processTeam(teamA, teamB)
        processTeam(teamB, teamA)
      })
    })

    // Convert to array and sort
    return Object.values(players).sort((a, b) => {
      if (b.points !== a.points) return b.points - a.points
      return b.diff - a.diff
    })
  }
} 