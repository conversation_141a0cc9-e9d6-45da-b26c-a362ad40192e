import { CommunityData } from "@/types/communities"
import { generateInitials } from "@/utils/avatar"
import type { CreateCommunityData } from "@/screens/CreateCommunity/hooks/useCreateCommunityData"

/**
 * CommunityEditService
 * --------------------
 * Central place to translate between persistent CommunityData and the
 * in-memory CreateCommunity form representation (CreateCommunityData).
 */
export class CommunityEditService {
  /**
   * Convert a full CommunityData object -> CreateCommunityData (prefill form)
   */
  static toFormData(community: CommunityData): Partial<CreateCommunityData> {
    if (!community) return {}

    // Combine members & moderators for user selection lists (remove duplicates)
    const mergedMembers = [
      ...(community.members ?? []),
      ...(community.adminsAndModerators ?? []),
    ]

    const unique = new Map<string, { id: string; name: string; initials?: string }>()
    mergedMembers.forEach((p: any) => {
      const idStr = p.id?.toString() ?? generateInitials(p.name)
      if (!unique.has(idStr)) {
        unique.set(idStr, {
          id: idStr,
          name: p.name,
          initials: p.initials ?? generateInitials(p.name),
        })
      }
    })

    return {
      details: {
        name: community.name,
        shortDescription: community.short_description,
        description: community.description,
        location: community.location ?? community.address ?? "",
        rules: community.rules ?? [],
        // avatar could be added when asset available
      },
      settings: {
        communityType: community.isPrivate ? "Private" : "Public",
        chatEnabled: community.chatEnabled ?? true,
        limitEnabled: false,
        userLimit: "",
        moderationEnabled: (community.adminsAndModerators?.length ?? 0) > 0,
      },
      members: Array.from(unique.values()),
      moderators: (community.adminsAndModerators ?? []).map((mod) => ({
        id: mod.id.toString(),
        name: mod.name,
        initials: mod.initials ?? generateInitials(mod.name),
      })),
    }
  }

  /**
   * Convert CreateCommunityData back to CommunityData partial (for update API)
   * Implementation stubbed for future use.
   */
  static fromFormData(_form: CreateCommunityData): Partial<CommunityData> {
    // TODO: implement when backend update API is ready
    return {}
  }
} 