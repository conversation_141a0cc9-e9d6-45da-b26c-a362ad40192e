/**
 * CarouselService - Business logic for carousel buttons
 */

export interface CarouselButton {
  id: string
  title: string
  subtitle: string
  icon: string
  backgroundColor: string
  onPress: () => void
}

export class CarouselService {
  /**
   * Get carousel button configurations for explore section
   */
  static getCarouselButtons(actions: {
    onExploreCommunities: () => void
    onExploreEvents: () => void
    onExploreFeed: () => void
  }): CarouselButton[] {
    return [
      {
        id: "explore-communities",
        title: "Explore",
        subtitle: "Communities",
        icon: "community",
        backgroundColor: "#4a9d8e", // Muted metallic teal
        onPress: actions.onExploreCommunities,
      },
      {
        id: "explore-events",
        title: "Explore",
        subtitle: "Upcoming Events",
        icon: "heart", // Using heart as calendar alternative
        backgroundColor: "#6366f1", // Muted metallic indigo
        onPress: actions.onExploreEvents,
      },
      {
        id: "explore-feed",
        title: "Explore",
        subtitle: "Feed",
        icon: "feed",
        backgroundColor: "#e04f6b", // red
        onPress: actions.onExploreFeed,
      },
    ]
  }
} 