/**
 * Service for SectionCarousel data processing and business logic
 */
export class SectionCarouselService {
  /**
   * Chunk array into groups of specified size
   */
  static chunkData<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * Calculate optimal item width based on container width and items per row
   */
  static calculateItemWidth(containerWidth: number, itemsPerRow: number, spacing: number): number {
    const totalSpacing = (itemsPerRow - 1) * spacing
    return (containerWidth - totalSpacing) / itemsPerRow
  }

  /**
   * Validate carousel configuration
   */
  static validateConfig(itemsPerRow: number, itemWidth?: number): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (itemsPerRow < 1) {
      errors.push("itemsPerRow must be at least 1")
    }

    if (itemWidth !== undefined && itemWidth <= 0) {
      errors.push("itemWidth must be greater than 0")
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
} 