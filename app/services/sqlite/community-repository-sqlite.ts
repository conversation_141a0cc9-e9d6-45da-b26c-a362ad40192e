import { CommunityRepository } from "../repositories/community-repository";
import { CommunityData } from "@/types";
import { db } from "./sqlite-client";
import { drizzle } from 'drizzle-orm/expo-sqlite';
import { communities } from '@/services/sqlite/schema';
import { eq } from "drizzle-orm";

const drizzleDb = drizzle(db);

export class CommunityRepositorySqlite implements CommunityRepository {
  private parseJSON(jsonString: string | null, defaultValue: any = []) {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString);
    } catch (e) {
      return defaultValue;
    }
  }

  async getAll(): Promise<CommunityData[]> {
    const result = await drizzleDb.select().from(communities).all();
    return result.map(c => ({
      ...c,
      memberCount: c.memberCount ?? 0,
      isPrivate: c.isPrivate ?? false,
      isJoined: c.isJoined ?? false,
      description: c.description ?? '',
      short_description: c.short_description ?? '',
      backgroundImage: c.backgroundImage ?? '',
      address: c.address ?? '',
      stats: this.parseJSON(c.stats, {}),
      adminsAndModerators: this.parseJSON(c.adminsAndModerators, []),
      achievements: this.parseJSON(c.achievements, []),
      rules: this.parseJSON(c.rules, []),
      upcomingEvents: this.parseJSON(c.upcomingEvents, []),
      pastEvents: this.parseJSON(c.pastEvents, []),
      members: this.parseJSON(c.members, []),
      chatMessages: this.parseJSON(c.chatMessages, []),
    }));
  }

  async getById(id: string): Promise<CommunityData | undefined> {
    const result = await drizzleDb.select().from(communities).where(eq(communities.id, id)).get();
    if (!result) return undefined;
    return {
      ...result,
      memberCount: result.memberCount ?? 0,
      isPrivate: result.isPrivate ?? false,
      isJoined: result.isJoined ?? false,
      description: result.description ?? '',
      short_description: result.short_description ?? '',
      backgroundImage: result.backgroundImage ?? '',
      address: result.address ?? '',
      stats: this.parseJSON(result.stats, {}),
      adminsAndModerators: this.parseJSON(result.adminsAndModerators, []),
      achievements: this.parseJSON(result.achievements, []),
      rules: this.parseJSON(result.rules, []),
      upcomingEvents: this.parseJSON(result.upcomingEvents, []),
      pastEvents: this.parseJSON(result.pastEvents, []),
      members: this.parseJSON(result.members, []),
      chatMessages: this.parseJSON(result.chatMessages, []),
    };
  }

  async save(entity: CommunityData): Promise<void> {
    const entityToSave = {
      ...entity,
      stats: JSON.stringify(entity.stats),
      adminsAndModerators: JSON.stringify(entity.adminsAndModerators),
      achievements: JSON.stringify(entity.achievements),
      rules: JSON.stringify(entity.rules),
      upcomingEvents: JSON.stringify(entity.upcomingEvents),
      pastEvents: JSON.stringify(entity.pastEvents),
      members: JSON.stringify(entity.members),
      chatMessages: JSON.stringify(entity.chatMessages),
    };
    await drizzleDb.insert(communities).values(entityToSave).onConflictDoUpdate({ target: communities.id, set: entityToSave });
  }

  async delete(id: string): Promise<void> {
    await drizzleDb.delete(communities).where(eq(communities.id, id));
  }
}
