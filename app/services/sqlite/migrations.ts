import { db } from "./sqlite-client";

export const runMigrations = () => {
  console.log("Running migrations...");
  try {
    db.execSync(
      `CREATE TABLE IF NOT EXISTS communities (
        id TEXT PRIMARY KEY NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        short_description TEXT,
        memberCount INTEGER,
        backgroundImage TEXT,
        isPrivate BOOLEAN,
        isJoined BOOLEAN,
        stats TEXT,
        address TEXT,
        adminsAndModerators TEXT,
        achievements TEXT,
        rules TEXT,
        upcomingEvents TEXT,
        pastEvents TEXT,
        members TEXT,
        chatMessages TEXT
      );`
    );
    console.log("Communities table migration successful.");
  } catch (error) {
    console.error("Migration failed:", error);
  }
};
