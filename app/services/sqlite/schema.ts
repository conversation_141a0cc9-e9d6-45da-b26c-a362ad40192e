import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';

export const communities = sqliteTable('communities', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  short_description: text('short_description'),
  memberCount: integer('memberCount'),
  backgroundImage: text('backgroundImage'),
  isPrivate: integer('isPrivate', { mode: 'boolean' }),
  isJoined: integer('isJoined', { mode: 'boolean' }),
  stats: text('stats'),
  address: text('address'),
  adminsAndModerators: text('adminsAndModerators'),
  achievements: text('achievements'),
  rules: text('rules'),
  upcomingEvents: text('upcomingEvents'),
  pastEvents: text('pastEvents'),
  members: text('members'),
  chatMessages: text('chatMessages'),
});

export const users = sqliteTable('users', {
  user_id: text('user_id').primary<PERSON>ey(),
  name: text('name'),
  surname: text('surname'),
  email: text('email').unique().notNull(),
  country_code: text('country_code'),
  phone: text('phone'),
  gender: text('gender'),
  dob: text('dob'), // Storing date as text for simplicity
  description: text('description'),
  location: text('location'),
  trophies: integer('trophies'),
  created_at: text('created_at'),
  last_updated: text('last_updated'),
});
