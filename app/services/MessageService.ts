import messagesData from "@/data/messages.json"
import userData from "@/data/user_data.json"
import { ConversationPreview, ConversationSummary } from "@/types/messages"

export class MessageService {
  static conversations: ConversationPreview[] = (messagesData as any).conversations

  static getConversationSummaries(currentUserId: string): ConversationSummary[] {
    return this.conversations
      .map((conv) => {
        const otherUserId = conv.participants.find((id) => id !== currentUserId) || ""
        const otherUser = (userData as any).users[otherUserId]

        return {
          id: conv.id,
          otherUserId,
          otherUserName: otherUser?.profile?.name ?? "Unknown",
          otherUserAvatar: otherUser?.profile?.avatar ?? "",
          lastMessageText: conv.preview,
          lastMessageTimestamp: conv.timestamp,
          unread: conv.unread,
        } as ConversationSummary
      })
      .sort(
        (a, b) =>
          new Date(b.lastMessageTimestamp).getTime() -
          new Date(a.lastMessageTimestamp).getTime(),
      )
  }
} 