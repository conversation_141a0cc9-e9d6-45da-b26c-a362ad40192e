// TODO: write documentation about fonts and typography along with guides on how to add custom fonts in own
// markdown file and add links from here

import { Platform } from "react-native"

const fonts = {
  system: {
    // Apple system font (San Francisco) on iOS, system font on Android
    light: Platform.select({
      ios: "System",
      android: "sans-serif-light",
    }),
    normal: Platform.select({
      ios: "System",
      android: "sans-serif",
    }),
    medium: Platform.select({
      ios: "System",
      android: "sans-serif-medium",
    }),
    semiBold: Platform.select({
      ios: "System",
      android: "sans-serif-medium",
    }),
    bold: Platform.select({
      ios: "System",
      android: "sans-serif",
    }),
  },

  helveticaNeue: {
    // iOS only font.
    thin: "HelveticaNeue-Thin",
    light: "HelveticaNeue-Light",
    normal: "Helvetica Neue",
    medium: "HelveticaNeue-Medium",
    semiBold: "HelveticaNeue-Medium", // Use medium as fallback
    bold: "HelveticaNeue-Bold",
  },
  courier: {
    // iOS only font.
    normal: "Courier",
  },
  sansSerif: {
    // Android only font.
    thin: "sans-serif-thin",
    light: "sans-serif-light",
    normal: "sans-serif",
    medium: "sans-serif-medium",
    semiBold: "sans-serif-medium", // Use medium as fallback
    bold: "sans-serif",
  },
  monospace: {
    // Android only font.
    normal: "monospace",
  },
}

// No custom fonts to load - using system fonts for optimal performance
export const customFontsToLoad = {}

export const typography = {
  /**
   * The fonts are available to use, but prefer using the semantic name.
   */
  fonts,
  /**
   * The primary font. Used in most places.
   */
  primary: Platform.select({ ios: fonts.helveticaNeue, android: fonts.sansSerif }),
  /**
   * An alternate font used for perhaps titles and stuff.
   */
  secondary: Platform.select({ ios: fonts.helveticaNeue, android: fonts.sansSerif }),
  /**
   * Lets get fancy with a monospace font!
   */
  code: Platform.select({ ios: fonts.courier, android: fonts.monospace }),
}
