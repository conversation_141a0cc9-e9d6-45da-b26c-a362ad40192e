import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { AuthenticationStoreModel } from "./AuthenticationStore"
import { OfflineStoreModel } from "./OfflineStore"
import { CommunityStoreModel } from "./CommunityStore"
import { UserProfileStoreModel } from "./UserProfileStore"

/**
 * A RootStore model.
 */
export const RootStoreModel = types.model("RootStore").props({
  authenticationStore: types.optional(AuthenticationStoreModel, {}),
  offlineStore: types.optional(OfflineStoreModel, {}),
  communityStore: types.optional(CommunityStoreModel, {}),
  userProfileStore: types.optional(UserProfileStoreModel, {}),
})

/**
 * The RootStore instance.
 */
export interface RootStore extends Instance<typeof RootStoreModel> {}
/**
 * The data of a RootStore.
 */
export interface RootStoreSnapshot extends SnapshotOut<typeof RootStoreModel> {}
