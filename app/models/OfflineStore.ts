import { Instance, types } from "mobx-state-tree";

export const OfflineStoreModel = types
  .model("OfflineStore")
  .props({
    isHydrated: types.optional(types.boolean, false),
    lastSync: types.optional(types.Date, () => new Date()),
  })
  .actions(self => ({
    setIsHydrated(isHydrated: boolean) {
      self.isHydrated = isHydrated;
    },
    setLastSync(date: Date) {
      self.lastSync = date;
    },
  }));

export interface OfflineStore extends Instance<typeof OfflineStoreModel> {}
