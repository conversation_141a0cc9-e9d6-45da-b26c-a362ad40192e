import { Instance, types, flow } from "mobx-state-tree";
import { CommunityData } from "@/types";
import { CommunityRepositorySqlite } from "@/services/sqlite/community-repository-sqlite";
import { withSetPropAction } from "./helpers/withSetPropAction";

const CommunityModel = types.model("Community").props({
    id: types.identifier,
    name: types.string,
    description: types.maybe(types.string),
    short_description: types.maybe(types.string),
    memberCount: types.maybe(types.number),
    backgroundImage: types.maybe(types.string),
    isPrivate: types.maybe(types.boolean),
    isJoined: types.maybe(types.boolean),
    stats: types.frozen(),
    address: types.maybe(types.string),
    adminsAndModerators: types.frozen(),
    achievements: types.frozen(),
    rules: types.frozen(),
    upcomingEvents: types.frozen(),
    pastEvents: types.frozen(),
    members: types.frozen(),
    chatMessages: types.frozen(),
});

export const CommunityStoreModel = types
    .model("CommunityStore")
    .props({
        communities: types.array(CommunityModel),
    })
    .actions(withSetPropAction)
    .actions(self => {
        const communityRepository = new CommunityRepositorySqlite();

        const hydrate = flow(function* () {
            const communities: CommunityData[] = yield communityRepository.getAll();
            self.setProp("communities", communities);
        });

        const addCommunity = flow(function* (community: CommunityData) {
            yield communityRepository.save(community);
            yield hydrate();
        });

        const removeCommunity = flow(function* (id: string) {
            yield communityRepository.delete(id);
            yield hydrate();
        });

        return {
            hydrate,
            addCommunity,
            removeCommunity,
        };
    });

export interface CommunityStore extends Instance<typeof CommunityStoreModel> { }
export interface Community extends Instance<typeof CommunityModel> { }
