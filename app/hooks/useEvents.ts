import { useState, useMemo } from "react"
import { Event, EventFilters } from "@/types/events"
import eventsData from "@/data/events.json"

export const useEvents = () => {
  const [filters, setFilters] = useState<EventFilters>({
    selectedLocations: [],
    selectedTypes: [],
  })
  const [showFilters, setShowFilters] = useState(false)

  const allEvents: Event[] = eventsData

  const filteredEvents = useMemo(() => {
    return allEvents.filter((event) => {
      if (
        filters.selectedLocations.length > 0 &&
        !filters.selectedLocations.includes(event.location)
      ) {
        return false
      }
      if (filters.selectedTypes.length > 0 && !filters.selectedTypes.includes(event.type)) {
        return false
      }
      return true
    })
  }, [allEvents, filters])

  const toggleFilter = () => {
    setShowFilters(!showFilters)
  }

  const updateLocationFilters = (locations: string[]) => {
    setFilters((prev) => ({ ...prev, selectedLocations: locations }))
  }

  const updateTypeFilters = (types: string[]) => {
    setFilters((prev) => ({ ...prev, selectedTypes: types }))
  }

  const handleJoinEvent = (event: Event) => {
    if (event.status === "full") return
    // Handle join logic here
    console.log("Joining event:", event.title)
  }

  return {
    filteredEvents,
    filters,
    showFilters,
    toggleFilter,
    updateLocationFilters,
    updateTypeFilters,
    handleJoinEvent,
  }
}
