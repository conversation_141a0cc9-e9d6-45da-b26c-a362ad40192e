import { useEffect } from 'react'
import { Linking } from 'react-native'
import { supabase } from '@/supabase'
import { navigationRef } from '@/navigators'

/**
 * Hook to handle deep link processing, specifically for password reset flows
 */
export const useDeepLinkHandler = () => {
  useEffect(() => {
    const handleDeepLink = async (url: string) => {
      // Check if this is a password reset deep link
      if (!isPasswordResetFlow(url)) {
        return
      }

      const tokens = extractAuthTokensFromUrl(url)
      if (!tokens?.access_token || tokens.type !== 'recovery') {
        return
      }

      try {
        // Pre-load navigation ref for immediate navigation
        const navRef = navigationRef

        // Set the session manually since Supabase auto-detection isn't reliable
        const { error } = await supabase.auth.setSession({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token || '',
        })

        if (error) {
          console.error('Failed to set session during password reset:', error)
          return
        }

        // Navigate immediately to prevent automatic navigation to Main
        if (navRef.current) {
          navRef.current.reset({
            index: 0,
            routes: [{ name: 'PasswordResetConfirm' as never }],
          })
        }
      } catch (error) {
        console.error('Error processing password reset deep link:', error)
      }
    }

    // Listen for deep links when app is already open
    const subscription = Linking.addEventListener('url', (event) => {
      handleDeepLink(event.url)
    })

    // Check for initial URL when app is opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink(url)
      }
    })

    return () => subscription?.remove()
  }, [])
}

/**
 * Check if URL is a password reset flow
 */
const isPasswordResetFlow = (url?: string): boolean => {
  if (!url) return false
  return url.includes('PasswordResetConfirm') && url.includes('type=recovery')
}

/**
 * Extract auth tokens from URL
 */
const extractAuthTokensFromUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    const fragment = urlObj.hash || urlObj.search
    const params = new URLSearchParams(fragment.replace('#', ''))
    
    return {
      access_token: params.get('access_token'),
      refresh_token: params.get('refresh_token'),
      type: params.get('type'),
      expires_in: params.get('expires_in'),
    }
  } catch (error) {
    return null
  }
}
