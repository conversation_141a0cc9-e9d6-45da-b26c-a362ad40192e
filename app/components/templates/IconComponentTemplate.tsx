import React from "react"
import { View, TouchableOpacity, Text, TextInput, ViewStyle, TextStyle } from "react-native"
import { MaterialIcon, FeatherIcon } from "@/components/VectorIcon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

/**
 * TEMPLATE: How to use Vector Icons in new components
 *
 * This template shows common patterns for using vector icons.
 * Copy and modify these patterns for your new components.
 */

// ===== PATTERN 1: Basic Icon Button =====
interface IconButtonProps {
  icon: string
  label: string
  onPress: () => void
  variant?: "primary" | "secondary" | "ghost"
  size?: "small" | "medium" | "large"
}

export function IconButton({
  icon,
  label,
  onPress,
  variant = "primary",
  size = "medium",
}: IconButtonProps) {
  const { themed } = useAppTheme()

  const iconSize = size === "small" ? 16 : size === "large" ? 24 : 20
  const isGhost = variant === "ghost"

  return (
    <TouchableOpacity onPress={onPress} style={themed($button(variant))}>
      <MaterialIcon
        name={icon}
        size={iconSize}
        color={isGhost ? themed.$ghostText.color : "white"}
      />
      <Text style={themed($buttonText(variant))}>{label}</Text>
    </TouchableOpacity>
  )
}

// ===== PATTERN 2: Status Icon with Color =====
interface StatusIconProps {
  status: "active" | "inactive" | "pending"
  size?: number
}

export function StatusIcon({ status, size = 20 }: StatusIconProps) {
  const getStatusConfig = () => {
    switch (status) {
      case "active":
        return { icon: "check-circle", color: "#4CAF50" }
      case "inactive":
        return { icon: "cancel", color: "#F44336" }
      case "pending":
        return { icon: "schedule", color: "#FF9800" }
      default:
        return { icon: "help", color: "#999" }
    }
  }

  const { icon, color } = getStatusConfig()

  return <MaterialIcon name={icon} size={size} color={color} />
}

// ===== PATTERN 3: Navigation Header =====
interface HeaderWithIconsProps {
  title: string
  onBack?: () => void
  onSettings?: () => void
  rightIcon?: string
  onRightPress?: () => void
}

export function HeaderWithIcons({
  title,
  onBack,
  onSettings,
  rightIcon,
  onRightPress,
}: HeaderWithIconsProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($header)}>
      {/* Left: Back Button */}
      {onBack && (
        <TouchableOpacity onPress={onBack} style={themed($headerButton)}>
          <MaterialIcon name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
      )}

      {/* Center: Title */}
      <Text style={themed($headerTitle)}>{title}</Text>

      {/* Right: Action Buttons */}
      <View style={themed($headerRight)}>
        {rightIcon && onRightPress && (
          <TouchableOpacity onPress={onRightPress} style={themed($headerButton)}>
            <MaterialIcon name={rightIcon} size={24} color="white" />
          </TouchableOpacity>
        )}

        {onSettings && (
          <TouchableOpacity onPress={onSettings} style={themed($headerButton)}>
            <MaterialIcon name="settings" size={24} color="white" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
}

// ===== PATTERN 4: List Item with Icon =====
interface IconListItemProps {
  icon: string
  title: string
  subtitle?: string
  onPress?: () => void
  showArrow?: boolean
  iconColor?: string
  badge?: number
}

export function IconListItem({
  icon,
  title,
  subtitle,
  onPress,
  showArrow = true,
  iconColor,
  badge,
}: IconListItemProps) {
  const { themed } = useAppTheme()

  return (
    <TouchableOpacity onPress={onPress} style={themed($listItem)}>
      {/* Left Icon */}
      <View style={themed($iconContainer)}>
        <MaterialIcon name={icon} size={24} color={iconColor || themed.$primaryColor} />
        {/* Badge */}
        {badge && badge > 0 && (
          <View style={themed($badge)}>
            <Text style={themed($badgeText)}>{badge}</Text>
          </View>
        )}
      </View>

      {/* Content */}
      <View style={themed($textContainer)}>
        <Text style={themed($itemTitle)}>{title}</Text>
        {subtitle && <Text style={themed($itemSubtitle)}>{subtitle}</Text>}
      </View>

      {/* Right Arrow */}
      {showArrow && (
        <MaterialIcon name="arrow-forward-ios" size={16} color={themed.$textDim.color} />
      )}
    </TouchableOpacity>
  )
}

// ===== PATTERN 5: Icon Tab Bar =====
interface TabItem {
  key: string
  label: string
  icon: string
}

interface IconTabBarProps {
  tabs: TabItem[]
  activeTab: string
  onTabPress: (key: string) => void
}

export function IconTabBar({ tabs, activeTab, onTabPress }: IconTabBarProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($tabBar)}>
      {tabs.map((tab) => {
        const isActive = tab.key === activeTab

        return (
          <TouchableOpacity key={tab.key} onPress={() => onTabPress(tab.key)} style={themed($tab)}>
            <MaterialIcon
              name={tab.icon}
              size={24}
              color={isActive ? themed.$activeTabColor : themed.$inactiveTabColor}
            />
            <Text style={themed($tabLabel(isActive))}>{tab.label}</Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
}

// ===== PATTERN 6: Floating Action Button =====
interface FABProps {
  icon: string
  onPress: () => void
  size?: "small" | "large"
  position?: "bottom-right" | "bottom-left"
}

export function FloatingActionButton({
  icon,
  onPress,
  size = "large",
  position = "bottom-right",
}: FABProps) {
  const { themed } = useAppTheme()

  const buttonSize = size === "small" ? 48 : 56
  const iconSize = size === "small" ? 20 : 24

  return (
    <TouchableOpacity onPress={onPress} style={themed($fab(buttonSize, position))}>
      <MaterialIcon name={icon} size={iconSize} color="white" />
    </TouchableOpacity>
  )
}

// ===== PATTERN 7: Search Bar with Icon =====
interface SearchBarProps {
  placeholder?: string
  value: string
  onChangeText: (text: string) => void
  onClear?: () => void
}

export function SearchBarWithIcon({
  placeholder = "Search...",
  value,
  onChangeText,
  onClear,
}: SearchBarProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($searchContainer)}>
      <FeatherIcon name="search" size={20} color={themed.$textDim.color} />

      <TextInput
        style={themed($searchInput)}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor={themed.$textDim.color}
      />

      {value.length > 0 && onClear && (
        <TouchableOpacity onPress={onClear}>
          <FeatherIcon name="x" size={20} color={themed.$textDim.color} />
        </TouchableOpacity>
      )}
    </View>
  )
}

// ===== STYLES =====

const $button =
  (variant: string): ThemedStyle<ViewStyle> =>
  ({ colors, spacing }) => ({
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    backgroundColor:
      variant === "primary"
        ? colors.palette.primary500
        : variant === "secondary"
          ? colors.palette.neutral300
          : "transparent",
    borderWidth: variant === "ghost" ? 1 : 0,
    borderColor: variant === "ghost" ? colors.palette.primary500 : "transparent",
  })

const $buttonText =
  (variant: string): ThemedStyle<TextStyle> =>
  ({ colors, typography, spacing }) => ({
    marginLeft: spacing.xs,
    color: variant === "ghost" ? colors.palette.primary500 : "white",
    fontFamily: typography.primary?.medium,
  })

const $ghostText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.primary500,
})

const $primaryColor = ({ colors }) => colors.palette.primary500

const $header: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral800,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  justifyContent: "space-between",
})

const $headerButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
})

const $headerTitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.palette.neutral100,
  fontSize: 18,
  fontFamily: typography.primary?.semiBold,
  flex: 1,
  textAlign: "center",
})

const $headerRight: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
})

const $listItem: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.background,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
})

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginRight: spacing.md,
  position: "relative",
})

const $badge: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  top: -8,
  right: -8,
  backgroundColor: "#FF6B6B",
  borderRadius: 10,
  minWidth: 20,
  height: 20,
  justifyContent: "center",
  alignItems: "center",
})

const $badgeText: ThemedStyle<TextStyle> = () => ({
  color: "white",
  fontSize: 12,
  fontWeight: "bold",
})

const $textContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $itemTitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.text,
  fontSize: 16,
  fontFamily: typography.primary?.medium,
})

const $itemSubtitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.textDim,
  fontSize: 14,
  fontFamily: typography.primary?.normal,
  marginTop: 2,
})

const $textDim: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $tabBar: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  backgroundColor: colors.background,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
  paddingVertical: spacing.xs,
})

const $tab: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  alignItems: "center",
  paddingVertical: spacing.xs,
})

const $tabLabel =
  (isActive: boolean): ThemedStyle<TextStyle> =>
  ({ colors, typography }) => ({
    fontSize: 12,
    fontFamily: typography.primary?.medium,
    color: isActive ? colors.palette.primary500 : colors.textDim,
    marginTop: 2,
  })

const $activeTabColor = ({ colors }) => colors.palette.primary500
const $inactiveTabColor = ({ colors }) => colors.textDim

const $fab =
  (size: number, position: string): ThemedStyle<ViewStyle> =>
  ({ colors, spacing }) => ({
    position: "absolute",
    bottom: spacing.lg,
    [position.includes("right") ? "right" : "left"]: spacing.lg,
    width: size,
    height: size,
    borderRadius: size / 2,
    backgroundColor: colors.palette.primary500,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  })

const $searchContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  marginHorizontal: spacing.md,
})

const $searchInput: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  flex: 1,
  marginLeft: spacing.sm,
  fontSize: 16,
  fontFamily: typography.primary?.normal,
  color: colors.text,
})
