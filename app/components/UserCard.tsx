import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text } from "@/components/Text"
import { Icon } from "@/components/Icon"
import { Avatar } from "@/components/Avatar"
import { StatusBadge } from "@/components/StatusBadge"
import { But<PERSON> } from "@/components/Button"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export interface UserCardUser {
  id: string
  name: string
  role?: string // Could be level, description, community role, etc.
  initials?: string
}

export interface UserCardProps {
  user: UserCardUser
  onUserPress?: (userId: string) => void
  onMessagePress?: (userId: string) => void
  onFollowPress?: (userId: string) => void
  showMessageButton?: boolean
  showFollowButton?: boolean
  followState?: 'follow' | 'following' | 'none'
  statusBadge?: {
    status: string
    variant: 'role' | 'type' | 'indicator' | 'status'
  }
  avatarColor?: string
  containerStyle?: ViewStyle
}

export const UserCard: FC<UserCardProps> = ({
  user,
  onUserPress,
  onMessagePress,
  onFollowPress,
  showMessageButton = false,
  showFollowButton = false,
  followState = 'none',
  statusBadge,
  avatarColor,
  containerStyle,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const handleUserPress = () => {
    onUserPress?.(user.id)
  }

  const handleMessagePress = () => {
    onMessagePress?.(user.id)
  }

  const handleFollowPress = () => {
    onFollowPress?.(user.id)
  }

  return (
    <View style={[themed($container), containerStyle]}>
      <Pressable style={themed($userInfo)} onPress={handleUserPress}>
        <Avatar
          name={user.name}
          size="md"
          style={[
            themed($avatar),
            avatarColor ? { backgroundColor: avatarColor } : { backgroundColor: colors.palette.primary500 }
          ]}
          textStyle={themed($avatarText)}
          textSize="sm"
          textWeight="medium"
          onPress={handleUserPress}
        />
        <Pressable style={themed($userDetails)} onPress={handleUserPress}>
          <Text text={user.name} style={themed($userName)} weight="medium" />
          {user.role && <Text text={user.role} style={themed($userRole)} size="sm" />}
        </Pressable>
      </Pressable>

      <View style={themed($actions)}>
        {/* Status Badge */}
        {statusBadge && (
          <StatusBadge 
            status={statusBadge.status} 
            variant={statusBadge.variant} 
          />
        )}

        {/* Follow Button */}
        {showFollowButton && followState !== 'none' && (
          <Button
            text={followState === 'following' ? "Following" : "Follow"}
            preset="default"
            style={themed(followState === 'following' ? $followingButton : $followButton)}
            textStyle={themed(followState === 'following' ? $followingButtonText : $followButtonText)}
            onPress={handleFollowPress}
          />
        )}

        {/* Message Button */}
        {showMessageButton && onMessagePress && (
          <Pressable onPress={handleMessagePress} style={themed($messageButton)}>
            <Icon icon="slack" size={18} color={colors.palette.primary500} />
          </Pressable>
        )}
      </View>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  padding: spacing.sm,
  borderRadius: 8,
  borderColor: colors.palette.neutral300,
  borderWidth: 1,
})

const $userInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
  marginRight: spacing.sm,
})

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $userDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $userName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontSize: 15,
})

const $userRole: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $actions: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
})

const $followButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 6,
  minHeight: 32,
  minWidth: 80,
})

const $followButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 14,
  fontWeight: "600",
  textAlign: "center",
})

const $followingButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 6,
  minHeight: 32,
  minWidth: 80,
})

const $followingButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral600,
  fontSize: 14,
  fontWeight: "600",
  textAlign: "center",
})

const $messageButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "center",
  alignItems: "center",
}) 