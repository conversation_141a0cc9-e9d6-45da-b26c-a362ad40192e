import React from "react"
import { View, Text, ViewStyle } from "react-native"
import { Icon } from "./Icon" // Your existing Icon component
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

// Temporarily commented out until vector icons are working
// import { VectorIcon, MaterialIcon, FeatherIcon } from "./VectorIcon"

/**
 * Example component demonstrating the difference between
 * PNG-based icons vs Vector icons
 */
export function IconExample() {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <Text style={themed($sectionTitle)}>PNG Icons (Current)</Text>
      <View style={themed($iconRow)}>
        <Icon icon="settings" size={24} />
        <Icon icon="bell" size={24} />
        <Icon icon="caretLeft" size={24} />
        <Icon icon="more" size={24} />
      </View>

      <Text style={themed($sectionTitle)}>Vector Icons (Coming Soon)</Text>
      <View style={themed($iconRow)}>
        <Text>🚧 Vector icons will replace these 🚧</Text>
      </View>

      <Text style={themed($sectionTitle)}>Next: Setup Vector Icons</Text>
      <View style={themed($iconRow)}>
        <Text>📱 Modern scalable icons coming soon! 📱</Text>
      </View>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  padding: spacing.md,
})

const $sectionTitle: ThemedStyle<ViewStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 16,
  fontFamily: typography.primary?.semiBold,
  color: colors.text,
  marginTop: spacing.md,
  marginBottom: spacing.sm,
})

const $iconRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
  marginBottom: spacing.md,
})
