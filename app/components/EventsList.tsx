import { FC, forwardRef } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { type ContentStyle } from "@shopify/flash-list"
import { Text } from "./Text"
import { ListView, ListViewRef } from "./ListView"
import { EventCard } from "./EventCard"
import { EventsListProps, Event } from "@/types/events"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { translate } from "@/i18n"

export const EventsList = forwardRef<ListViewRef<Event>, EventsListProps>(
  ({ events, onJoinEvent, onEventPress }, ref) => {
    const { themed } = useAppTheme()

    return (
      <View style={themed($container)}>
        <ListView<Event>
          ref={ref as any}
          contentContainerStyle={themed($listContentContainer)}
          data={events.slice()}
          extraData={events.length}
          estimatedItemSize={177}
          ListHeaderComponent={
            <View style={themed($eventsContent)}>
              <Text
                text={translate("eventsTab:upcomingEventsTitle")}
                style={themed($sectionTitle)}
                weight="semiBold"
                size="xl"
              />
            </View>
          }
          renderItem={({ item }) => (
            <EventCard event={item} onJoin={onJoinEvent} onPress={onEventPress} />
          )}
        />
      </View>
    )
  },
)

EventsList.displayName = "EventsList"

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.background,
})

const $listContentContainer: ThemedStyle<ContentStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.sm,
})

const $eventsContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: spacing.sm,
  paddingBottom: spacing.xs,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})
