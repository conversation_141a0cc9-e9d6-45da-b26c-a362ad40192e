import { FC } from "react"
import { <PERSON>, ViewStyle, TextStyle, Modal, Pressable, <PERSON><PERSON>, Linking } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"
import { safeAsync } from "@/utils/safeAsync"
import type { ThemedStyle } from "@/theme"

interface Club {
  id: number
  name: string
  initials: string
  address: string
  coordinates: {
    latitude: number
    longitude: number
  }
  phone: string
  amenities: string[]
}

interface MapsSelectionModalProps {
  /**
   * Whether the modal is visible
   */
  visible: boolean
  /**
   * Club information for navigation
   */
  club: Club | null
  /**
   * Function to close the modal
   */
  onClose: () => void
}

export const MapsSelectionModal: FC<MapsSelectionModalProps> = ({ visible, club, onClose }) => {
  const { themed } = useAppTheme()

  const handleMapSelection = async (mapType: string) => {
    if (!club) return

    onClose()

    const { latitude, longitude } = club.coordinates
    const clubName = encodeURIComponent(club.name)

    let appUrl = ""
    let webUrl = ""
    let appName = ""

    switch (mapType) {
      case "apple":
        appUrl = `https://maps.apple.com/?ll=${latitude},${longitude}&q=${clubName}`
        webUrl = `https://maps.apple.com/?ll=${latitude},${longitude}&q=${clubName}`
        appName = "Apple Maps"
        break
      case "google":
        appUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`
        webUrl = `https://www.google.com/maps/@${latitude},${longitude},15z`
        appName = "Google Maps"
        break
      case "waze":
        appUrl = `https://waze.com/ul?ll=${latitude},${longitude}&navigate=yes`
        webUrl = `https://waze.com/ul?ll=${latitude},${longitude}&navigate=yes`
        appName = "Waze"
        break
      default:
        return
    }

    await safeAsync(async () => {
      const appSupported = await Linking.canOpenURL(appUrl)
      if (appSupported) {
        await Linking.openURL(appUrl)
      } else {
        // Try web fallback
        const webSupported = await Linking.canOpenURL(webUrl)
        if (webSupported) {
          await Linking.openURL(webUrl)
        } else {
          Alert.alert("Error", `Cannot open ${appName}. Please check your internet connection.`)
        }
      }
    })
  }

  return (
    <Modal visible={visible} transparent={true} animationType="fade" onRequestClose={onClose}>
      <View style={themed($modalOverlay)}>
        <View style={themed($modalContent)}>
          <Text text="Open in Maps" style={themed($modalTitle)} weight="semiBold" size="lg" />
          <Text
            text={`Choose your preferred maps app to navigate to ${club?.name || ""}`}
            style={themed($modalSubtitle)}
            size="sm"
          />

          <View style={themed($modalButtons)}>
            <Pressable style={themed($modalButton)} onPress={() => handleMapSelection("apple")}>
              <Text text="Apple Maps" style={themed($modalButtonText)} weight="medium" />
            </Pressable>

            <Pressable style={themed($modalButton)} onPress={() => handleMapSelection("google")}>
              <Text text="Google Maps" style={themed($modalButtonText)} weight="medium" />
            </Pressable>

            <Pressable style={themed($modalButton)} onPress={() => handleMapSelection("waze")}>
              <Text text="Waze" style={themed($modalButtonText)} weight="medium" />
            </Pressable>

            <Pressable style={[themed($modalButton), themed($modalCancelButton)]} onPress={onClose}>
              <Text text="Cancel" style={themed($modalCancelText)} weight="medium" />
            </Pressable>
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $modalOverlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 20,
})

const $modalContent: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.lg,
  width: "100%",
  maxWidth: 340,
})

const $modalTitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.xs,
})

const $modalSubtitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  textAlign: "center",
  marginBottom: spacing.lg,
  lineHeight: 18,
})

const $modalButtons: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm,
})

const $modalButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral200,
  borderRadius: 12,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
  alignItems: "center",
})

const $modalButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontSize: 16,
})

const $modalCancelButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.angry500,
  marginTop: 4,
})

const $modalCancelText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 16,
})
