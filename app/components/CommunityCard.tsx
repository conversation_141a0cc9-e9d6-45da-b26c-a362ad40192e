import React, { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Avatar } from "./Avatar"
import { Button } from "./Button"
import { Card } from "./Card"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { CommunityData } from "@/types/communities"
import { CommunityService } from "@/services"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface CommunityCardProps {
  community: CommunityData
  onPress: (community: CommunityData) => void
  onJoinPress?: (community: CommunityData) => void
  onRequestPress?: (community: CommunityData) => void
}

export const CommunityCard: FC<CommunityCardProps> = ({
  community,
  onPress,
  onJoinPress,
  onRequestPress,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Get button configuration from service
  const joinButtonConfig = CommunityService.getJoinButtonConfig(community)
  const buttonStyleConfig = CommunityService.getButtonStyleConfig(joinButtonConfig.variant)
  
  // Get formatted data from service
  const memberCount = CommunityService.formatMemberCount(community)
  const formattedAddress = CommunityService.formatAddress(community)

  const handleActionPress = () => {
    // Validate action through service
    const action = joinButtonConfig.action
    
    if (!action) return // No action available (already joined)

    const validation = CommunityService.validateCommunityAction(community, action)
    
    if (!validation.canPerform) {
      // Could show error message using validation.reason
      return
    }

    // Call appropriate callback based on action type
    if (action === 'request' && onRequestPress) {
      onRequestPress(community)
    } else if (action === 'join' && onJoinPress) {
      onJoinPress(community)
    }
  }

  const getJoinButtonStyles = () => {
    return [
      themed($joinButton),
      { backgroundColor: buttonStyleConfig.backgroundColor }
    ]
  }

  return (
    <Card
      style={themed($communityCard)}
      onPress={() => onPress(community)}
      HeadingComponent={
        <View style={themed($communityHeader)}>
          <Avatar
            name={community.name}
            size="md"
            style={themed($avatar)}
            textStyle={themed($avatarText)}
            textSize="sm"
            textWeight="bold"
          />
          <View style={themed($headerContent)}>
            <View style={themed($textContent)}>
              <Text
                text={community.name}
                style={themed($communityName)}
                size="sm"
                weight="bold"
                numberOfLines={1}
              />
              <Text
                text={community.short_description}
                style={themed($description)}
                size="xxs"
                numberOfLines={2}
              />
            </View>
            <Icon icon="caretRight" size={16} color={colors.palette.neutral400} />
          </View>
        </View>
      }
      FooterComponent={
        <View style={themed($communityFooter)}>
          <View style={themed($leftDetails)}>
            <View style={themed($detailItem)}>
              <Icon icon="community" size={12} color={colors.textDim} />
              <Text text={memberCount} style={themed($detailText)} size="xs" />
            </View>
            <View style={themed($detailItem)}>
              <Icon icon="pin" size={12} color={colors.textDim} />
              <Text
                text={formattedAddress}
                style={themed($detailText)}
                size="xs"
                numberOfLines={1}
              />
            </View>
          </View>
          <Button
            text={joinButtonConfig.text}
            preset="filled"
            style={getJoinButtonStyles()}
            textStyle={themed($joinButtonText)}
            disabled={joinButtonConfig.disabled}
            onPress={handleActionPress}
          />
        </View>
      }
    />
  )
}

const $communityCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  marginBottom: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  padding: spacing.md,
})

const $communityHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  marginBottom: spacing.sm,
})

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#4a9d8e",
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.md,
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $headerContent: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  flexDirection: "row",
  alignItems: "flex-start",
  justifyContent: "space-between",
})

const $textContent: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $communityName: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  fontWeight: "700",
})

const $description: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginTop: spacing.xxs,
})

const $communityFooter: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginTop: spacing.sm,
})

const $leftDetails: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.md,
  flex: 1,
  alignItems: "center",
})

const $detailItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xxs,
})

const $detailText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $joinButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: 6,
  borderRadius: 6,
  minWidth: 60,
  height: 32,
  minHeight: 32,
  maxHeight: 32,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 0,
})

const $joinButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 14,
  fontWeight: "600",
  textAlign: "center",
})
