import { FC } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "./Text"
import { Button } from "./Button"
import { Avatar } from "./Avatar"
import { useAppTheme } from "@/utils/useAppTheme"
import { Notification } from "@/types/notifications"
import { NotificationService } from "@/services"
import type { ThemedStyle } from "@/theme"

interface NotificationItemProps {
  notification: Notification
  onFollowPress: (notificationId: string) => void
  onActionPress: (notificationId: string, action: string) => void
  onUserPress: (userId: string, userName: string) => void
  onEventPress: (eventName: string) => void
}

export const NotificationItem: FC<NotificationItemProps> = ({
  notification,
  onFollowPress,
  onActionPress,
  onUserPress,
  onEventPress,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Get configuration from service
  const textConfig = NotificationService.getNotificationTextConfig(notification)
  const buttonConfig = NotificationService.getActionButtonConfig(notification)
  const clickableElements = NotificationService.getClickableElements(notification)

  const renderNotificationText = () => {
    return (
      <View>
        <Text style={themed($notificationText)}>
          {textConfig.segments.map((segment, index) => {
            if (segment.style === "clickable") {
              return (
                <Text 
                  key={index}
                  style={themed($clickableText)}
                  onPress={() => onUserPress(clickableElements.userUsername, clickableElements.userName)}
                >
                  {segment.text}
                </Text>
              )
            } else if (segment.style === "highlight") {
              return (
                <Text 
                  key={index}
                  style={themed($highlightText)}
                  onPress={() => segment.text && onEventPress(segment.text)}
                >
                  {segment.text}
                </Text>
              )
            } else {
              return <Text key={index}>{segment.text}</Text>
            }
          })}
        </Text>
      </View>
    )
  }

  const renderActionButton = () => {
    if (!buttonConfig) return null

    const handleActionPress = () => {
      // Validate action through service
      const validation = NotificationService.validateNotificationAction(notification, buttonConfig.action)
      
      if (!validation.canPerform) {
        // Could show error message using validation.reason
        return
      }

      // Call appropriate callback based on action type
      if (buttonConfig.action === "follow") {
        onFollowPress(notification.id)
      } else {
        onActionPress(notification.id, buttonConfig.action)
      }
    }

    return (
      <Button
        text={buttonConfig.text}
        preset={buttonConfig.preset}
        style={themed($actionButton)}
        textStyle={themed($actionButtonText)}
        disabled={buttonConfig.disabled}
        onPress={handleActionPress}
      />
    )
  }

  return (
    <View style={themed($container(notification.isRead))}>
      <Avatar
        name={notification.user.name}
        size="sm"
        style={themed($avatar)}
        textStyle={themed($avatarText)}
        onPress={() => onUserPress(notification.user.username, notification.user.name)}
      />

      <View style={themed($content)}>
        {renderNotificationText()}
        <Text style={themed($timestamp)}>{notification.timestamp}</Text>
      </View>

      <View style={themed($actions)}>{renderActionButton()}</View>
    </View>
  )
}

const $container =
  (isRead: boolean): ThemedStyle<ViewStyle> =>
  ({ colors, spacing }) => ({
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: spacing.xs,
    paddingHorizontal: spacing.md,
    backgroundColor: isRead ? "transparent" : colors.palette.neutral200,
    borderBottomWidth: 1,
    borderBottomColor: colors.palette.neutral300,
    minHeight: 56,
  })

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: "#6366f1",
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.palette.neutral100,
  fontFamily: typography.primary?.bold,
  fontSize: 12,
})

const $content: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  marginRight: 12,
})

const $notificationText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 13,
  fontFamily: typography.primary?.normal,
  color: colors.text,
  lineHeight: 16,
})

const $clickableText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 16,
  fontFamily: typography.primary?.medium,
  color: colors.palette.primary500,
})

const $highlightText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 16,
  fontFamily: typography.primary?.medium,
  color: colors.palette.accent500,
})

const $timestamp: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 11,
  fontFamily: typography.primary?.normal,
  color: colors.textDim,
  marginTop: spacing.xxs,
})

const $actions: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
})

const $actionButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  minWidth: 60,
  minHeight: 40,
  borderRadius: 10,
  paddingHorizontal: 16,
  paddingVertical: 4,
})

const $actionButtonText: ThemedStyle<TextStyle> = () => ({
  fontSize: 12,
})
