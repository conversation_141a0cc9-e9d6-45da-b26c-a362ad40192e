import { observer } from "mobx-react-lite"
import { FC, ReactNode } from "react"
import { ScrollView, View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { SectionCarouselService } from "@/services"
import type { ThemedStyle } from "@/theme"

export interface SectionCarouselProps<T> {
  data: T[]
  renderItem: (item: T, index: number) => ReactNode
  keyExtractor: (item: T, index: number) => string
  itemsPerRow?: number
  itemWidth?: number
  itemSpacing?: number
  containerStyle?: ViewStyle
  contentContainerStyle?: ViewStyle
  showsHorizontalScrollIndicator?: boolean
  horizontal?: boolean
}

export const SectionCarousel = function SectionCarousel(props: SectionCarouselProps<any>) {
  const {
    data,
    renderItem,
    keyExtractor,
    itemsPerRow = 1,
    itemWidth,
    itemSpacing = 12,
    containerStyle,
    contentContainerStyle,
    showsHorizontalScrollIndicator = false,
    horizontal = true,
  } = props
  const { themed } = useAppTheme()

  // Chunk data into columns (each column has up to itemsPerRow items)
  const chunkedData = SectionCarouselService.chunkData(data as any[], itemsPerRow)

  return (
    <ScrollView
      horizontal={horizontal}
      showsHorizontalScrollIndicator={showsHorizontalScrollIndicator}
      style={[themed($container), containerStyle]}
      contentContainerStyle={[themed($contentContainer), contentContainerStyle]}
    >
      {chunkedData.map((column: any[], colIdx: number) => (
        <View
          key={colIdx}
          style={[
            themed($column),
            ...(itemWidth ? [{ width: itemWidth }] : []),
            ...(colIdx < chunkedData.length - 1 ? [{ marginRight: itemSpacing }] : []),
          ]}
        >
          {column.map((item, rowIdx) => (
            <View key={keyExtractor(item, colIdx * itemsPerRow + rowIdx)} style={themed($itemContainer)}>
              {renderItem(item, colIdx * itemsPerRow + rowIdx)}
            </View>
          ))}
        </View>
      ))}
    </ScrollView>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  flexGrow: 0,
})

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  gap: spacing.xxxs,
  flexDirection: "row",
  alignItems: "flex-start",
})

const $column: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "column",
  gap: spacing.xs,
})

const $itemContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})