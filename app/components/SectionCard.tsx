import { FC, ReactNode } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface SectionCardProps {
  /**
   * The title of the section
   */
  title: string
  /**
   * The content to display inside the section
   */
  children: ReactNode
  /**
   * Optional style overrides for the container
   */
  containerStyle?: ViewStyle
  /**
   * Optional style overrides for the title
   */
  titleStyle?: TextStyle
  /**
   * Optional style overrides for the content area
   */
  contentStyle?: ViewStyle
}

export const SectionCard: FC<SectionCardProps> = ({
  title,
  children,
  containerStyle,
  titleStyle,
  contentStyle,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={[themed($section), containerStyle]}>
      <Text text={title} style={[themed($sectionTitle), titleStyle]} weight="semiBold" />
      <View style={[themed($sectionContent), contentStyle]}>{children}</View>
    </View>
  )
}

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ spacing, colors }) => ({
  fontSize: 18,
  marginBottom: spacing.sm,
  color: colors.text,
  paddingHorizontal: spacing.xxs,
})

const $sectionContent: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.sm,
  padding: spacing.md,
  borderWidth: 0.5,
  borderColor: colors.palette.neutral300,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})
