import React, { memo } from "react"
import { ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"

// Vector icon imports - now enabled for better performance
import MaterialIcons from "react-native-vector-icons/MaterialIcons"
import Feather from "react-native-vector-icons/Feather"
import Ionicons from "react-native-vector-icons/Ionicons"
import FontAwesome from "react-native-vector-icons/FontAwesome"

type IconLibrary = "material" | "feather" | "ionicons" | "fontawesome"

export interface VectorIconProps {
  /**
   * The name of the icon
   */
  name: string

  /**
   * The icon library to use
   */
  library?: IconLibrary

  /**
   * An optional tint color for the icon
   */
  color?: string

  /**
   * An optional size for the icon
   */
  size?: number

  /**
   * Style overrides for the icon container
   */
  style?: ViewStyle
}

/**
 * A modern vector icon component using react-native-vector-icons
 * Supports multiple icon libraries with scalable vector graphics
 */
export const VectorIcon = memo(function VectorIcon(props: VectorIconProps) {
  const { name, library = "material", color, size = 24, style } = props

  const { theme } = useAppTheme()
  const iconColor = color ?? theme.colors.text

  const getIconComponent = () => {
    // Vector icons now enabled - these are cached and perform much better than PNG
    switch (library) {
      case "material":
        return <MaterialIcons name={name} size={size} color={iconColor} style={style} />
      case "feather":
        return <Feather name={name} size={size} color={iconColor} style={style} />
      case "ionicons":
        return <Ionicons name={name} size={size} color={iconColor} style={style} />
      case "fontawesome":
        return <FontAwesome name={name} size={size} color={iconColor} style={style} />
      default:
        return <MaterialIcons name={name} size={size} color={iconColor} style={style} />
    }
  }

  return getIconComponent()
})

// Convenience exports for different icon libraries
export const MaterialIcon = (props: Omit<VectorIconProps, "library">) => (
  <VectorIcon {...props} library="material" />
)

export const FeatherIcon = (props: Omit<VectorIconProps, "library">) => (
  <VectorIcon {...props} library="feather" />
)

export const IonIcon = (props: Omit<VectorIconProps, "library">) => (
  <VectorIcon {...props} library="ionicons" />
)

export const FontAwesomeIcon = (props: Omit<VectorIconProps, "library">) => (
  <VectorIcon {...props} library="fontawesome" />
)
