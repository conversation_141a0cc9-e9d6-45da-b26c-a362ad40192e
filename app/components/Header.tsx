import { FC, ReactNode } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { Icon, IconTypes } from "./Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import { useEnhancedSafeArea } from "@/utils/safeAreaHelpers"
import type { ThemedStyle } from "@/theme"
import { LayoutChangeEvent } from "react-native"

export interface HeaderButton {
  icon?: IconTypes
  text?: string
  onPress: () => void
  style?: ViewStyle
  textColor?: string
  textStyle?: TextStyle
}

export interface HeaderProps {
  /**
   * Main title text
   */
  title?: string

  /**
   * Subtitle text (appears below title)
   */
  subtitle?: string

  /**
   * Show back button on left side
   */
  showBackButton?: boolean

  /**
   * Back button press handler
   */
  onBackPress?: () => void

  /**
   * Left side content (overrides back button if provided)
   */
  leftContent?: ReactNode

  /**
   * Right side buttons
   */
  rightButtons?: HeaderButton[]

  /**
   * Callback for right buttons container layout
   */
  onRightButtonsLayout?: (event: LayoutChangeEvent) => void

  /**
   * Custom background color (defaults to theme)
   */
  backgroundColor?: string

  /**
   * Whether to include safe area padding
   */
  includeSafeArea?: boolean

  /**
   * Custom style for the header container
   */
  style?: ViewStyle
}

export const Header: FC<HeaderProps> = ({
  title,
  subtitle,
  showBackButton = false,
  onBackPress,
  leftContent,
  rightButtons = [],
  onRightButtonsLayout,
  backgroundColor,
  includeSafeArea = true,
  style,
}) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const safeArea = useEnhancedSafeArea()

  const renderLeftContent = () => {
    if (leftContent) {
      return <View style={themed($leftSection)}>{leftContent}</View>
    }

    if (showBackButton) {
      return (
        <View style={themed($leftSection)}>
          <TouchableOpacity style={themed($backButton)} onPress={onBackPress}>
            <Icon icon="caretLeft" size={20} color={colors.palette.neutral100} />
          </TouchableOpacity>
          {title && <Text text={title} style={themed($headerTitle)} size="xl" weight="bold" />}
        </View>
      )
    }

    if (title && !leftContent) {
      return (
        <View style={themed($leftSection)}>
          <Text text={title} style={themed($headerTitle)} size="xl" weight="bold" />
        </View>
      )
    }

    return null
  }

  const renderRightButtons = () => {
    if (rightButtons.length === 0) return null

    return (
      <View style={themed($headerButtons)} onLayout={onRightButtonsLayout}>
        {rightButtons.map((button, index) => (
          <TouchableOpacity
            key={index}
            style={[themed($iconButton), button.style]}
            onPress={button.onPress}
          >
            {button.icon && (
              <Icon
                icon={button.icon}
                size={18}
                color={button.textColor || colors.palette.neutral100}
              />
            )}
            {button.text && (
              <Text
                text={button.text}
                style={[
                  themed($buttonText),
                  { color: button.textColor || colors.palette.neutral100 },
                  button.textStyle,
                ]}
                size="sm"
                weight="medium"
              />
            )}
          </TouchableOpacity>
        ))}
      </View>
    )
  }

  return (
    <View
      style={[
        themed($header),
        {
          paddingTop: includeSafeArea ? safeArea.top + 8 : 16,
          backgroundColor: backgroundColor || colors.palette.neutral800,
        },
        style,
      ]}
    >
      <View style={themed($headerContent)}>
        {renderLeftContent()}
        {renderRightButtons()}
      </View>
      {subtitle && <Text text={subtitle} style={themed($subtitle)} size="sm" />}
    </View>
  )
}

const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.md,
})

const $headerContent: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
})

const $leftSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  flex: 1,
})

const $headerTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $subtitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.palette.neutral100,
  marginTop: spacing.xs,
})

const $headerButtons: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.sm,
  alignItems: "center",
})

const $iconButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  borderRadius: 20,
  minWidth: 36,
  minHeight: 36,
  alignItems: "center",
  justifyContent: "center",
})

const $backButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
  borderRadius: 20,
})

const $buttonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  textAlign: "center",
})
