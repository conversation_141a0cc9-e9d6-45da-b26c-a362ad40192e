import { FC, useState } from "react"
import { View, ViewStyle, TextStyle, TextInput, Pressable } from "react-native"
import { Icon } from "./Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface ChatInputContainerProps {
  onSendMessage?: (message: string) => void
  placeholder?: string
}

export const ChatInputContainer: FC<ChatInputContainerProps> = ({
  onSendMessage,
  placeholder = "Type a message...",
}) => {
  const [message, setMessage] = useState("")
  const {
    themed,
    theme: { colors, spacing },
  } = useAppTheme()

  const handleSendMessage = () => {
    if (message.trim() && onSendMessage) {
      onSendMessage(message.trim())
      setMessage("")
    }
  }

  return (
    <View style={themed($container)}>
      <View style={themed($inputWrapper)}>
        <TextInput
          style={themed($textInput)}
          placeholder={placeholder}
          placeholderTextColor={colors.textDim}
          value={message}
          onChangeText={setMessage}
          multiline
          textAlignVertical="center"
        />
        <Pressable onPress={handleSendMessage} style={themed($sendButton)}>
          <Icon icon="caretRight" size={20} color={colors.palette.neutral100} />
        </Pressable>
      </View>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.background,
  borderTopWidth: 1,
  borderTopColor: colors.border,
  paddingTop: spacing.sm,
})

const $inputWrapper: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "flex-end",
  gap: spacing.sm,
  height: 48,
  paddingHorizontal: spacing.md,
})

const $textInput: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  flex: 1,
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 24,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  color: colors.text,
  fontSize: 16,
  height: 48,
  maxHeight: 96,
  textAlignVertical: "center",
  includeFontPadding: false,
})

const $sendButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: colors.palette.primary500,
  justifyContent: "center",
  alignItems: "center",
  elevation: 0,
  shadowOpacity: 0,
})
