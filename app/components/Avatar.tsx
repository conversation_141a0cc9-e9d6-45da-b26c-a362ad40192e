import { FC } from "react"
import { Pressable, ViewStyle, TextStyle, PressableProps } from "react-native"
import { Text } from "@/components/Text"
import {
  generateInitials,
  AvatarSize,
  AvatarColor,
  AVATAR_SIZES,
  AVATAR_COLORS,
} from "@/utils/avatar"

export interface AvatarProps extends Omit<PressableProps, "style"> {
  /**
   * Name to generate initials from
   */
  name: string

  /**
   * Avatar size preset or custom number
   */
  size?: AvatarSize | number

  /**
   * Background color preset or custom color string
   */
  backgroundColor?: AvatarColor | string

  /**
   * Text color for initials
   */
  textColor?: string

  /**
   * Custom container style (will override size and backgroundColor if provided)
   */
  style?: ViewStyle | ViewStyle[]

  /**
   * Custom text style for initials
   */
  textStyle?: TextStyle | TextStyle[]

  /**
   * Text size preset for the initials
   */
  textSize?: "xxs" | "xs" | "sm" | "md" | "lg" | "xl" | "xxl"

  /**
   * Text weight for the initials
   */
  textWeight?: "normal" | "medium" | "semiBold" | "bold"

  /**
   * Whether the avatar should be pressable
   */
  disabled?: boolean

  /**
   * Test ID for testing
   */
  testID?: string
}

/**
 * Shared Avatar component for consistent avatar display across the app
 * Styling is handled by parent components to maintain flexibility
 */
export const Avatar: FC<AvatarProps> = ({
  name,
  size = "md",
  backgroundColor = "primary",
  textColor = "#FFFFFF",
  style,
  textStyle,
  textSize = "sm",
  textWeight = "medium",
  disabled = false,
  testID,
  onPress,
  ...pressableProps
}) => {
  const initials = generateInitials(name)

  // Get default dimensions if no custom style provided
  const defaultStyle: ViewStyle = (() => {
    // If custom style array/object is provided, don't apply defaults
    if (style) return {}

    const dimension = typeof size === "number" ? size : AVATAR_SIZES[size]
    const bgColor =
      typeof backgroundColor === "string" && backgroundColor.startsWith("#")
        ? backgroundColor
        : AVATAR_COLORS[backgroundColor as AvatarColor] || AVATAR_COLORS.primary

    return {
      width: dimension,
      height: dimension,
      borderRadius: dimension / 2,
      backgroundColor: bgColor,
      justifyContent: "center",
      alignItems: "center",
    }
  })()

  const defaultTextStyle: TextStyle = {
    color: textColor,
  }

  const combinedStyle = style ? style : defaultStyle
  const combinedTextStyle = textStyle ? textStyle : defaultTextStyle

  return (
    <Pressable
      style={combinedStyle}
      onPress={onPress}
      disabled={disabled || !onPress}
      testID={testID}
      {...pressableProps}
    >
      <Text text={initials} style={combinedTextStyle} size={textSize} weight={textWeight} />
    </Pressable>
  )
}
