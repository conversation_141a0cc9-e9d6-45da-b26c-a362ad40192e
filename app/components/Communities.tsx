import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components/Text"
import { CommunityCardLight } from "@/components/CommunityCardLight"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export interface UserCommunity {
  id: string
  name: string
  members: number
  role: "Member" | "Admin"
  color: string
}

export interface CommunitiesProps {
  communities: UserCommunity[]
  onCommunityPress: (community: UserCommunity) => void
}

export const Communities: FC<CommunitiesProps> = ({ communities, onCommunityPress }) => {
  const { themed } = useAppTheme()

  const renderCommunityCard = (community: UserCommunity) => (
    <CommunityCardLight key={community.id} community={community} onPress={onCommunityPress} />
  )

  return (
    <View style={themed($section)}>
      <Text text="My Communities" style={themed($sectionTitle)} size="lg" weight="semiBold" />
      <View style={themed($sectionContent)}>
        {communities.map((community) => renderCommunityCard(community))}
      </View>
    </View>
  )
}

const $section: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl,
})

const $sectionTitle: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  marginBottom: spacing.md,
})

const $sectionContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm, // Add proper spacing between community cards
})
