import { FC } from "react"
import { Pressable, View, ViewStyle, TextStyle, StyleProp } from "react-native"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text } from "@/components/Text"

interface Option<T> {
  label: string
  value: T
}

export interface SegmentedSwitchProps<T = string> {
  /** Options to display */
  options: Option<T>[]
  /** Currently selected value */
  value: T
  /** Callback when new value selected */
  onChange: (value: T) => void
  /** Selected background color (defaults to theme primary) */
  selectionColor?: string
  /** Whether to apply rounded corners */
  round?: boolean
  /** Optional container style override */
  style?: StyleProp<ViewStyle>
}

export const SegmentedSwitch: FC<SegmentedSwitchProps<any>> = function SegmentedSwitch(props) {
  const {
    options,
    value,
    onChange,
    selectionColor,
    round = true,
    style: $styleOverride,
  } = props

  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  const selColor = selectionColor || colors.palette.primary500
  const borderRadius = round ? 25 : 4

  return (
    <View
      style={[
        themed([
          $container,
          {
            borderColor: selColor,
            borderRadius,
            borderWidth: 1,
            backgroundColor: colors.background,
            overflow: "hidden",
          },
        ]),
        $styleOverride,
      ]}
    >
      {options.map((opt, idx) => {
        const isSelected = opt.value === value
        const last = idx === options.length - 1
        const first = idx === 0
        return (
          <Pressable
            key={String(opt.value)}
            style={themed([
              $option,
              {
                backgroundColor: isSelected ? selColor : colors.background,
                borderTopLeftRadius: first ? borderRadius : 0,
                borderBottomLeftRadius: first ? borderRadius : 0,
                borderTopRightRadius: last ? borderRadius : 0,
                borderBottomRightRadius: last ? borderRadius : 0,
              },
            ])}
            accessibilityRole="button"
            accessibilityState={{ selected: isSelected }}
            onPress={() => onChange(opt.value)}
          >
            <Text
              text={opt.label}
              size="sm"
              style={themed([
                $label,
                { color: isSelected ? colors.palette.neutral100 : selColor },
              ])}
            />
          </Pressable>
        )
      })}
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  height: 44,
  padding: 2,
})

const $option: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
})

const $label: ThemedStyle<TextStyle> = () => ({
  fontWeight: "600",
}) 