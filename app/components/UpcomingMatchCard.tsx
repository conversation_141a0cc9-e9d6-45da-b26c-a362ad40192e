import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { StatusBadge } from "./StatusBadge"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Icon } from "./Icon"
import { Button } from "./Button"

export interface UpcomingMatch {
  id: number
  title: string
  date: string
  location: string
  status: string
}

export interface UpcomingMatchCardProps {
  match: UpcomingMatch
  onPress?: (match: UpcomingMatch) => void
}

export const UpcomingMatchCard: FC<UpcomingMatchCardProps> = ({ match, onPress }) => {
  const { themed } = useAppTheme()

  const handlePress = () => {
    if (onPress) {
      onPress(match)
    }
  }

  // Prepare all styles outside JSX
  const $cardStyle = themed($matchCard)
  const $mainContentStyle = themed($mainContent)
  const $rowCenterStyle = themed($rowCenter)
  const $iconLeftStyle = themed($iconLeft)
  const $mainDateBlackStyle = themed($mainDateBlack)
  const $eventTitleBlackStyle = themed($eventTitleBlack)
  const $eventLocationBlackStyle = themed($eventLocationBlack)
  const $arrowButtonStyle = themed($arrowButton)
  const $arrowIconStyle = themed($arrowIcon)
  const $containerRowStyle = themed($containerRow)

  const handleArrowPress = (e: any) => {
    e.stopPropagation && e.stopPropagation();
    if (onPress) {
      onPress(match)
    }
  }

  return (
    <TouchableOpacity 
      style={$cardStyle} 
      onPress={handlePress}
      activeOpacity={0.9}
    >
      <View style={$containerRowStyle}>
        <View style={$mainContentStyle}>
          <View style={$rowCenterStyle}>
            <Icon icon="calendar" size={20} color="#111" containerStyle={$iconLeftStyle} />
            <Text text={match.date} style={$mainDateBlackStyle} />
            <Text text=" | " style={$mainDateBlackStyle} />
            <Text text="09:00 AM" style={$mainDateBlackStyle} />
          </View>
          <Text text={match.title} style={$eventTitleBlackStyle} />
          <View style={$rowCenterStyle}>
            <Icon icon="location" size={18} color="#111" containerStyle={$iconLeftStyle} />
            <Text text={match.location} style={$eventLocationBlackStyle} />
          </View>
        </View>
        <TouchableOpacity style={$arrowButtonStyle} onPress={handleArrowPress} activeOpacity={0.7}>
          <Icon icon="caretRight" size={22} color="#111" containerStyle={$arrowIconStyle} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
}

const $matchCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#f5f7fa", // much lighter, near-white
  borderRadius: spacing.sm,
  padding: spacing.sm,
  marginBottom: spacing.xs,
})

const $mainContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  alignItems: "flex-start",
  justifyContent: "center",
  paddingVertical: spacing.xxs,
})

const $rowCenter: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  marginBottom: spacing.xxs,
})

const $iconLeft: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginRight: spacing.xs,
})

const $mainDateBlack: ThemedStyle<TextStyle> = () => ({
  color: "#111",
  fontSize: 16,
  fontWeight: "600",
})

const $eventTitleBlack: ThemedStyle<TextStyle> = ({ spacing }) => ({
  color: "#111",
  fontSize: 18,
  fontWeight: "700",
  marginBottom: spacing.xxs,
})

const $eventLocationBlack: ThemedStyle<TextStyle> = () => ({
  color: "#111",
  fontSize: 14,
  marginLeft: 2,
})

const $containerRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
})

const $arrowButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: "#e5e7eb",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: spacing.sm,
})

const $arrowIcon: ThemedStyle<ViewStyle> = () => ({})
