import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Button } from "./Button"
import { Card } from "./Card"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { StatusBadge } from "./StatusBadge"
import { EventCardProps } from "@/types/events"
import { EventService } from "@/services"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export const EventCard: FC<EventCardProps> = ({ event, onJoin, onPress }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Get join button configuration from service
  const joinButtonConfig = EventService.getJoinButtonConfig(event)
  
  // Get participants text from service
  const participantsText = EventService.formatParticipantsText(event)
  
  // Get price text from service
  const priceText = EventService.formatPriceText(event)

  const handleJoin = () => {
    // Validate join action through service
    const validation = EventService.validateJoinAction(event)
    
    if (validation.canJoin && onJoin) {
      onJoin(event)
    }
    // Note: validation.reason could be used for user feedback if needed
  }

  const getJoinButtonStyles = () => {
    if (joinButtonConfig.variant === "disabled") {
      return {
        backgroundColor: colors.palette.neutral400,
      }
    }
    return {
      backgroundColor: colors.palette.success500,
    }
  }

  return (
    <Card
      style={themed($eventCard)}
      onPress={onPress ? () => onPress(event) : undefined}
      HeadingComponent={
        <View style={themed($eventHeader)}>
          <View style={themed($headerLeft)}>
            <StatusBadge 
              status={event.type} 
              variant="type" 
              size="xs"
              style={themed($statusBadgeOverride)}
              textStyle={themed($statusTextOverride)}
            />
            <Text text={event.date} style={themed($dateText)} size="xs" />
          </View>
        </View>
      }
      ContentComponent={
        <View style={themed($titleSection)}>
          <Text text={event.title} style={themed($eventTitle)} />
          <Button
            text={joinButtonConfig.text}
            preset="filled"
            style={[themed($joinButton), getJoinButtonStyles()]}
            textStyle={themed($joinButtonText)}
            disabled={joinButtonConfig.disabled}
            onPress={handleJoin}
          />
        </View>
      }
      FooterComponent={
        <View style={themed($eventFooter)}>
          <View style={themed($leftDetails)}>
            <Icon icon="pin" size={14} color={colors.textDim} />
            <Text text={event.location} style={themed($detailText)} size="sm" />
          </View>
          <View style={themed($rightDetails)}>
            <View style={themed($detailItem)}>
              <Text text="AED" style={themed($currencyText)} size="xs" weight="medium" />
              <Text text={priceText} style={themed($priceText)} size="sm" />
            </View>
            <View style={themed($detailItem)}>
              <Icon icon="community" size={14} color={colors.textDim} />
              <Text
                text={participantsText}
                style={themed($detailText)}
                size="sm"
              />
            </View>
          </View>
        </View>
      }
    />
  )
}

const $eventCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  marginBottom: spacing.md,
  backgroundColor: colors.palette.neutral100,
  padding: spacing.md,
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.03,
  shadowRadius: 3,
  elevation: 2,
})

const $eventHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.xs,
})

const $headerLeft: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  flex: 1,
})

// Custom style override to match original EventCard sizing exactly
const $statusBadgeOverride: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,    // 12px (original)
  paddingVertical: spacing.xs,      // 8px (original)
  borderRadius: 12,                 // 12px (original)
})

const $statusTextOverride: ThemedStyle<TextStyle> = () => ({
  fontSize: 12,                     // Match original size="xxs"
  lineHeight: 18,                   // Match original size="xxs"
})

const $dateText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $titleSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.sm,
})

const $eventTitle: ThemedStyle<TextStyle> = ({ spacing }) => ({
  flex: 1,
  fontWeight: "700",
  fontSize: 18,
  marginRight: spacing.sm,
})

const $eventFooter: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  marginTop: spacing.sm,
})

const $leftDetails: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  flex: 1,
})

const $rightDetails: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
  width: 120,
  marginRight: spacing.md,
})

const $detailItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs,
})

const $detailText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $currencyText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  backgroundColor: colors.palette.neutral200,
  paddingHorizontal: 6,
  paddingVertical: 2,
  borderRadius: 4,
  fontSize: 10,
})

const $priceText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  fontWeight: "600",
})

const $joinButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,
  paddingVertical: 6,
  borderRadius: 6,
  minWidth: 50,
  height: 32,
  minHeight: 32,
  maxHeight: 32,
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 0,
})

const $joinButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 14,
  fontWeight: "600",
  textAlign: "center",
})
