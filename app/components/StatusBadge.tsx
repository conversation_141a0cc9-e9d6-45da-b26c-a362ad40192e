import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { useAppTheme } from "@/utils/useAppTheme"
import { StatusBadgeService } from "@/services"
import type { ThemedStyle } from "@/theme"

export interface StatusBadgeProps {
  /**
   * The status text to display
   */
  status: string
  /**
   * The variant determines the styling approach and color mapping
   * - 'type': For event types (AMERICANO, TOURNAMENT, etc.)
   * - 'status': For general statuses (joinable, full, joined, etc.)
   * - 'role': For user roles (Admin, Member, Moderator)
   * - 'indicator': For small status indicators (Online, Offline)
   */
  variant?: 'type' | 'status' | 'role' | 'indicator'
  /**
   * Size of the badge
   */
  size?: 'xs' | 'sm' | 'md'
  /**
   * Override container style
   */
  style?: ViewStyle
  /**
   * Override text style
   */
  textStyle?: TextStyle
}

export const StatusBadge: FC<StatusBadgeProps> = ({ 
  status, 
  variant = 'status', 
  size = 'sm',
  style,
  textStyle 
}) => {
  const { themed, theme } = useAppTheme()

  // Get configuration from service
  const badgeConfig = StatusBadgeService.getBadgeConfiguration(status, variant, size, theme)
  const displayText = StatusBadgeService.getDisplayText(status, variant)

  // Validate configuration
  const validation = StatusBadgeService.validateBadgeConfig(status, variant)
  if (!validation.isValid) {
    console.warn(`StatusBadge validation failed: ${validation.reason}`)
  }

  const getContainerStyle = (): ViewStyle => {
    return {
      ...badgeConfig.containerStyle,
      ...style,
    }
  }

  const getTextStyle = (): TextStyle => {
    return {
      ...(badgeConfig.textStyle as TextStyle),
      ...textStyle,
    }
  }

  return (
    <View style={getContainerStyle()}>
      <Text text={displayText} style={getTextStyle()} />
    </View>
  )
}

// Base container style
const $container: ThemedStyle<ViewStyle> = () => ({
  alignItems: 'center',
  justifyContent: 'center',
  alignSelf: 'flex-start',
})

// Base text style  
const $text: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  textAlign: 'center',
})

// Indicator style for small circular status indicators
const $indicatorContainer: ThemedStyle<ViewStyle> = () => ({
  width: 8,
  height: 8,
  borderRadius: 4,
}) 