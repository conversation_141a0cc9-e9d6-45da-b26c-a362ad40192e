import { FC, ReactNode, useCallback, useEffect, useRef } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { observer } from "mobx-react-lite"
import {
  BottomSheetModal,
  BottomSheetBackdrop,
  type BottomSheetBackdropProps,
} from "@gorhom/bottom-sheet"

import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

// Importing directly to avoid potential circular dependency through components index
import { Text } from "./Text"
import { Icon } from "./Icon"

/**
 * Generic, reusable bottom-sheet wrapper that takes care of:
 * – Mount / unmount lifecycle (visible flag + lazy present / dismiss)
 * – Backdrop rendering with sensible defaults
 * – Header with optional title + X button (calls onClose)
 * – Customisable snap points & dynamic sizing flag
 *
 * Usage:
 * ```tsx
 * <BaseBottomSheet visible={show} onClose={() => setShow(false)} title="Select Option">
 *   <BottomSheetScrollView>
 *     {...content}
 *   </BottomSheetScrollView>
 * </BaseBottomSheet>
 * ```
 */
export interface BaseBottomSheetProps {
  /** Controls whether the sheet is shown */
  visible: boolean
  /** Called when the sheet is fully dismissed (including swipe-down) */
  onClose: () => void
  /** Optional sheet title – if omitted, header is hidden */
  title?: string
  /** Snap points passed to `BottomSheetModal`. Defaults to `["50%"]`. */
  snapPoints?: Array<string | number>
  /** Disable dynamic sizing to prevent height flickering. Defaults to `false`. */
  enableDynamicSizing?: boolean
  /** Sheet content */
  children: ReactNode
}

export const BaseBottomSheet: FC<BaseBottomSheetProps> = observer(function BaseBottomSheet({
  visible,
  onClose,
  title,
  snapPoints = ["50%"],
  enableDynamicSizing = false,
  children,
}) {
  const { themed } = useAppTheme()
  const bottomSheetRef = useRef<BottomSheetModal>(null)

  // Lazy present / dismiss based on `visible` prop
  useEffect(() => {
    if (!visible) {
      bottomSheetRef.current?.dismiss()
      return
    }

    const id = requestAnimationFrame(() => {
      bottomSheetRef.current?.present()
    })

    return () => cancelAnimationFrame(id)
  }, [visible])

  // Backdrop with fade-in/out
  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />
    ),
    [],
  )

  // Custom close handler to always dismiss the modal first
  const handleClose = () => {
    bottomSheetRef.current?.dismiss()
    // Give time for modal to close before calling onClose
    setTimeout(() => {
      onClose()
    }, 200)
  }

  return (
    <BottomSheetModal
      ref={bottomSheetRef}
      index={0}
      snapPoints={snapPoints}
      onDismiss={onClose}
      backdropComponent={renderBackdrop}
      enablePanDownToClose
      keyboardBlurBehavior="restore"
      backgroundStyle={themed($background)}
      handleIndicatorStyle={themed($handleIndicator)}
      enableDynamicSizing={enableDynamicSizing}
    >
      {title ? (
        <View style={themed($header)}>
          <Text
            text={title}
            style={themed($headerTitle)}
            size="lg"
            weight="semiBold"
          />
          <TouchableOpacity onPress={handleClose} style={themed($closeButton)}>
            <Icon icon="x" size={24} />
          </TouchableOpacity>
        </View>
      ) : null}

      {children}
    </BottomSheetModal>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $background: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,
})

const $handleIndicator: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral400,
  height: 3,
  borderRadius: 2,
  marginTop: 4,
  marginBottom: 4,
  width: 48,
  alignSelf: "center",
})

const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.md,
  borderBottomWidth: 1,
  borderBottomColor: "#E5E7EB",
})

const $headerTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
})

const $closeButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.xs,
}) 