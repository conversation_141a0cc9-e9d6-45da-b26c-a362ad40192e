import { FC } from "react"
import { View, ViewStyle, TextStyle, Pressable, Keyboard, ScrollView } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export interface TabItem {
  key: string
  title: string
}

interface SimpleTabsProps {
  tabs: TabItem[]
  activeKey: string
  onTabChange: (key: string) => void
  scrollable?: boolean
  tabWidth?: number
}

export const SimpleTabs: FC<SimpleTabsProps> = ({ tabs, activeKey, onTabChange, scrollable = false, tabWidth = 100 }) => {
  const { themed } = useAppTheme()

  const TabPressable = (tab: TabItem) => (
    <Pressable
      key={tab.key}
      style={themed([$tab, scrollable ? { width: tabWidth } : { flex: 1 }, activeKey === tab.key && $activeTab])}
      onPress={() => {
        Keyboard.dismiss()
        onTabChange(tab.key)
      }}
    >
      <Text
        text={tab.title}
        style={themed([$tabText, activeKey === tab.key && $activeTabText])}
        size="sm"
      />
      {activeKey === tab.key && <View style={themed($activeIndicator)} />}
    </Pressable>
  )

  if (scrollable) {
    return (
      <View style={themed($scrollContainer)}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={themed($tabsScrollContent)}>
          {tabs.map(TabPressable)}
        </ScrollView>
      </View>
    )
  }

  return <View style={themed($tabsContainer)}>{tabs.map(TabPressable)}</View>
}

const $tabsContainer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.border,
})

const $tab: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  alignItems: "center",
  paddingVertical: spacing.xs,
  position: "relative",
})

const $activeTab: ThemedStyle<ViewStyle> = () => ({})

const $tabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $activeTabText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontWeight: "bold",
})

const $activeIndicator: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  height: 2,
  backgroundColor: colors.text,
})

const $scrollContainer: ThemedStyle<ViewStyle> = () => ({})

const $tabsScrollContent: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
}) 