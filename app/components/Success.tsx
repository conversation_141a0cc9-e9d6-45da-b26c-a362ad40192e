import { FC } from "react"
import { Modal, View, ViewStyle, TextStyle, TouchableOpacity, Image, ImageSourcePropType, ImageStyle } from "react-native"
import { observer } from "mobx-react-lite"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text } from "./Text"
import type { ThemedStyle } from "@/theme"
import { Icon, IconTypes } from "./Icon"

export interface SuccessButton {
  /** Button label */
  text?: string
  /** Callback when pressing the button */
  onPress: () => void
  /** Optional button style override */
  style?: ViewStyle
  /** Optional icon to display instead of text */
  icon?: IconTypes
}

export interface SuccessProps {
  /** Control modal visibility */
  visible: boolean
  /** Optional callback when user taps outside */
  onClose?: () => void
  /** Image displayed at the top of the card */
  image?: ImageSourcePropType
  /** Title text (e.g. "Yay!") */
  title?: string
  /** Body message */
  message?: string
  /** Action buttons */
  buttons: SuccessButton[]
}

const DEFAULT_IMAGE = require("../../assets/images/success.jpg")

export const Success: FC<SuccessProps> = observer(function Success({
  visible,
  onClose,
  image = DEFAULT_IMAGE,
  title = "Yay!",
  message = "You have successfully created your community.",
  buttons,
}) {
  const { themed, theme: { colors } } = useAppTheme()

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      presentationStyle="overFullScreen"
      onRequestClose={onClose}
    >
      <View style={themed($overlay)}>
        <TouchableOpacity style={themed($overlayTouchable)} activeOpacity={1} onPress={onClose} />

        <View style={themed($card)}>
          <Image source={image} style={themed($image)} resizeMode="contain" />

          <Text text={title} preset="heading" weight="bold" style={themed($title)} />
          <Text text={message} size="sm" style={themed($message)} />

          <View style={themed($buttonsRow)}>
            {buttons.map((btn, idx) => (
              <TouchableOpacity
                key={idx}
                onPress={btn.onPress}
                style={[
                  btn.icon && !btn.text ? themed($iconButton) : themed($button),
                  btn.style,
                ]}
                activeOpacity={0.8}
              >
                {btn.icon ? (
                  <Icon icon={btn.icon} size={28} color={colors.text} />
                ) : (
                  <Text text={btn.text} weight="medium" style={themed($buttonText)} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  )
})

const $overlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0,0,0,0.5)",
  justifyContent: "center",
  alignItems: "center",
})

// Extra view layered to catch outside taps
const $overlayTouchable: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
})

const $card: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: "85%",
  borderRadius: 16,
  paddingVertical: spacing.lg,
  paddingHorizontal: spacing.lg,
  alignItems: "center",
  backgroundColor: "#fff",
})

const $image: ThemedStyle<ImageStyle> = ({ spacing }) => ({
  width: 90,
  height: 90,
  marginBottom: spacing.md,
})

const $title: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.text,
  textTransform: "capitalize",
  marginBottom: spacing.xs,
})

const $message: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  textAlign: "center",
  lineHeight: 22,
  marginBottom: spacing.lg,
})

const $buttonsRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  width: "100%",
  gap: spacing.sm,
})

const $button: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  backgroundColor: colors.success,
  paddingVertical: spacing.md,
  borderRadius: 12,
  alignItems: "center",
})

const $buttonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 16,
})

const $iconButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  backgroundColor: "transparent",
  paddingVertical: spacing.md,
  borderRadius: 12,
  alignItems: "center",
}) 