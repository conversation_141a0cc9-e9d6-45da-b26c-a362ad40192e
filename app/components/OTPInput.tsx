import React, { FC, useRef } from "react"
import { View, TextInput, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text } from "@/components/Text"
import { useAppTheme } from "@/utils/useAppTheme"

interface OTPInputProps {
  value: string
  onChange: (v: string) => void
  length?: number
  isDisabled?: boolean
  autoFocus?: boolean
}

/**
 * A simple boxed OTP input component (pure UI).
 * Renders `length` boxes and stores the numeric code in `value`.
 */
export const OTPInput: FC<OTPInputProps> = ({
  value,
  onChange,
  length = 6,
  isDisabled = false,
  autoFocus = false,
}) => {
  const { themed } = useAppTheme()
  const inputRef = useRef<TextInput>(null)

  const handlePress = () => {
    inputRef.current?.focus()
  }

  const handleChangeText = (text: string) => {
    // only allow digits and max length
    const cleaned = text.replace(/[^0-9]/g, "").slice(0, length)
    onChange(cleaned)
  }

  return (
    <Pressable style={themed($container)} onPress={handlePress} disabled={isDisabled}>
      {Array.from({ length }).map((_, idx) => (
        <View key={`otp-${idx}`} style={themed([$box, idx === value.length ? $boxActive : undefined])}>
          <Text text={value[idx] ?? ""} preset="heading" style={themed($digit)} />
        </View>
      ))}
      {/* Hidden TextInput that captures the numeric input */}
      <TextInput
        ref={inputRef}
        value={value}
        onChangeText={handleChangeText}
        keyboardType="number-pad"
        maxLength={length}
        autoFocus={autoFocus}
        style={$hiddenInput}
        editable={!isDisabled}
      />
    </Pressable>
  )
}

// -----------------------------------------------------------------------------
// Styles
// -----------------------------------------------------------------------------
const $container: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
}

const BOX_SIZE = 48
const $box: ViewStyle = {
  width: BOX_SIZE,
  height: BOX_SIZE,
  borderRadius: 6,
  borderWidth: 1,
  borderColor: "#ccc",
  justifyContent: "center",
  alignItems: "center",
}

const $boxActive: ViewStyle = {
  borderColor: "#007AFF",
}

const $digit: TextStyle = {
  fontSize: 20,
  textAlign: "center",
}

const $hiddenInput: ViewStyle = {
  position: "absolute",
  opacity: 0,
  width: 1,
  height: 1,
} 