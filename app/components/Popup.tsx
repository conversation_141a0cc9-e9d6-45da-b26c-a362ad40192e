import React, { <PERSON> } from "react"
import { View, TouchableOpacity, Modal, ViewStyle, TextStyle, Platform } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { IconTypes } from "@/components/Icon"

export interface PopupMenuItem {
  icon: IconTypes
  title: string
  onPress: () => void
  isToggle?: boolean
  toggleValue?: boolean
  onToggleChange?: (value: boolean) => void
}

export interface PopupProps {
  visible: boolean
  onClose: () => void
  items: PopupMenuItem[]
  iconSize?: number
  textSize?: "xxs" | "xs" | "sm" | "md" | "lg" | "xl" | "xxl"
  paddingVertical?: number
  paddingHorizontal?: number
  borderRadius?: number
  position?: {
    top?: number
    right?: number
    left?: number
    bottom?: number
  }
}

export type PopupPosition = PopupProps["position"]

export const Popup: FC<PopupProps> = ({
  visible,
  onClose,
  items,
  iconSize = 16,
  textSize = "xs",
  paddingVertical = 12,
  paddingHorizontal = 16,
  borderRadius = 12,
  position = { top: 100, right: 20 },
  }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <TouchableOpacity style={themed($overlay)} activeOpacity={1} onPress={onClose}>
        <View style={[themed($container), { borderRadius }, position]}>
          <TouchableOpacity activeOpacity={1}>
            {items.map((item, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  themed($menuItem),
                  { paddingVertical, paddingHorizontal },
                  index === items.length - 1 && { borderBottomWidth: 0 },
                ]}
                onPress={() => {
                  if (item.isToggle && item.onToggleChange) {
                    item.onToggleChange(!item.toggleValue)
                  } else {
                    item.onPress()
                  }
                }}
              >
                <View style={themed($menuItemContent)}>
                  <Icon icon={item.icon} size={iconSize} color={colors.palette.neutral600} />
                  <Text text={item.title} style={themed($menuItemText)} size={textSize} />
                  {item.isToggle && (
                    <View style={themed($toggle)}>
                      <View
                        style={[
                          themed($toggleTrack),
                          item.toggleValue && themed($toggleTrackActive),
                        ]}
                      >
                        <View
                          style={[
                            themed($toggleThumb),
                            item.toggleValue && themed($toggleThumbActive),
                          ]}
                        />
                      </View>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  )
}

const $overlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
})

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral100,
  overflow: "hidden",
  elevation: 8,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 8,
  position: "absolute",
})

const $menuItem: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
})

const $menuItemContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  minWidth: 0,
})

const $menuItemText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral700,
  flexShrink: 1,
})

const $toggle: ThemedStyle<ViewStyle> = () => ({
  marginLeft: "auto",
})

const $toggleTrack: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 40,
  height: 22,
  borderRadius: 11,
  backgroundColor: colors.palette.neutral300,
  justifyContent: "center",
  paddingHorizontal: 2,
})

const $toggleTrackActive: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#34d399",
})

const $toggleThumb: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 18,
  height: 18,
  borderRadius: 9,
  backgroundColor: colors.palette.neutral100,
  alignSelf: "flex-start",
  elevation: 2,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.2,
  shadowRadius: 2,
})

const $toggleThumbActive: ThemedStyle<ViewStyle> = () => ({
  alignSelf: "flex-end",
})
