import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface ProgressBarProps {
  /**
   * Progress percentage (0 - 100). If `filledSegments` is provided, this is ignored.
   */
  percentage?: number
  /**
   * Height of the progress bar in pixels (defaults to 6)
   */
  height?: number
  /**
   * Optional override for the fill color. Defaults to the theme primary/secondary color.
   */
  barColor?: string
  /**
   * Number of segments to split the bar into. Defaults to 1 (continuous bar).
   */
  segments?: number
  /**
   * Gap between segments in pixels. Defaults to 4.
   */
  gap?: number
  /**
   * When using a segmented bar, the number of segments that should be fully filled (e.g. current step).
   * If provided, this takes precedence over `percentage` and ensures only whole segments are filled.
   */
  filledSegments?: number
}

/**
 * A simple themed progress bar with rounded edges that fills based on the provided percentage.
 *
 * @example
 * <ProgressBar percentage={67} />
 */
export const ProgressBar: FC<ProgressBarProps> = observer(function ProgressBar({
  percentage = 0,
  height = 6,
  barColor,
  segments = 1,
  gap = 4,
  filledSegments,
}) {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Ensure at least 1 segment
  const segmentCount = Math.max(1, Math.floor(segments))

  // Clamp filledSegments value
  const filledCount = filledSegments !== undefined ? Math.min(segmentCount, Math.max(0, filledSegments)) : 0

  // Clamp percentage between 0 and 100 to avoid overflow.
  const clampedPercentage = Math.min(100, Math.max(0, percentage))
  const segmentSize = 100 / segmentCount

  return (
    <View style={[themed($track), { height }]}>      
      {Array.from({ length: segmentCount }).map((_, index) => {
        let filledPercentage = 0

        if (filledSegments !== undefined) {
          filledPercentage = index < filledCount ? 100 : 0
        } else {
          const segmentStart = index * segmentSize
          const segmentEnd = segmentStart + segmentSize

          // Determine how much of this segment should be filled (0 - 100)
          filledPercentage = clampedPercentage <= segmentStart
            ? 0
            : clampedPercentage >= segmentEnd
            ? 100
            : ((clampedPercentage - segmentStart) / segmentSize) * 100
        }

        return (
          <View
            key={`segment-${index}`}
            style={[themed($segmentTrack), { height, marginRight: index !== segmentCount - 1 ? gap : 0 }]}
          >
            <View
              style={[
                themed($segmentFill),
                {
                  width: `${filledPercentage}%`,
                  height,
                  backgroundColor: barColor ?? colors.palette.primary500,
                },
              ]}
            />
          </View>
        )
      })}
    </View>
  )
})

const $track: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  width: "100%",
})

const $segmentTrack: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flex: 1,
  backgroundColor: colors.palette.neutral300,
  borderRadius: 999,
  overflow: "hidden",
})

const $segmentFill: ThemedStyle<ViewStyle> = () => ({}) 