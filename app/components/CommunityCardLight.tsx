import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { Avatar } from "./Avatar"
import { StatusBadge } from "./StatusBadge"
import { CommunityService } from "@/services"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export interface UserCommunity {
  id: string
  name: string
  members: number
  role: "Member" | "Admin"
  color: string
}

export interface CommunityCardLightProps {
  community: UserCommunity
  onPress: (community: UserCommunity) => void
  showStatusBadge?: boolean
}

export const CommunityCardLight: FC<CommunityCardLightProps> = ({ community, onPress, showStatusBadge = true }) => {
  const { themed } = useAppTheme()
  
  // ✅ Use service for all business logic
  // const memberCountText = CommunityService.formatMemberCountText(community.members)
  const roleConfig = CommunityService.getRoleDisplayConfig(community.role)

  return (
    <TouchableOpacity style={themed($card)} onPress={() => onPress(community)}>
      <View style={themed($cardContent)}>
        <Avatar
          name={community.name}
          size="md"
          style={themed($avatar)}
          textStyle={themed($avatarText)}
          textSize="sm"
          textWeight="bold"
        />
        <View style={themed($cardDetails)}>
          <Text text={community.name} style={themed($cardTitle)} size="md" weight="medium" />
          <View style={themed($memberRow)}>
            <Icon icon="community" size={14} color="#888" />
            <Text text={String(community.members)} style={themed($cardSubtitle)} size="sm" numberOfLines={1} ellipsizeMode="tail" />
          </View>
        </View>
        {showStatusBadge && (
          <View style={themed($cardAction)}>
            <StatusBadge 
              status={roleConfig.displayText} 
              variant={roleConfig.variant} 
              size="sm"
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}

const $card: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.sm,
  padding: spacing.md,
  minHeight: 80,
  maxHeight: 80,
  height: 80,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 2,
  justifyContent: "center",
})

const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
})

const $avatar: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#4a9d8e",
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.xxs,
})

const $avatarText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
})

const $cardDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $cardTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontSize: 16,
  fontWeight: "600",
})

const $cardSubtitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $cardAction: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
})

const $memberRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xxs,
  marginTop: spacing.xxs,
})

const $memberIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 2,
})
