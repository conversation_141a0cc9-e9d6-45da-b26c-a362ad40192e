import { FC } from "react"
import { observer } from "mobx-react-lite"
import { View, ViewStyle, TouchableOpacity, StyleProp, TextStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Icon, IconTypes } from "./Icon"
import { Text } from "./Text"

interface FormatCardProps {
  /** Title displayed below the icon */
  title: string
  /** Name of the icon to display – must exist in the icon registry */
  icon: IconTypes
  /** Size (both width and height) of the square card. Default: 140 */
  size?: number
  /** Border radius of the card. Default: 16 */
  rounded?: number
  /** Size of the icon. Default: 56 */
  iconSize?: number
  /** Override background color */
  backgroundColor?: string
  /** Additional style overrides for the card container */
  containerStyle?: StyleProp<ViewStyle>
  /** Optional press handler – card becomes pressable if provided */
  onPress?: () => void
  /** If true, card is highlighted as selected */
  selected?: boolean
  /** Optional background when selected; defaults to theme primary200 */
  selectedBackgroundColor?: string
  /**
   * Optional handler for the info (i) button displayed at the top-left corner.
   * If provided, the (i) button will be rendered and this callback will be
   * invoked when the user taps it.
   */
  onInfoPress?: () => void
}

/**
 * A reusable card component used to display an event format option.
 * It shows an icon centered in the card with a title beneath it.
 */
export const FormatCard: FC<FormatCardProps> = observer(function FormatCard(props) {
  const {
    title,
    icon,
    size = 140,
    rounded = 16,
    iconSize = 56,
    backgroundColor,
    containerStyle,
    onPress,
    selected = false,
    selectedBackgroundColor,
    onInfoPress,
  } = props

  const { themed, theme } = useAppTheme()

  const baseStyle = themed($container)
  const dynamicStyle: ViewStyle = {
    width: size,
    height: size,
    borderRadius: rounded,
    ...(backgroundColor ? { backgroundColor } : {}),
  }

  // Selected styles
  const $selectedStyle: ViewStyle = selected
    ? {
        backgroundColor: selectedBackgroundColor ?? theme.colors.palette.success200,
        shadowColor: theme.colors.palette.neutral800,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 4,
      }
    : {}

  const $combinedStyles: StyleProp<ViewStyle> = [baseStyle, dynamicStyle, $selectedStyle, containerStyle]

  const Container = onPress ? TouchableOpacity : View

  return (
    <Container
      style={$combinedStyles}
      activeOpacity={0.8}
      accessibilityRole={onPress ? "button" : undefined}
      onPress={onPress}
    >
      {/* Info button */}
      {onInfoPress && (
        <TouchableOpacity
          onPress={() => {
            onInfoPress()
          }}
          style={themed($infoButton)}
          accessibilityRole="button"
          hitSlop={8}
        >
          <Icon icon="info" size={16} />
        </TouchableOpacity>
      )}
      <Icon icon={icon} size={iconSize} />
      <Text style={themed($title)} text={title} weight="medium" size="sm" />
    </Container>
  )
})

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.sm,
})

const $title: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginTop: spacing.xs,
  textAlign: "center",
})

const $infoButton: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  position: "absolute",
  top: spacing.xs,
  left: spacing.xs,
  width: 24,
  height: 24,
  borderRadius: 12,
  backgroundColor: colors.background,
  justifyContent: "center",
  alignItems: "center",
  // iOS shadow / Android elevation for visibility
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.15,
  shadowRadius: 1,
  elevation: 2,
}) 