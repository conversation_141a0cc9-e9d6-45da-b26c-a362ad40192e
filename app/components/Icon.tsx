import {
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewProps,
  ViewStyle,
} from "react-native"
import React, { memo } from "react"
import { VectorIcon } from "./VectorIcon"

export type IconTypes = keyof typeof iconRegistry

type BaseIconProps = {
  /**
   * The name of the icon
   */
  icon: IconTypes

  /**
   * An optional tint color for the icon
   */
  color?: string

  /**
   * An optional size for the icon. If not provided, the icon will be sized to the icon's resolution.
   */
  size?: number

  /**
   * Style overrides for the icon container
   */
  containerStyle?: StyleProp<ViewStyle>
}

type PressableIconProps = Omit<TouchableOpacityProps, "style"> & BaseIconProps
type IconProps = Omit<ViewProps, "style"> & BaseIconProps

/**
 * A component to render a registered icon using vector icons.
 * It is wrapped in a <TouchableOpacity />
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Icon/}
 * @param {PressableIconProps} props - The props for the `PressableIcon` component.
 * @returns {JSX.Element} The rendered `PressableIcon` component.
 */
export const PressableIcon = memo(function PressableIcon(props: PressableIconProps) {
  const {
    icon,
    color,
    size = 24,
    containerStyle: $containerStyleOverride,
    ...pressableProps
  } = props

  const iconConfig = iconRegistry[icon]

  return (
    <TouchableOpacity {...pressableProps} style={$containerStyleOverride}>
      <VectorIcon name={iconConfig.name} library={iconConfig.library} size={size} color={color} />
    </TouchableOpacity>
  )
})

/**
 * A component to render a registered icon using vector icons.
 * It is wrapped in a <View />, use `PressableIcon` if you want to react to input
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Icon/}
 * @param {IconProps} props - The props for the `Icon` component.
 * @returns {JSX.Element} The rendered `Icon` component.
 */
export const Icon = memo(function Icon(props: IconProps) {
  const { icon, color, size = 24, containerStyle: $containerStyleOverride, ...viewProps } = props

  const iconConfig = iconRegistry[icon]

  return (
    <View {...viewProps} style={$containerStyleOverride}>
      <VectorIcon name={iconConfig.name} library={iconConfig.library} size={size} color={color} />
    </View>
  )
})

// Vector icon registry mapping emoji names to actual vector icons
export const iconRegistry = {
  back: { name: "arrow-back", library: "material" as const },
  bell: { name: "notifications", library: "material" as const },
  inbox: { name: "inbox", library: "fontawesome" as const },
  caretLeft: { name: "chevron-left", library: "feather" as const },
  caretRight: { name: "chevron-right", library: "feather" as const },
  check: { name: "check", library: "feather" as const },
  clap: { name: "thumbs-up", library: "feather" as const },
  info: { name: "info", library: "feather" as const },
  community: { name: "users", library: "feather" as const },
  components: { name: "grid", library: "feather" as const },
  debug: { name: "bug", library: "material" as const },
  github: { name: "github", library: "feather" as const },
  heart: { name: "heart", library: "feather" as const },
  hidden: { name: "eye-off", library: "feather" as const },
  ladybug: { name: "bug", library: "material" as const },
  lock: { name: "lock", library: "feather" as const },
  menu: { name: "menu", library: "feather" as const },
  more: { name: "more-vert", library: "material" as const },
  pin: { name: "map-pin", library: "feather" as const },
  podcast: { name: "mic", library: "feather" as const },
  settings: { name: "settings", library: "feather" as const },
  slack: { name: "message-circle", library: "feather" as const },
  view: { name: "eye", library: "feather" as const },
  x: { name: "x", library: "feather" as const },
  filter: { name: "filter", library: "feather" as const },
  search: { name: "search", library: "material" as const },

  // Padel-specific icons
  trophy: { name: "emoji-events", library: "material" as const },
  star: { name: "star", library: "material" as const },
  calendar: { name: "event", library: "material" as const },
  location: { name: "location-on", library: "material" as const },
  sports: { name: "sports-tennis", library: "material" as const },
  people: { name: "group", library: "material" as const },
  trending: { name: "trending-up", library: "material" as const },
  award: { name: "stars", library: "material" as const },
  sunrise: { name: "wb-sunny", library: "material" as const },
  crown: { name: "star-rate", library: "material" as const },
  handshake: { name: "handshake", library: "material" as const },
  graduation: { name: "school", library: "material" as const },

  // Navigation icons
  events: { name: "home", library: "material" as const },
  user: { name: "person", library: "material" as const },
  share: { name: "share", library: "feather" as const },
  message: { name: "message-circle", library: "feather" as const },
  feed: { name: "rss-feed", library: "material" as const },
  leaderboard: { name: "leaderboard", library: "material" as const },
  // Time and currency icons
  clock: { name: "access-time", library: "material" as const },
  aed: { name: "attach-money", library: "material" as const },
  
  // Action icons
  add: { name: "add", library: "material" as const },
  home: { name: "home", library: "material" as const },
  logout: { name: "logout", library: "material" as const },
}
