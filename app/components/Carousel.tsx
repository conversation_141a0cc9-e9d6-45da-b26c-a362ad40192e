import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ScrollView, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components/Text"
import { Icon } from "@/components/Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import { CarouselService, CarouselButton } from "@/services"
import type { ThemedStyle } from "@/theme"

interface CarouselProps {
  onExploreCommunities: () => void
  onExploreEvents: () => void
  onExploreFeed: () => void
}

export const Carousel: FC<CarouselProps> = observer(function Carousel({
  onExploreCommunities,
  onExploreEvents,
  onExploreFeed,
}) {
  const { themed } = useAppTheme()

  const carouselButtons = CarouselService.getCarouselButtons({
    onExploreCommunities,
    onExploreEvents,
    onExploreFeed,
  })

  const renderCarouselItem = (item: CarouselButton) => (
    <TouchableOpacity
      key={item.id}
      style={[themed($carouselItem), { backgroundColor: item.backgroundColor }]}
      onPress={item.onPress}
    >
      <Icon icon={item.icon as any} size={32} color="#ffffff" />
      <View style={themed($carouselTextContainer)}>
        <Text text={item.title} style={themed($carouselTitle)} size="sm" weight="medium" />
        <Text text={item.subtitle} style={themed($carouselSubtitle)} size="xs" />
      </View>
    </TouchableOpacity>
  )

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={themed($carouselContainer)}
      style={themed($carousel)}
    >
      {carouselButtons.map(renderCarouselItem)}
    </ScrollView>
  )
})

const $carousel: ThemedStyle<ViewStyle> = () => ({
  flexGrow: 0,
})

const $carouselContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg,
  gap: spacing.md,
})

const $carouselItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 120,
  height: 120,
  borderRadius: spacing.md,
  padding: spacing.md,
  justifyContent: "space-between",
  alignItems: "flex-start",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})

const $carouselTextContainer: ThemedStyle<ViewStyle> = () => ({
  alignSelf: "stretch",
})

const $carouselTitle: ThemedStyle<TextStyle> = () => ({
  color: "#ffffff",
})

const $carouselSubtitle: ThemedStyle<TextStyle> = () => ({
  color: "#ffffff",
  opacity: 0.9,
}) 