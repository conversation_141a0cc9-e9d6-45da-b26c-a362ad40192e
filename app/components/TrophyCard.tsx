import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

export interface Trophy {
  id: number
  title: string
  description: string
  date: string
  icon: string
}

export interface TrophyCardProps {
  trophy: Trophy
  onPress?: (trophy: Trophy) => void
}

export const TrophyCard: FC<TrophyCardProps> = ({ trophy, onPress }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  return (
    <View style={themed($card)}>
      <View style={themed($cardContent)}>
        <View style={themed($trophyIcon)}>
          <Text text={trophy.icon} size="xl" />
        </View>
        <View style={themed($cardDetails)}>
          <Text text={trophy.title} style={themed($cardTitle)} size="sm" weight="medium" />
          <Text text={trophy.description} style={themed($cardSubtitle)} size="xs" />
          <Text text={trophy.date} style={themed($trophyDate)} size="xs" />
        </View>
        <View style={themed($cardAction)}>
          <Icon icon="heart" size={20} color="#fbbf24" />
        </View>
      </View>
    </View>
  )
}

const $card: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.sm,
  padding: spacing.md,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 2,
})

const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md,
})

const $trophyIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 48,
  height: 48,
  justifyContent: "center",
  alignItems: "center",
})

const $cardDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $cardTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
})

const $cardSubtitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $trophyDate: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginTop: spacing.xs,
})

const $cardAction: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
})
