import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { observer } from "mobx-react-lite"

import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import { Text } from "@/components"
import type { LeaderboardRow } from "@/services/LeaderboardService"

export interface LeaderboardProps {
  data: LeaderboardRow[]
}

/**
 * Displays a simple leaderboard table.
 */
export const Leaderboard: FC<LeaderboardProps> = observer(function Leaderboard({ data }) {
  const { themed } = useAppTheme()
  const insets = useSafeAreaInsets()

  const renderHeader = () => (
    <View style={themed($headerRow)}>
      <Text text="#" style={themed([$headerCell, $cellBorder, $indexCell])} weight="semiBold" size="sm" />
      <Text text="Player" style={[themed([$headerCell, $cellBorder]), { flex: 2, textAlign: "left" }]} weight="semiBold" size="sm" />
      <Text text="W-T-L" style={themed([$headerCell, $cellBorder])} weight="semiBold" size="sm" />
      <Text text="Diff" style={themed([$headerCell, $cellBorder])} weight="semiBold" size="sm" />
      <Text text="Points" style={themed([$headerCell, $pointsColumn])} weight="semiBold" size="sm" />
    </View>
  )

  const renderItem = (item: LeaderboardRow, index: number) => (
    <View key={item.player} style={themed($row)}>
      <Text text={`${index + 1}`} style={themed([$cell, $cellBorder, $indexCell])} size="sm" />
      <Text text={item.player} style={[themed([$cell, $cellBorder]), { flex: 2, textAlign: "left" }]} size="sm" />
      <Text text={`${item.wins}-${item.ties}-${item.losses}`} style={themed([$cell, $cellBorder])} size="sm" />
      <Text text={`${item.diff}`} style={themed([$cell, $cellBorder])} size="sm" />
      <Text text={`${item.points}`} style={themed([$cell, $pointsText])} size="sm" />
    </View>
  )

  const renderFooter = () => (
    <View style={{ height: 32 + insets.bottom }} />
  )

  return (
    <View style={themed($container)}>
      {renderHeader()}
      {data.map((item, index) => renderItem(item, index))}
      {renderFooter()}
    </View>
  )
})

/* ──────────────────────────────────────────────────────────
 * Styles
 * ─────────────────────────────────────────────────────────*/
const $container: ThemedStyle<ViewStyle> = ({ spacing, colors }) => ({
  flex: 1, // Ensure Leaderboard fills available vertical space
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
  borderRadius: spacing.sm,
  overflow: "hidden",
})

const baseRow: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
})

const $headerRow: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.palette.neutral800,
})

const $row: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xs,
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
})

const $headerCell: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  flex: 1,
  textAlign: "center",
})

const $cell: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  flex: 1,
  textAlign: "center",
})

// Narrow width for index (#) column
const $indexCell: ThemedStyle<TextStyle> = () => ({
  width: 28,
  textAlign: "center",
})

// Shared background color for entire Points column (header + rows)
const $pointsColumn: ThemedStyle<ViewStyle | TextStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral800,
  color: colors.palette.neutral100,
  paddingHorizontal: spacing.xs,
})

// Text style inside Points column
const $pointsText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral900,
  textAlign: "center",
})

const $cellBorder: ThemedStyle<ViewStyle | TextStyle> = ({ colors }) => ({
  borderRightWidth: 1,
  borderRightColor: colors.palette.neutral200,
})


