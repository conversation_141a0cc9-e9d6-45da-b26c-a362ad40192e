import { FC } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "./Text"
import { Icon } from "./Icon"
import { Button } from "./Button"
import { StatusBadge } from "./StatusBadge"
import { useAppTheme } from "@/utils/useAppTheme"
import { EventService } from "@/services"
import type { ThemedStyle } from "@/theme"

export interface EventLight {
  id: number
  type: string
  title: string
  date: string
  location: string
  price: string
  participants: number
  maxParticipants: number
  status?: string
}

export interface EventCardLightProps {
  event: EventLight
  onJoin?: (eventId: number) => void
  onPress?: (event: EventLight) => void
}

export const EventCardLight: FC<EventCardLightProps> = ({ event, onJoin, onPress }) => {
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Convert EventLight to Event format for service compatibility
  const eventForService = {
    ...event,
    day: event.date, // Use date as day since EventLight doesn't have separate day field
    status: event.status || 'joinable',
    maxParticipants: event.maxParticipants,
  }

  // Get button configuration from service
  const joinButtonConfig = EventService.getJoinButtonConfig(eventForService)
  
  // Get formatted data from service
  const participantsText = EventService.formatParticipantsText(eventForService)
  const priceText = EventService.formatPriceText(eventForService)

  const handleJoin = () => {
    // Validate join action through service
    const validation = EventService.validateJoinAction(eventForService)
    
    if (validation.canJoin && onJoin) {
      onJoin(event.id)
    }
    // Note: validation.reason could be used for user feedback if needed
  }

  const handlePress = () => {
    if (onPress) {
      onPress(event)
    }
  }

  const getJoinButtonStyles = () => {
    if (joinButtonConfig.variant === "disabled") {
      return {
        backgroundColor: colors.palette.neutral400,
      }
    }
    return {
      backgroundColor: colors.palette.success500,
    }
  }

  return (
    <TouchableOpacity style={themed($card)} onPress={handlePress}>
      <View style={themed($cardHeader)}>
        <StatusBadge 
          status={event.type} 
          variant="type" 
          size="xs"
          style={themed($statusBadgeOverride)}
          textStyle={themed($statusTextOverride)}
        />
        <Text text={event.date} style={themed($dateText)} size="xs" />
      </View>

      <View style={themed($cardContent)}>
        <View style={themed($cardDetails)}>
          <Text text={event.title} style={themed($cardTitle)} size="sm" weight="medium" />
          <View style={themed($cardInfo)}>
            <View style={themed($infoItem)}>
              <Icon icon="pin" size={12} color={colors.textDim} />
              <Text text={event.location} style={themed($infoText)} size="xs" />
            </View>
            <View style={themed($infoItem)}>
              <Text text="AED" style={themed($currencyText)} size="xxs" />
              <Text text={priceText} style={themed($priceText)} size="xs" />
            </View>
            <View style={themed($infoItem)}>
              <Icon icon="community" size={12} color={colors.textDim} />
              <Text
                text={participantsText}
                style={themed($infoText)}
                size="xs"
              />
            </View>
          </View>
        </View>

        <View style={themed($cardAction)}>
          <Button
            text={joinButtonConfig.text}
            preset="filled"
            style={[themed($joinButton), getJoinButtonStyles()]}
            textStyle={themed($joinButtonText)}
            disabled={joinButtonConfig.disabled}
            onPress={handleJoin}
          />
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $card: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: spacing.sm,
  padding: spacing.sm,
  borderWidth: 1,
  borderColor: colors.palette.neutral400,
  marginTop: spacing.xs,
})

const $cardHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.xs,
})

// Custom style override to match original EventCardLight sizing exactly
const $statusBadgeOverride: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.xs,    // 8px (original)
  paddingVertical: 2,               // 2px (original)
  borderRadius: 8,                  // 8px (original)
})

const $statusTextOverride: ThemedStyle<TextStyle> = () => ({
  fontSize: 12,                     // Match original size="xxs"
  lineHeight: 18,                   // Match original size="xxs"
})

const $dateText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
})

const $cardDetails: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $cardTitle: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  marginBottom: 4,
})

const $cardInfo: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm,
  flexWrap: "nowrap",
})

const $infoItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: 2,
})

const $infoText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})

const $currencyText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  backgroundColor: colors.palette.neutral200,
  paddingHorizontal: 4,
  paddingVertical: 1,
  borderRadius: 3,
  fontSize: 9,
})

const $priceText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
  fontWeight: "600",
})

const $joinButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm,
  paddingTop: 3,
  paddingBottom: 6, // Slightly increased bottom padding for better visual alignment
  borderRadius: 6,
  minWidth: 45,
  minHeight: 28,
  justifyContent: "center",
  alignItems: "center",
})

const $joinButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 12,
  fontWeight: "600",
})

// New wrapper for action button to tweak vertical alignment
const $cardAction: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "flex-start",
  marginBottom: 12,
})
