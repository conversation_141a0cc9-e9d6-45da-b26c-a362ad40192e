import { observer } from "mobx-react-lite"
import React, { FC } from "react"
import { View, ViewStyle, TextStyle, StyleProp } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text } from "@/components"
import { ThemedStyle } from "@/theme"

interface ScoreCardProps {
  /**
   * A tuple of two player names representing the first team.
   * Example: ["Player 1", "Player 2"].
   */
  team1Players: [string, string]
  /**
   * A tuple of two player names representing the second team.
   * Example: ["Player 3", "Player 4"].
   */
  team2Players: [string, string]
  /**
   * Score for team 1.
   */
  team1Score: number
  /**
   * Score for team 2.
   */
  team2Score: number
  /**
   * Court name displayed in the pill at the bottom of the card.
   */
  courtName: string
  /**
   * Optional style override for the root container.
   */
  style?: StyleProp<ViewStyle>
}

export const ScoreCard: FC<ScoreCardProps> = observer(function ScoreCard(props) {
  const { team1Players, team2Players, team1Score, team2Score, courtName, style } = props
  const { themed } = useAppTheme()

  return (
    <View style={[themed($container), style]}>
      {/* Scores */}
      <View style={themed($scoresRow)}>
        <View style={themed($scoreBox)}>
          <Text preset="heading" text={team1Score.toString()} style={themed($scoreText)} />
        </View>
        <Text preset="bold" text="vs" style={themed($vsText)} />
        <View style={themed($scoreBox)}>
          <Text preset="heading" text={team2Score.toString()} style={themed($scoreText)} />
        </View>
      </View>

      {/* Player Names */}
      <View style={themed($playersRow)}>
        <Text preset="subheading" text={team1Players[0]} style={themed($playerName)} />
        <Text preset="subheading" text={team2Players[0]} style={themed([$playerName, $playerNameRight])} />
      </View>
      <View style={themed($playersRow)}>
        <Text preset="subheading" text={team1Players[1]} style={themed($playerName)} />
        <Text preset="subheading" text={team2Players[1]} style={themed([$playerName, $playerNameRight])} />
      </View>

      {/* Court Tag */}
      <View style={themed($courtTag)}>
        <Text preset="formLabel" text={courtName} style={themed($courtTagText)} />
      </View>
    </View>
  )
})

// -- Styles ----------------------------------------------------

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  borderColor: colors.border,
  borderWidth: 2,
  borderRadius: spacing.md,
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
  paddingBottom: spacing.xl,
  alignItems: "center",
  justifyContent: "center",
  overflow: "visible",
})

const $scoresRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  position: "absolute",
  top: -spacing.xl,
})

const $scoreBox: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral900,
  borderRadius: spacing.lg,
  minWidth: 70,
  minHeight: 70,
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: spacing.sm,
})

const $scoreText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral100,
  fontSize: 36,
})

const $vsText: ThemedStyle<TextStyle> = ({ spacing, colors }) => ({
  marginTop: spacing.md,
  marginHorizontal: spacing.md,
  color: colors.text,
})

const $playersRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: "100%",
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.xs,
})

const $playerName: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  fontSize: 16,
})

const $playerNameRight: ThemedStyle<TextStyle> = () => ({
  textAlign: "right",
})

const $courtTag: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.border,
  borderRadius: spacing.xl,
  paddingVertical: spacing.xxs,
  paddingHorizontal: spacing.lg,
  position: "absolute",
  bottom: -spacing.md,
  alignSelf: "center",
})

const $courtTagText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,
  textAlign: "center",
}) 